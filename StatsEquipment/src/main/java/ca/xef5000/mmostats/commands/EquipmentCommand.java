package ca.xef5000.mmostats.commands;

import ca.xef5000.mmostats.StatsEquipment;
import ca.xef5000.mmostats.gui.EquipmentGUIManager;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import javax.annotation.Nonnull;

public class EquipmentCommand implements CommandExecutor {
    private final StatsEquipment plugin;
    private final EquipmentGUIManager guiManager;

    public EquipmentCommand(StatsEquipment plugin, EquipmentGUIManager guiManager) {
        this.plugin = plugin;
        this.guiManager = guiManager;
    }

    @Override
    public boolean onCommand(@Nonnull CommandSender sender, @Nonnull Command command, @Nonnull String label, @Nonnull String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(plugin.getConfigManager().getPluginPrefix() + "Only players can use this command.");
            return true;
        }

        if (args.length == 0) {
            Player player = (Player) sender;
            if (!player.hasPermission("statsequipment.use")) {
                player.sendMessage(plugin.getConfigManager().getPluginPrefix() + "You don't have permission.");
                return true;
            }

            guiManager.openEquipmentGUI(player);
            return true;
        } else if (args.length == 1) {
            Player target = plugin.getServer().getPlayerExact(args[0]);
            if (target == null) {
                sender.sendMessage(plugin.getConfigManager().getPluginPrefix() + "Player not found.");
                return true;
            }
            if (!sender.hasPermission("statsequipment.use.other")) {
                sender.sendMessage(plugin.getConfigManager().getPluginPrefix() + "You don't have permission.");
                return true;
            }
            guiManager.openEquipmentGUI((Player) sender, target);
            return true;
        }


        return true;
    }
}
