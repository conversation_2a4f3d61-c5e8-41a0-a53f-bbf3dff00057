package ca.xef5000.mmostats.manager;

import org.bukkit.inventory.ItemStack;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class PlayerEquipmentData {
    private final UUID playerUUID;
    private final Map<Integer, ItemStack> equippedItems = new HashMap<>(); // Slot -> Item

    public PlayerEquipmentData(UUID playerUUID) {
        this.playerUUID = playerUUID;
    }

    public UUID getPlayerUUID() {
        return playerUUID;
    }

    public Map<Integer, ItemStack> getEquippedItems() {
        return new HashMap<>(equippedItems); // Return a copy to prevent direct modification
    }

    public ItemStack getEquippedItem(int slot) {
        return equippedItems.get(slot);
    }

    public void setEquippedItem(int slot, ItemStack item) {
        if (item == null || item.getType().isAir()) {
            equippedItems.remove(slot);
        } else {
            equippedItems.put(slot, item.clone()); // Store a clone
        }
    }

    public void clearSlot(int slot) {
        equippedItems.remove(slot);
    }
}
