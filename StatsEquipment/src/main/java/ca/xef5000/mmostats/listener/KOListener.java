package ca.xef5000.mmostats.listener;

import ca.xef5000.mmostats.StatsEquipment;
import ca.xef5000.mmostats.StatsItemsAPI;
import ca.xef5000.mmostats.config.ConfigManager;
import ca.xef5000.mmostats.gui.EquipmentGUIManager;
import ca.xef5000.mmostats.manager.PlayerEquipmentManager;
import me.rainstxrm.downbutnotout.CustomEvents.KOEvent;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.inventory.ItemStack;

import java.util.Map;

public class KOListener implements Listener {

    private final PlayerEquipmentManager playerEquipmentManager;
    private final ConfigManager configManager;
    private final StatsItemsAPI statsItemsAPI;
    private final EquipmentGUIManager guiManager;

    public KOListener(StatsEquipment plugin, EquipmentGUIManager guiManager) {
        this.playerEquipmentManager = plugin.getPlayerEquipmentManager();
        this.configManager = plugin.getConfigManager();
        this.guiManager = guiManager;
        org.bukkit.plugin.RegisteredServiceProvider<StatsItemsAPI> provider = plugin.getServer().getServicesManager().getRegistration(StatsItemsAPI.class);
        if (provider != null) {
            this.statsItemsAPI = provider.getProvider();
        } else {
            plugin.getLogger().severe("Failed to get StatsItemsAPI! Equipment slot item validation will not work.");
            this.statsItemsAPI = null; // Handle gracefully
        }
    }

    @EventHandler
    public void onPlayerKO(KOEvent event) {
        if (!configManager.isDBNODeathClearEnabled()) return;
        Map<Integer, ItemStack> customEquipment = playerEquipmentManager.getEquippedItems(event.getPlayer().getUniqueId());

        for (Map.Entry<Integer, ItemStack> entry : customEquipment.entrySet()) {
            String itemKeyFromAPI = statsItemsAPI.getItemDefinitionKey(entry.getValue());
            if (itemKeyFromAPI != null && configManager.getDBNODeathClearItems().contains(itemKeyFromAPI)) {
                playerEquipmentManager.setEquippedItem(event.getPlayer().getUniqueId(), entry.getKey(), null);
                guiManager.refreshAllGUIs(event.getPlayer().getUniqueId());
            }
        }
    }
}
