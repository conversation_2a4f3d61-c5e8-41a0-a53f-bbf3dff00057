package ca.xef5000.mmostats.gui;

import ca.xef5000.mmostats.StatsEquipment;
import ca.xef5000.mmostats.StatsItemsAPI;
import ca.xef5000.mmostats.config.MenuConfig;
import ca.xef5000.mmostats.manager.PlayerEquipmentData;
import ca.xef5000.mmostats.manager.PlayerEquipmentManager;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryAction;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class EquipmentGUIListener implements Listener {
    private final StatsEquipment plugin;
    private final EquipmentGUIManager guiManager;
    private final PlayerEquipmentManager playerEquipmentManager;
    private final MenuConfig menuConfig;
    private final StatsItemsAPI statsItemsAPI; // Get this from StatsItems plugin

    public EquipmentGUIListener(StatsEquipment plugin, EquipmentGUIManager guiManager,
                                PlayerEquipmentManager playerEquipmentManager, MenuConfig menuConfig) {
        this.plugin = plugin;
        this.guiManager = guiManager;
        this.playerEquipmentManager = playerEquipmentManager;
        this.menuConfig = menuConfig;

        // Obtain StatsItems' ItemStatManager (CRUCIAL)
        org.bukkit.plugin.RegisteredServiceProvider<StatsItemsAPI> provider = plugin.getServer().getServicesManager().getRegistration(StatsItemsAPI.class);
        if (provider != null) {
            this.statsItemsAPI = provider.getProvider();
        } else {
            plugin.getLogger().severe("Failed to get StatsItemsAPI! Equipment slot item validation will not work.");
            this.statsItemsAPI = null; // Handle gracefully
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();
        Inventory clickedInventory = event.getClickedInventory();
        Inventory topInventory = event.getView().getTopInventory();

        if (!(topInventory.getHolder() instanceof EquipmentGUIHolder)) return;
        EquipmentGUIHolder holder = (EquipmentGUIHolder) topInventory.getHolder();
        //if (!holder.getPlayerUUID().equals(player.getUniqueId())) return; // Should not happen

        //event.setCancelled(true); // Default to cancel, then selectively allow

        if (clickedInventory == null) return; // Clicked outside any inventory

        int rawSlot = event.getRawSlot();
        ItemStack cursorItem = event.getCursor(); // Item on cursor
        ItemStack currentItem = event.getCurrentItem(); // Item in the clicked slot

        // --- Click within the Custom GUI ---
        if (rawSlot < topInventory.getSize()) {
            event.setCancelled(true); // Default to cancelling actions in the top GUI, then allow specific ones.
            int slotInGui = event.getSlot();
            MenuConfig.SlotDefinition slotDef = menuConfig.getSlotDefinition(slotInGui);

            if (slotDef != null && slotDef.isEquipmentSlot()) {
                //plugin.getLogger().info("[DEBUG] Clicked GUI Equipment Slot: " + slotInGui + ", Action: " + event.getAction() + ", Cursor: " + cursorItem + ", CurrentItemInSlot: " + currentItem);

                // --- Case 1: Placing an item from cursor into an equipment slot ---
                if (cursorItem != null && !cursorItem.getType().isAir()) {
                    // Actions: PLACE_ALL, PLACE_SOME, PLACE_ONE (when clicking an empty or placeholder slot with an item on cursor)
                    // Action: SWAP_WITH_CURSOR (when clicking a filled slot with an item on cursor)
                    if (event.getAction() == InventoryAction.PLACE_ALL ||
                            event.getAction() == InventoryAction.PLACE_SOME ||
                            event.getAction() == InventoryAction.PLACE_ONE ||
                            event.getAction() == InventoryAction.SWAP_WITH_CURSOR) {

                        //plugin.getLogger().info("[DEBUG] GUI Click: Player " + player.getName() + " trying to place/swap item: " + cursorItem);
                        if (statsItemsAPI == null) { /* ... handle null API ... */ return; }

                        String itemKeyFromAPI = statsItemsAPI.getItemDefinitionKey(cursorItem);
                        //plugin.getLogger().info("[DEBUG] GUI Click: Item key for cursor: " + itemKeyFromAPI + ", Accepted: " + slotDef.acceptedItems);

                        if (isItemAccepted(cursorItem, slotDef)) {
                            //plugin.getLogger().info("[DEBUG] GUI Click: Item IS accepted for slot " + slotInGui);

                            // Perform the swap/placement carefully
                            ItemStack oldItemInSlotOriginal = topInventory.getItem(slotInGui); // Get what's really there
                            ItemStack itemToPutOnCursor = null;

                            if (isPlaceholder(oldItemInSlotOriginal, slotDef)) {
                                itemToPutOnCursor = null; // Placeholder goes away, cursor gets nothing back from it
                            } else {
                                itemToPutOnCursor = oldItemInSlotOriginal != null ? oldItemInSlotOriginal.clone() : null;
                            }

                            topInventory.setItem(slotInGui, cursorItem.clone()); // Put new item in slot
                            player.setItemOnCursor(itemToPutOnCursor);         // Put old item (or null) on cursor

                            playerEquipmentManager.setEquippedItem(holder.getPlayerUUID(), slotInGui, cursorItem.clone());
                            // Refresh all GUIs showing this player's data
                            guiManager.refreshAllGUIs(holder.getPlayerUUID());
                            //event.setCancelled(false); // << Allow this specific, handled action
                            //plugin.getLogger().info("[DEBUG] GUI Click: Manually handled PLACE/SWAP. Cursor after manual: " + player.getItemOnCursor() + ", Slot after manual: " + topInventory.getItem(slotInGui));
                            player.updateInventory(); // Sometimes helps sync client
                        } else {
                            //plugin.getLogger().info("[DEBUG] GUI Click: Item IS NOT accepted for slot " + slotInGui);
                            player.sendMessage(plugin.getConfigManager().getPluginPrefix() + plugin.getConfigManager().getMessage("item_not_accepted"));
                            // event remains cancelled by the earlier setCancelled(true)
                        }
                    }
                }
                // --- Case 2: Picking up an item from an equipment slot to cursor ---
                else if (currentItem != null && !currentItem.getType().isAir() && !isPlaceholder(currentItem, slotDef) && cursorItem.getType().isAir()) {
                    // Actions: PICKUP_ALL, PICKUP_SOME, PICKUP_HALF, PICKUP_ONE (when clicking a filled slot with empty cursor)
                    // Action: SWAP_WITH_CURSOR (also covered above, but this handles picking up specifically)
                    if (event.getAction() == InventoryAction.PICKUP_ALL ||
                            event.getAction() == InventoryAction.PICKUP_SOME ||
                            event.getAction() == InventoryAction.PICKUP_HALF ||
                            event.getAction() == InventoryAction.PICKUP_ONE) {

                        //plugin.getLogger().info("[DEBUG] GUI Click: Player " + player.getName() + " trying to pickup item: " + currentItem);

                        /// 1. Put the currentItem onto the player's cursor
                        player.setItemOnCursor(currentItem.clone()); // currentItem is the actual equipment

                        // 2. Set the GUI slot to the placeholder for that slot definition
                        ItemStack placeholderToSet = menuConfig.getPlaceholderItem(); // Default general placeholder
                        if (slotDef.displayItem != null) { // If this specific slot has an empty_slot_display defined
                            placeholderToSet = slotDef.displayItem.clone();
                        }
                        topInventory.setItem(slotInGui, placeholderToSet);

                        // 3. Update your backend data
                        playerEquipmentManager.setEquippedItem(holder.getPlayerUUID(), slotInGui, null);

                        // 4. The event remains cancelled (because it's true from the top of the `rawSlot < topInventory.getSize()` block)
                        //    We have manually handled the entire operation.
                        //plugin.getLogger().info("[DEBUG] GUI Click: MANUALLY handled PICKUP. Cursor: " + player.getItemOnCursor() + ", Slot: " + topInventory.getItem(slotInGui));
                        player.updateInventory();
                    }
                }
                // --- Case 3: Shift-clicking an item FROM an equipment slot TO player inventory ---
                else if (event.getAction() == InventoryAction.MOVE_TO_OTHER_INVENTORY &&
                        currentItem != null && !currentItem.getType().isAir() && !isPlaceholder(currentItem, slotDef)) {
                    //plugin.getLogger().info("[DEBUG] GUI Click: Player " + player.getName() + " trying to shift-click OUT item: " + currentItem);
                    // Try to add to player's inventory
                    ItemStack itemToMoveToPlayer = currentItem.clone(); // This is the actual equipment
                    ItemStack placeholderToSet = menuConfig.getPlaceholderItem(); // Default general placeholder
                    if (slotDef.displayItem != null) { // If this specific slot has an empty_slot_display defined
                        placeholderToSet = slotDef.displayItem.clone();
                    }
                    topInventory.setItem(slotInGui, placeholderToSet);

                    // 2. Update your backend data to reflect the item is no longer in the custom slot.
                    playerEquipmentManager.setEquippedItem(holder.getPlayerUUID(), slotInGui, null);
                    HashMap<Integer, ItemStack> couldNotFit = player.getInventory().addItem(itemToMoveToPlayer);

                    if (!couldNotFit.isEmpty()) {
                        // Item could not fully fit. Drop the remainder at the player's location.
                        player.sendMessage(plugin.getConfigManager().getPluginPrefix() + plugin.getConfigManager().getMessage("inventory_full"));
                        couldNotFit.values().forEach(itemLeft ->
                                player.getWorld().dropItemNaturally(player.getLocation(), itemLeft)
                        );
                        //plugin.getLogger().info("[DEBUG] GUI Click: Shift-click OUT, inventory full. Dropped: " + couldNotFit);
                    } else {
                        //plugin.getLogger().info("[DEBUG] GUI Click: Shift-click OUT successful. Item moved to player inventory.");
                    }
                }
                // Other actions in equipment slots are denied by the event.setCancelled(true) at the top of this block
            } else {
                // Clicked on a decorative/placeholder slot in the GUI - event is already cancelled
                //plugin.getLogger().info("[DEBUG] Clicked GUI Decorative Slot: " + slotInGui + ", Action: " + event.getAction() + ". Denied.");
            }
        }
        // --- Click within the Player's Inventory ---
        else if (clickedInventory.equals(player.getInventory())) {
            //plugin.getLogger().info("[DEBUG] Clicked Player Inventory. Slot: " + event.getSlot() + ", Action: " + event.getAction() + ", currentItem: " + currentItem);
            // --- Case 4: Shift-clicking an item FROM player inventory TO an equipment slot ---
            if (event.getAction() == InventoryAction.MOVE_TO_OTHER_INVENTORY) {
                if (currentItem != null && !currentItem.getType().isAir()) {
                    event.setCancelled(true); // We will handle this manually
                    //plugin.getLogger().info("[DEBUG] Player Inventory: Trying to SHIFT-CLICK IN item: " + currentItem);

                    Integer targetSlot = findAvailableMatchingSlot(currentItem, topInventory); // Your existing method
                    if (targetSlot != null) {
                        //plugin.getLogger().info("[DEBUG] Player Inventory: Found matching GUI slot " + targetSlot + " for shift-click.");
                        topInventory.setItem(targetSlot, currentItem.clone()); // Place in GUI
                        playerEquipmentManager.setEquippedItem(holder.getPlayerUUID(), targetSlot, currentItem.clone());
                        event.setCurrentItem(null); // Remove from player's inventory
                        // event.setCancelled(false); // NO! We handled it, so keep it cancelled to prevent Bukkit's default shift-click too
                        //plugin.getLogger().info("[DEBUG] Player Inventory: Allowed SHIFT-CLICK IN.");
                        player.updateInventory();
                    } else {
                        //plugin.getLogger().info("[DEBUG] Player Inventory: No matching GUI slot for shift-click. Denied.");
                        // Event remains cancelled
                    }
                } else {
                    event.setCancelled(false); // Allow shift-clicking air or nothing, though it does nothing
                }
            } else {
                // Allow all other normal interactions in player's inventory
                event.setCancelled(false);
            }
        } else {
            // Clicked somewhere else unexpected (e.g. another plugin's GUI part if rawSlot was weird)
            // Generally, don't interfere unless you know what it is.
            // If event.getClickedInventory() is null, but rawSlot was not in topInventory, it's outside.
            event.setCancelled(false); // Or true, depending on how paranoid you want to be
        }

    }

    private Integer findAvailableMatchingSlot(ItemStack item, Inventory gui) {
        for (Map.Entry<Integer, MenuConfig.SlotDefinition> entry : menuConfig.getAllSlotDefinitions().entrySet()) {
            int slotIndex = entry.getKey();
            MenuConfig.SlotDefinition slotDef = entry.getValue();
            if (slotDef.isEquipmentSlot()) {
                ItemStack currentInSlot = gui.getItem(slotIndex);
                // Check if slot is empty OR if it's displaying the "empty_slot_display" item
                boolean isEmptyOrPlaceholder = (currentInSlot == null || currentInSlot.getType() == Material.AIR ||
                        (slotDef.displayItem != null && currentInSlot.isSimilar(slotDef.displayItem)));

                if (isEmptyOrPlaceholder && isItemAccepted(item, slotDef)) {
                    return slotIndex;
                }
            }
        }
        return null;
    }


    private boolean isItemAccepted(ItemStack item, MenuConfig.SlotDefinition slotDef) {
        if (statsItemsAPI == null) {
            plugin.getLogger().warning("StatsItems API not available. Cannot validate item for equipment slot.");
            return false; // Or true if you want to be lenient when API is missing
        }
        if (item == null || item.getType().isAir()) return false;

        String itemKey = statsItemsAPI.getItemDefinitionKey(item); // YOU NEED THIS METHOD IN ItemStatManager
        if (itemKey == null) {
            return false; // Item is not a recognized StatsItems item
        }
        return slotDef.acceptedItems.contains(itemKey);
    }

    private boolean isPlaceholder(ItemStack itemInSlot, MenuConfig.SlotDefinition slotDef) {
        if (itemInSlot == null || itemInSlot.getType().isAir() || slotDef == null) return true;
        return slotDef.displayItem != null && itemInSlot.isSimilar(slotDef.displayItem);
    }


    @EventHandler
    public void onInventoryDrag(InventoryDragEvent event) {
        if (event.getInventory().getHolder() instanceof EquipmentGUIHolder) {
            // Check if any of the dragged-over slots are part of the top inventory
            boolean inTopInventory = false;
            for (int slot : event.getRawSlots()) {
                if (slot < event.getView().getTopInventory().getSize()) {
                    inTopInventory = true;
                    break;
                }
            }
            if (inTopInventory) {
                event.setCancelled(true); // Disallow dragging into/within the custom GUI for simplicity
            }
        }
    }


    @EventHandler(priority = EventPriority.MONITOR) // Monitor, so it runs after other plugins might have modified it
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getInventory().getHolder() instanceof EquipmentGUIHolder) {
            Player player = (Player) event.getPlayer();
            UUID playerUUID = player.getUniqueId();
            PlayerEquipmentData currentData = playerEquipmentManager.getPlayerData(playerUUID);
            Inventory gui = event.getInventory();
            boolean changed = false;

            Map<Integer, ItemStack> newEquipmentState = new HashMap<>();

            for (int i = 0; i < gui.getSize(); i++) {
                MenuConfig.SlotDefinition slotDef = menuConfig.getSlotDefinition(i);
                if (slotDef != null && slotDef.isEquipmentSlot()) {
                    ItemStack itemInGui = gui.getItem(i);
                    // Only store actual items, not empty_slot_display placeholders
                    if (itemInGui != null && itemInGui.getType() != Material.AIR && !isPlaceholder(itemInGui, slotDef)) {
                        newEquipmentState.put(i, itemInGui.clone());
                    }
                }
            }

            // Compare with stored data and update if necessary
            Map<Integer, ItemStack> oldEquipmentState = currentData.getEquippedItems();

            // Check for removed items or changed items
            for (Map.Entry<Integer, ItemStack> oldEntry : oldEquipmentState.entrySet()) {
                if (!newEquipmentState.containsKey(oldEntry.getKey()) ||
                        (newEquipmentState.get(oldEntry.getKey()) != null && oldEntry.getValue() != null &&
                                !newEquipmentState.get(oldEntry.getKey()).isSimilar(oldEntry.getValue()))) {
                    changed = true;
                    break;
                }
            }
            // Check for added items
            if (!changed) {
                for (Integer newSlotKey : newEquipmentState.keySet()) {
                    if (!oldEquipmentState.containsKey(newSlotKey)) {
                        changed = true;
                        break;
                    }
                }
            }


            if (changed) {
                // Clear old and set new
                oldEquipmentState.keySet().forEach(slot -> playerEquipmentManager.setEquippedItem(playerUUID, slot, null)); // Clear all first
                newEquipmentState.forEach((slot, item) -> playerEquipmentManager.setEquippedItem(playerUUID, slot, item)); // Then add new ones
                plugin.getLogger().fine("Player " + player.getName() + " equipment GUI changed. Triggering stat update.");
            }

            // Always save data on close, as PlayerEquipmentManager handles the actual file IO.
            // The setEquippedItem calls above already trigger individual stat updates if StatsItems is hooked.
            // A final save ensures persistence.
            playerEquipmentManager.savePlayerData(player);


            // Crucially, trigger a stat update for the player in StatsItems
            if (plugin.getServer().getPluginManager().getPlugin("StatsItems") != null) {
                Object statsItemsPluginObj = plugin.getServer().getPluginManager().getPlugin("StatsItems");
                try {
                    Object itemStatManager = statsItemsPluginObj.getClass().getMethod("getItemStatManager").invoke(statsItemsPluginObj);
                    itemStatManager.getClass().getMethod("updatePlayerStats", Player.class).invoke(itemStatManager, player);
                    plugin.getLogger().fine("Forcing StatsItems update for " + player.getName() + " after GUI close.");
                } catch (Exception e) {
                    plugin.getLogger().warning("Could not trigger StatsItems update on GUI close: " + e.getMessage());
                }
            }
        }
    }
}
