package ca.xef5000.mmostats.gui;

import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;

import javax.annotation.Nonnull;
import java.util.UUID;

public class EquipmentGUIHolder implements InventoryHolder {
    private final UUID playerUUID; // To associate the GUI with a player

    public EquipmentGUIHolder(UUID playerUUID) {
        this.playerUUID = playerUUID;
    }

    public UUID getPlayerUUID() {
        return playerUUID;
    }

    @Nonnull
    @Override
    public Inventory getInventory() {
        // This method is typically called by Bukkit, but we don't need to return a pre-existing inventory here.
        // The inventory is created and managed by EquipmentGUIManager.
        return null; // Or throw an UnsupportedOperationException, as it's not meant to be directly used like this
    }
}
