package ca.xef5000.mmostats.gui;

import ca.xef5000.mmostats.StatsEquipment;
import ca.xef5000.mmostats.config.MenuConfig;
import ca.xef5000.mmostats.manager.PlayerEquipmentData;
import ca.xef5000.mmostats.manager.PlayerEquipmentManager;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;

import java.util.Map;

public class EquipmentGUIManager {
    private final StatsEquipment plugin;
    private final MenuConfig menuConfig;
    private final PlayerEquipmentManager playerEquipmentManager;
    // private final ItemStatManager statsItemsManagerAPI; // For checking accepted_items

    public static final String GUI_METADATA_TAG = "StatsEquipmentGUI"; // For InventoryCloseEvent identification


    public EquipmentGUIManager(StatsEquipment plugin, MenuConfig menuConfig, PlayerEquipmentManager playerEquipmentManager /*, ItemStatManager statsItemsManagerAPI*/) {
        this.plugin = plugin;
        this.menuConfig = menuConfig;
        this.playerEquipmentManager = playerEquipmentManager;
        // this.statsItemsManagerAPI = statsItemsManagerAPI;
    }

    public void openEquipmentGUI(Player player) {
        Inventory gui = Bukkit.createInventory(new EquipmentGUIHolder(player.getUniqueId()), menuConfig.getGuiSize(), menuConfig.getGuiTitle());
        // Set metadata on the inventory for easy identification
        // This requires a custom InventoryHolder or another way to tag it if you don't want to use PDC on items.
        // For simplicity, we'll use a custom InventoryHolder.

        PlayerEquipmentData playerData = playerEquipmentManager.getPlayerData(player.getUniqueId());
        Map<Integer, ItemStack> equippedItems = playerData.getEquippedItems();

        for (int i = 0; i < menuConfig.getGuiSize(); i++) {
            MenuConfig.SlotDefinition slotDef = menuConfig.getSlotDefinition(i);
            if (slotDef != null) {
                if (slotDef.isEquipmentSlot()) {
                    ItemStack equipped = equippedItems.get(i);
                    if (equipped != null && equipped.getType() != Material.AIR) {
                        gui.setItem(i, equipped.clone());
                    } else if (slotDef.displayItem != null) { // Empty slot display
                        gui.setItem(i, slotDef.displayItem.clone());
                    } else { // Fallback to general placeholder if no empty_slot_display
                        gui.setItem(i, menuConfig.getPlaceholderItem());
                    }
                } else { // Decorative slot
                    gui.setItem(i, slotDef.displayItem != null ? slotDef.displayItem.clone() : menuConfig.getPlaceholderItem());
                }
            } else { // No definition, use placeholder
                gui.setItem(i, menuConfig.getPlaceholderItem());
            }
        }
        player.openInventory(gui);
    }
}
