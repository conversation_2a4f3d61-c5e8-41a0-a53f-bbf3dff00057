plugins {
    id 'java'
    id("xyz.jpenilla.run-paper") version "2.3.1"
    id 'com.github.johnrengelman.shadow' version '8.1.1'
}

group = 'ca.xef5000'
version = '1.1'

repositories {
    mavenCentral()
    maven {
        name = "papermc-repo"
        url = "https://repo.papermc.io/repository/maven-public/"
    }
    maven {
        name = "sonatype"
        url = "https://oss.sonatype.org/content/groups/public/"
    }
    maven {
        name = "dmulloy2-repo"
        url = "https://repo.dmulloy2.net/repository/public/"
    }
    maven { url 'https://jitpack.io' }
    maven { url = "https://repo.extendedclip.com/content/repositories/placeholderapi/" }
    maven { url = "https://repo.bstats.org/content/repositories/releases/" } // bStats repository
}

dependencies {
    compileOnly("io.papermc.paper:paper-api:1.20.1-R0.1-SNAPSHOT")
    compileOnly("com.comphenix.protocol:ProtocolLib:5.1.0")
    compileOnly("com.github.MilkBowl:VaultAPI:1.7")
    compileOnly("me.clip:placeholderapi:2.11.6")
    implementation("org.xerial:sqlite-jdbc:********")
    implementation("org.bstats:bstats-bukkit:3.0.2") // bStats dependency
}

tasks {
    runServer {
        // Configure the Minecraft version for our task.
        // This is the only required configuration besides applying the plugin.
        // Your plugin's jar (or shadowJar if present) will be used automatically.
        minecraftVersion("1.20")
    }

    shadowJar {
        relocate 'org.bstats', 'ca.xef5000.lib.bstats'
    }
}

def targetJavaVersion = 17
java {
    def javaVersion = JavaVersion.toVersion(targetJavaVersion)
    sourceCompatibility = javaVersion
    targetCompatibility = javaVersion
    if (JavaVersion.current() < javaVersion) {
        toolchain.languageVersion = JavaLanguageVersion.of(targetJavaVersion)
    }
}

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8'

    if (targetJavaVersion >= 10 || JavaVersion.current().isJava10Compatible()) {
        options.release.set(targetJavaVersion)
    }
}

processResources {
    def props = [version: version]
    inputs.properties props
    filteringCharset 'UTF-8'
    filesMatching('plugin.yml') {
        expand props
    }
}
