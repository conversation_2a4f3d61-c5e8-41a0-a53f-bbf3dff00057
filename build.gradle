plugins {
    id 'java'
}

def targetJavaVersion = 17
allprojects {
    group = 'ca.xef5000.mmostats'
    version = '1.0.0'

    repositories {
        mavenCentral()
        maven { url = 'https://hub.spigotmc.org/nexus/content/repositories/snapshots/' }
    }

    tasks.withType(JavaCompile).configureEach {
        options.encoding = 'UTF-8'

        if (targetJavaVersion >= 10 || JavaVersion.current().isJava10Compatible()) {
            options.release.set(targetJavaVersion)
        }
    }
}

subprojects {
    apply plugin: 'java'

    java {
        def javaVersion = JavaVersion.toVersion(targetJavaVersion)
        sourceCompatibility = javaVersion
        targetCompatibility = javaVersion
        if (JavaVersion.current() < javaVersion) {
            toolchain.languageVersion = JavaLanguageVersion.of(targetJavaVersion)
        }
    }

    dependencies {
        compileOnly("org.spigotmc:spigot-api:1.20.1-R0.1-SNAPSHOT")
    }
}
