# Rolling Animation GUI Configuration
# This GUI shows the coinflip animation to both players

gui:
  title: "&6&lCoinflip in Progress..."
  rows: 3

# Animation settings
animation:
  # Duration of the rolling animation in ticks (20 ticks = 1 second)
  duration_ticks: 100
  
  # How often to switch between colors (in ticks)
  switch_interval: 10
  
  # Slots that will be filled with colored glass panes during animation
  # Center slots (12, 13, 14) are reserved for player heads
  animated_slots: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]

# Player head positions
player_heads:
  # Creator's head position
  creator_slot: 12
  creator_name: "&e{creator_name}"
  creator_lore:
    - "&7Color: &f{creator_color}"
    
  # Accepter's head position  
  accepter_slot: 14
  accepter_name: "&e{accepter_name}"
  accepter_lore:
    - "&7Color: &f{accepter_color}"
    
  # VS indicator in the middle
  vs_slot: 13
  vs_material: DIAMOND
  vs_name: "&f&lVS"
  vs_lore:
    - "&7Rolling..."

# Sound effects during animation
sounds:
  # Sound played at each color switch
  switch_sound: BLOCK_NOTE_BLOCK_PLING
  switch_volume: 0.5
  switch_pitch: 1.0
  
  # Sound played when animation ends
  end_sound: ENTITY_PLAYER_LEVELUP
  end_volume: 1.0
  end_pitch: 1.0
