{"credit": "Made with Blockbench", "textures": {"2": "minepiece:item/tiered_dagger_tier_1", "particle": "minepiece:item/tiered_dagger_tier_1"}, "elements": [{"from": [8, 8, 6.5], "to": [8, 17, 9.5], "rotation": {"angle": 0, "axis": "y", "origin": [7, 8, 6]}, "faces": {"north": {"uv": [0, 0, 0, 9], "texture": "#2"}, "east": {"uv": [0, 0, 3, 9], "texture": "#2"}, "south": {"uv": [0, 0, 0, 9], "texture": "#2"}, "west": {"uv": [3, 0, 6, 9], "texture": "#2"}, "up": {"uv": [0, 3, 0, 0], "texture": "#2"}, "down": {"uv": [0, 0, 0, 3], "texture": "#2"}}}, {"from": [7, 7, 6], "to": [9, 8, 10], "rotation": {"angle": 0, "axis": "y", "origin": [6, 6, 7]}, "faces": {"north": {"uv": [10, 2, 12, 3], "texture": "#2"}, "east": {"uv": [10, 0, 14, 1], "texture": "#2"}, "south": {"uv": [10, 3, 12, 4], "texture": "#2"}, "west": {"uv": [10, 1, 14, 2], "texture": "#2"}, "up": {"uv": [10, 4, 8, 0], "texture": "#2"}, "down": {"uv": [10, 4, 8, 8], "texture": "#2"}}}, {"from": [7.5, 2, 7], "to": [8.5, 7, 9], "rotation": {"angle": 0, "axis": "y", "origin": [7, 5, 7]}, "faces": {"north": {"uv": [3, 9, 4, 14], "texture": "#2"}, "east": {"uv": [6, 0, 8, 5], "texture": "#2"}, "south": {"uv": [4, 9, 5, 14], "texture": "#2"}, "west": {"uv": [6, 5, 8, 10], "texture": "#2"}, "up": {"uv": [6, 11, 5, 9], "texture": "#2"}, "down": {"uv": [11, 4, 10, 6], "texture": "#2"}}}, {"from": [8, 8, 9], "to": [8, 10, 12], "rotation": {"angle": -45, "axis": "y", "origin": [8, 9, 9]}, "faces": {"north": {"uv": [0, 0, 0, 2], "texture": "#2"}, "east": {"uv": [8, 8, 11, 10], "texture": "#2"}, "south": {"uv": [0, 0, 0, 2], "texture": "#2"}, "west": {"uv": [0, 9, 3, 11], "texture": "#2"}, "up": {"uv": [0, 3, 0, 0], "texture": "#2"}, "down": {"uv": [0, 0, 0, 3], "texture": "#2"}}}], "display": {"thirdperson_righthand": {"translation": [0, 1.5, 1.75]}, "thirdperson_lefthand": {"translation": [0, 1.5, 1.75]}, "firstperson_righthand": {"rotation": [0, 0, 20], "translation": [3.5, 1.75, 0]}, "firstperson_lefthand": {"rotation": [0, 0, 20], "translation": [3.5, 1.75, 0]}, "ground": {"rotation": [0, 0, 90], "translation": [0, -2.75, 0]}, "gui": {"rotation": [42, 0, -33], "translation": [-0.5, -1, 0]}, "head": {"rotation": [122, 0, 0], "translation": [6.5, -22.25, -4], "scale": [1.5, 1.5, 1.5]}, "fixed": {"rotation": [69, 33, 0], "translation": [0, -2.75, -11], "scale": [2, 2, 2]}}}