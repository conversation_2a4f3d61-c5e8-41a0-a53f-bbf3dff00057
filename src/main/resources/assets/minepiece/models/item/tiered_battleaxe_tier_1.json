{"credit": "Made with Blockbench", "texture_size": [64, 64], "textures": {"2": "minepiece:item/tiered_battleaxe_tier_1", "particle": "minepiece:item/tiered_battleaxe_tier_1"}, "elements": [{"from": [7, -11, 7], "to": [9, 10, 9], "rotation": {"angle": 0, "axis": "y", "origin": [7, -3, 7]}, "faces": {"north": {"uv": [2.5, 3.25, 3, 8.5], "texture": "#2"}, "east": {"uv": [3, 3.25, 3.5, 8.5], "texture": "#2"}, "south": {"uv": [3.5, 3.25, 4, 8.5], "texture": "#2"}, "west": {"uv": [4, 0, 4.5, 5.25], "texture": "#2"}, "up": {"uv": [0.5, 6, 0, 5.5], "texture": "#2"}, "down": {"uv": [1, 5.5, 0.5, 6], "texture": "#2"}}}, {"from": [7.5, 1, 4.5], "to": [8.5, 7, 10.5], "rotation": {"angle": 45, "axis": "x", "origin": [8, 7, 8.5]}, "faces": {"north": {"uv": [4.5, 3, 4.75, 4.5], "texture": "#2"}, "east": {"uv": [4.5, 0, 6, 1.5], "texture": "#2"}, "south": {"uv": [4.5, 4.5, 4.75, 6], "texture": "#2"}, "west": {"uv": [4.5, 1.5, 6, 3], "texture": "#2"}, "up": {"uv": [5, 4.5, 4.75, 3], "texture": "#2"}, "down": {"uv": [5, 4.5, 4.75, 6], "texture": "#2"}}}, {"from": [8, -1, -2], "to": [8, 12, 6], "rotation": {"angle": 0, "axis": "y", "origin": [8, 6, 3.5]}, "faces": {"north": {"uv": [0, 0, 0, 3.25], "texture": "#2"}, "east": {"uv": [0, 0, 2, 3.25], "texture": "#2"}, "south": {"uv": [0, 0, 0, 3.25], "texture": "#2"}, "west": {"uv": [2, 0, 4, 3.25], "texture": "#2"}, "up": {"uv": [0, 2, 0, 0], "texture": "#2"}, "down": {"uv": [0, 0, 0, 2], "texture": "#2"}}}, {"from": [8, 1, 9], "to": [8, 10, 14], "rotation": {"angle": 0, "axis": "y", "origin": [8, 6, 14.5]}, "faces": {"north": {"uv": [0, 0, 0, 2.25], "texture": "#2"}, "east": {"uv": [0, 3.25, 1.25, 5.5], "texture": "#2"}, "south": {"uv": [0, 0, 0, 2.25], "texture": "#2"}, "west": {"uv": [1.25, 3.25, 2.5, 5.5], "texture": "#2"}, "up": {"uv": [0, 1.25, 0, 0], "texture": "#2"}, "down": {"uv": [0, 0, 0, 1.25], "texture": "#2"}}}, {"from": [7.5, 3, 9], "to": [8.5, 8, 10], "rotation": {"angle": 0, "axis": "y", "origin": [0, 6, 9]}, "faces": {"north": {"uv": [5, 5, 5.25, 6.25], "texture": "#2"}, "east": {"uv": [4, 5.25, 4.25, 6.5], "texture": "#2"}, "south": {"uv": [4.25, 5.25, 4.5, 6.5], "texture": "#2"}, "west": {"uv": [5.25, 5, 5.5, 6.25], "texture": "#2"}, "up": {"uv": [1.25, 5.75, 1, 5.5], "texture": "#2"}, "down": {"uv": [1.5, 5.5, 1.25, 5.75], "texture": "#2"}}}, {"from": [8, 9, 9], "to": [8, 11, 12], "rotation": {"angle": 22.5, "axis": "y", "origin": [8, 10, 9]}, "faces": {"north": {"uv": [0, 0, 0, 0.5], "texture": "#2"}, "east": {"uv": [5, 3, 5.75, 3.5], "texture": "#2"}, "south": {"uv": [0, 0, 0, 0.5], "texture": "#2"}, "west": {"uv": [5, 3.5, 5.75, 4], "texture": "#2"}, "up": {"uv": [0, 0.75, 0, 0], "texture": "#2"}, "down": {"uv": [0, 0, 0, 0.75], "texture": "#2"}}}, {"from": [4, -1, 8], "to": [7, 1, 8], "rotation": {"angle": 22.5, "axis": "y", "origin": [7, -1, 8]}, "faces": {"north": {"uv": [5, 4, 5.75, 4.5], "texture": "#2"}, "east": {"uv": [0, 0, 0, 0.5], "texture": "#2"}, "south": {"uv": [5, 4.5, 5.75, 5], "texture": "#2"}, "west": {"uv": [0, 0, 0, 0.5], "texture": "#2"}, "up": {"uv": [0.75, 0, 0, 0], "texture": "#2"}, "down": {"uv": [0.75, 0, 0, 0], "texture": "#2"}}}], "display": {"thirdperson_righthand": {"translation": [0, 11.25, 2]}, "thirdperson_lefthand": {"translation": [0, 11.25, 2]}, "firstperson_righthand": {"rotation": [0, 0, 15], "translation": [0.25, 11.25, 0]}, "firstperson_lefthand": {"rotation": [0, 0, 15], "translation": [0.25, 11.25, 0]}, "ground": {"rotation": [0, 0, 90], "translation": [-5.75, -2.5, 0]}, "gui": {"rotation": [-24, 121, 38], "translation": [2, 4, 0], "scale": [0.65, 0.65, 0.65]}, "head": {"rotation": [0, -90, 0], "translation": [-2.75, -4, 4.75], "scale": [1.5, 1.5, 1.5]}, "fixed": {"rotation": [136, 0, 0], "translation": [0, -9.75, -8.5], "scale": [2, 2, 2]}}}