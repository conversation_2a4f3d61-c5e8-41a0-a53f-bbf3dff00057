{"credit": "MadewithBlockbench", "texture_size": [128, 128], "textures": {"0": "elitecreatures:casino_decoration_v1/casino_game_bigwin_red", "particle": "elitecreatures:casino_decoration_v1/casino_game_bigwin_red"}, "elements": [{"from": [1, 0, 3], "to": [15, 22, 15], "faces": {"north": {"uv": [0, 0, 1.75, 2.75], "texture": "#0"}, "east": {"uv": [0, 2.75, 1.5, 5.5], "texture": "#0"}, "south": {"uv": [1.75, 0, 3.5, 2.75], "texture": "#0"}, "west": {"uv": [0, 2.75, 1.5, 5.5], "texture": "#0"}, "up": {"uv": [3.25, 4.25, 1.5, 2.75], "texture": "#0"}}}, {"from": [0, 7, 5], "to": [1, 10, 8], "faces": {"north": {"uv": [5.75, 4.875, 5.875, 5.25], "texture": "#0"}, "east": {"uv": [6.25, 5.125, 6.625, 5.5], "texture": "#0"}, "south": {"uv": [5.875, 4.875, 5.75, 5.25], "texture": "#0"}, "west": {"uv": [6.25, 5.125, 6.625, 5.5], "texture": "#0"}, "up": {"uv": [5.875, 5.25, 5.75, 4.875], "texture": "#0"}, "down": {"uv": [5.875, 4.875, 5.75, 5.25], "texture": "#0"}}}, {"from": [0.5, 8.75, 5.5], "to": [0.5, 15.75, 7.5], "rotation": {"angle": -22.5, "axis": "x", "origin": [0.75, 8.5, 6.75]}, "faces": {"east": {"uv": [3.875, 4.25, 3.625, 5.125], "texture": "#0"}, "west": {"uv": [3.875, 4.25, 3.625, 5.125], "texture": "#0"}}}, {"from": [0, 15.75, 5.5], "to": [1, 17.75, 7.5], "rotation": {"angle": -22.5, "axis": "x", "origin": [0.75, 8.5, 6.75]}, "faces": {"north": {"uv": [3.75, 4, 3.625, 4.25], "texture": "#0"}, "east": {"uv": [3.875, 4, 3.625, 4.25], "texture": "#0"}, "south": {"uv": [3.75, 4, 3.625, 4.25], "texture": "#0"}, "west": {"uv": [3.875, 4, 3.625, 4.25], "texture": "#0"}, "up": {"uv": [3.75, 4, 3.625, 4.25], "texture": "#0"}, "down": {"uv": [3.75, 4, 3.625, 4.25], "texture": "#0"}}}, {"from": [1, 0, 0], "to": [15, 1, 3], "faces": {"north": {"uv": [1.5, 5.25, 3.25, 5.375], "texture": "#0"}, "east": {"uv": [1.5, 5.75, 1.875, 5.875], "texture": "#0"}, "west": {"uv": [1.5, 5.75, 1.875, 5.875], "texture": "#0"}, "up": {"uv": [3.25, 5.75, 1.5, 5.375], "texture": "#0"}}}, {"from": [1, 3, 1], "to": [15, 10, 3], "faces": {"north": {"uv": [3.875, 4, 5.625, 4.875], "texture": "#0"}, "east": {"uv": [5.625, 4, 5.875, 4.875], "texture": "#0"}, "west": {"uv": [5.625, 4, 5.875, 4.875], "texture": "#0"}, "up": {"uv": [5.625, 5.125, 3.875, 4.875], "texture": "#0"}, "down": {"uv": [5.625, 4.875, 3.875, 5.125], "texture": "#0"}}}, {"from": [1, 6, -2], "to": [15, 9, 1], "faces": {"north": {"uv": [5.875, 4, 7.625, 4.375], "texture": "#0"}, "east": {"uv": [5.875, 5.125, 6.25, 5.5], "texture": "#0"}, "west": {"uv": [5.875, 5.125, 6.25, 5.5], "texture": "#0"}, "up": {"uv": [7.625, 4.75, 5.875, 4.375], "texture": "#0"}, "down": {"uv": [7.625, 4.75, 5.875, 5.125], "texture": "#0"}}}, {"from": [5, 8.5, -1.25], "to": [7, 9.5, 0.75], "faces": {"north": {"uv": [5.5, 5.375, 5.75, 5.5], "texture": "#0"}, "east": {"uv": [5.5, 5.375, 5.75, 5.5], "texture": "#0"}, "south": {"uv": [5.5, 5.375, 5.75, 5.5], "texture": "#0"}, "west": {"uv": [5.5, 5.375, 5.75, 5.5], "texture": "#0"}, "up": {"uv": [5.75, 5.375, 5.5, 5.125], "texture": "#0"}}}, {"from": [8, 8.5, -1.25], "to": [10, 9.5, 0.75], "faces": {"north": {"uv": [5.5, 5.375, 5.75, 5.5], "texture": "#0"}, "east": {"uv": [5.5, 5.375, 5.75, 5.5], "texture": "#0"}, "south": {"uv": [5.5, 5.375, 5.75, 5.5], "texture": "#0"}, "west": {"uv": [5.5, 5.375, 5.75, 5.5], "texture": "#0"}, "up": {"uv": [5.75, 5.375, 5.5, 5.125], "texture": "#0"}}}, {"from": [11, 8.5, -1.25], "to": [13, 9.5, 0.75], "faces": {"north": {"uv": [5.5, 5.375, 5.75, 5.5], "texture": "#0"}, "east": {"uv": [5.5, 5.375, 5.75, 5.5], "texture": "#0"}, "south": {"uv": [5.5, 5.375, 5.75, 5.5], "texture": "#0"}, "west": {"uv": [5.5, 5.375, 5.75, 5.5], "texture": "#0"}, "up": {"uv": [5.75, 5.375, 5.5, 5.125], "texture": "#0"}}}, {"from": [14, 10, 2], "to": [15, 15, 3], "faces": {"north": {"uv": [3.25, 2.75, 3.375, 3.375], "texture": "#0"}, "east": {"uv": [3.25, 2.75, 3.375, 3.375], "texture": "#0"}, "south": {"uv": [3.25, 2.75, 3.375, 3.375], "texture": "#0"}, "west": {"uv": [3.25, 2.75, 3.375, 3.375], "texture": "#0"}}}, {"from": [1, 10, 2], "to": [2, 15, 3], "rotation": {"angle": 0, "axis": "y", "origin": [16, 0, 0]}, "faces": {"north": {"uv": [3.375, 2.75, 3.25, 3.375], "texture": "#0"}, "east": {"uv": [3.375, 2.75, 3.25, 3.375], "texture": "#0"}, "south": {"uv": [3.375, 2.75, 3.25, 3.375], "texture": "#0"}, "west": {"uv": [3.375, 2.75, 3.25, 3.375], "texture": "#0"}}}, {"from": [1, 15, 2], "to": [15, 22, 3], "faces": {"north": {"uv": [1.5, 4.25, 3.25, 5.125], "texture": "#0"}, "east": {"uv": [3.25, 4.25, 3.375, 5.125], "texture": "#0"}, "west": {"uv": [3.375, 4.25, 3.25, 5.125], "texture": "#0"}, "up": {"uv": [3.25, 5.25, 1.5, 5.125], "texture": "#0"}, "down": {"uv": [3.25, 5.125, 1.5, 5.25], "texture": "#0"}}}, {"from": [6.5, 22, 7.5], "to": [9.5, 25, 10.5], "faces": {"north": {"uv": [6.625, 5.125, 7, 5.5], "texture": "#0"}, "east": {"uv": [6.625, 5.125, 7, 5.5], "texture": "#0"}, "south": {"uv": [6.625, 5.125, 7, 5.5], "texture": "#0"}, "west": {"uv": [6.625, 5.125, 7, 5.5], "texture": "#0"}, "up": {"uv": [7.375, 5.5, 7, 5.125], "texture": "#0"}, "down": {"uv": [7.375, 5.125, 7, 5.5], "texture": "#0"}}}, {"from": [0, -16, 0], "to": [16, 0, 16], "faces": {"north": {"uv": [5.5, 0, 7.5, 2], "texture": "#0"}, "east": {"uv": [3.5, 0, 5.5, 2], "texture": "#0"}, "south": {"uv": [5.5, 2, 7.5, 4], "texture": "#0"}, "west": {"uv": [5.5, 0, 3.5, 2], "texture": "#0"}, "up": {"uv": [5.5, 4, 3.5, 2], "texture": "#0"}, "down": {"uv": [5.5, 2, 3.5, 4], "texture": "#0"}}}, {"from": [0, -3, -2], "to": [16, 0, 0], "faces": {"north": {"uv": [3.25, 5.125, 5.25, 5.5], "texture": "#0"}, "east": {"uv": [5.25, 5.125, 5.5, 5.5], "texture": "#0"}, "west": {"uv": [5.5, 5.125, 5.25, 5.5], "texture": "#0"}, "up": {"uv": [5.25, 5.75, 3.25, 5.5], "texture": "#0"}, "down": {"uv": [5.25, 5.5, 3.25, 5.75], "texture": "#0"}}}], "gui_light": "front", "display": {"thirdperson_righthand": {"translation": [0, 3.5, 2.25], "scale": [0.23, 0.23, 0.23]}, "thirdperson_lefthand": {"translation": [0, 3.5, 2.25], "scale": [0.23, 0.23, 0.23]}, "firstperson_righthand": {"translation": [0, 3.5, 2.25], "scale": [0.23, 0.23, 0.23]}, "firstperson_lefthand": {"translation": [-0.75, 3.5, 2.25], "scale": [0.23, 0.23, 0.23]}, "ground": {"translation": [0, 5.5, 0], "scale": [0.36, 0.36, 0.36]}, "gui": {"rotation": [31, 135, 0], "translation": [0, 1, 0], "scale": [0.25, 0.25, 0.25]}, "head": {"rotation": [0, -180, 0], "translation": [0, -4.75, 0.75], "scale": [1.6, 1.6, 1.6]}, "fixed": {"translation": [0, 2.25, -3], "scale": [0.42, 0.42, 0.42]}}}