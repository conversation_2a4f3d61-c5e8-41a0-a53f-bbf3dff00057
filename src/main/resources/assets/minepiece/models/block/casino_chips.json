{"credit": "MadewithBlockbench", "texture_size": [8, 8], "textures": {"0": "elitecreatures:casino_decoration_v1/casino_chips", "particle": "elitecreatures:casino_decoration_v1/casino_chips"}, "elements": [{"from": [6, 0, 9], "to": [8, 1, 11], "rotation": {"angle": 0, "axis": "y", "origin": [7, 0.5, 10]}, "faces": {"north": {"uv": [4, 0, 8, 2], "texture": "#0"}, "east": {"uv": [4, 0, 8, 2], "texture": "#0"}, "south": {"uv": [4, 0, 8, 2], "texture": "#0"}, "west": {"uv": [4, 0, 8, 2], "texture": "#0"}, "up": {"uv": [4, 4, 0, 0], "texture": "#0"}, "down": {"uv": [4, 0, 0, 4], "texture": "#0"}}}, {"from": [6, 1, 9], "to": [8, 2, 11], "rotation": {"angle": 0, "axis": "y", "origin": [7, 1.5, 10]}, "faces": {"north": {"uv": [4, 0, 8, 2], "texture": "#0"}, "east": {"uv": [4, 0, 8, 2], "texture": "#0"}, "south": {"uv": [4, 0, 8, 2], "texture": "#0"}, "west": {"uv": [4, 0, 8, 2], "texture": "#0"}, "up": {"uv": [4, 4, 0, 0], "texture": "#0"}, "down": {"uv": [4, 0, 0, 4], "texture": "#0"}}}, {"from": [8.5, 0, 5.75], "to": [10.5, 1, 7.75], "rotation": {"angle": -45, "axis": "y", "origin": [8, 1, 6]}, "faces": {"north": {"uv": [4, 0, 8, 2], "texture": "#0"}, "east": {"uv": [4, 0, 8, 2], "texture": "#0"}, "south": {"uv": [4, 0, 8, 2], "texture": "#0"}, "west": {"uv": [4, 0, 8, 2], "texture": "#0"}, "up": {"uv": [4, 4, 0, 0], "texture": "#0"}, "down": {"uv": [4, 0, 0, 4], "texture": "#0"}}}, {"from": [8.5, 1, 5.75], "to": [10.5, 2, 7.75], "rotation": {"angle": -45, "axis": "y", "origin": [8, 1, 6]}, "faces": {"north": {"uv": [4, 0, 8, 2], "texture": "#0"}, "east": {"uv": [4, 0, 8, 2], "texture": "#0"}, "south": {"uv": [4, 0, 8, 2], "texture": "#0"}, "west": {"uv": [4, 0, 8, 2], "texture": "#0"}, "up": {"uv": [4, 4, 0, 0], "texture": "#0"}, "down": {"uv": [4, 0, 0, 4], "texture": "#0"}}}, {"from": [6, 2, 9], "to": [8, 3, 11], "rotation": {"angle": 0, "axis": "y", "origin": [7, 1.5, 10]}, "faces": {"north": {"uv": [4, 0, 8, 2], "texture": "#0"}, "east": {"uv": [4, 0, 8, 2], "texture": "#0"}, "south": {"uv": [4, 0, 8, 2], "texture": "#0"}, "west": {"uv": [4, 0, 8, 2], "texture": "#0"}, "up": {"uv": [4, 4, 0, 0], "texture": "#0"}, "down": {"uv": [4, 0, 0, 4], "texture": "#0"}}}, {"from": [8, 0, 11], "to": [10, 1, 13], "rotation": {"angle": 45, "axis": "y", "origin": [9, 1.5, 12]}, "faces": {"north": {"uv": [4, 0, 8, 2], "texture": "#0"}, "east": {"uv": [4, 0, 8, 2], "texture": "#0"}, "south": {"uv": [4, 0, 8, 2], "texture": "#0"}, "west": {"uv": [4, 0, 8, 2], "texture": "#0"}, "up": {"uv": [4, 4, 0, 0], "texture": "#0"}, "down": {"uv": [4, 0, 0, 4], "texture": "#0"}}}, {"from": [8, 1, 11], "to": [10, 2, 13], "rotation": {"angle": 45, "axis": "y", "origin": [9, 1.5, 12]}, "faces": {"north": {"uv": [4, 0, 8, 2], "texture": "#0"}, "east": {"uv": [4, 0, 8, 2], "texture": "#0"}, "south": {"uv": [4, 0, 8, 2], "texture": "#0"}, "west": {"uv": [4, 0, 8, 2], "texture": "#0"}, "up": {"uv": [4, 4, 0, 0], "texture": "#0"}, "down": {"uv": [4, 0, 0, 4], "texture": "#0"}}}, {"from": [8, 2, 11], "to": [10, 3, 13], "rotation": {"angle": 45, "axis": "y", "origin": [9, 1.5, 12]}, "faces": {"north": {"uv": [4, 0, 8, 2], "texture": "#0"}, "east": {"uv": [4, 0, 8, 2], "texture": "#0"}, "south": {"uv": [4, 0, 8, 2], "texture": "#0"}, "west": {"uv": [4, 0, 8, 2], "texture": "#0"}, "up": {"uv": [4, 4, 0, 0], "texture": "#0"}, "down": {"uv": [4, 0, 0, 4], "texture": "#0"}}}, {"from": [6, 3, 9], "to": [8, 4, 11], "rotation": {"angle": 22.5, "axis": "y", "origin": [7, 4, 10]}, "faces": {"north": {"uv": [4, 0, 8, 2], "texture": "#0"}, "east": {"uv": [4, 0, 8, 2], "texture": "#0"}, "south": {"uv": [4, 0, 8, 2], "texture": "#0"}, "west": {"uv": [4, 0, 8, 2], "texture": "#0"}, "up": {"uv": [4, 4, 0, 0], "texture": "#0"}, "down": {"uv": [4, 0, 0, 4], "texture": "#0"}}}, {"from": [6, 4, 9], "to": [8, 5, 11], "rotation": {"angle": 45, "axis": "y", "origin": [7, 4, 10]}, "faces": {"north": {"uv": [4, 0, 8, 2], "texture": "#0"}, "east": {"uv": [4, 0, 8, 2], "texture": "#0"}, "south": {"uv": [4, 0, 8, 2], "texture": "#0"}, "west": {"uv": [4, 0, 8, 2], "texture": "#0"}, "up": {"uv": [4, 4, 0, 0], "texture": "#0"}, "down": {"uv": [4, 0, 0, 4], "texture": "#0"}}}, {"from": [2.85195, 0, 9.77164], "to": [4.85195, 1, 11.77164], "rotation": {"angle": -45, "axis": "y", "origin": [3.85195, 1, 10.77164]}, "faces": {"north": {"uv": [4, 4, 8, 6], "texture": "#0"}, "east": {"uv": [4, 4, 8, 6], "texture": "#0"}, "south": {"uv": [4, 4, 8, 6], "texture": "#0"}, "west": {"uv": [4, 4, 8, 6], "texture": "#0"}, "up": {"uv": [4, 8, 0, 4], "texture": "#0"}, "down": {"uv": [4, 4, 0, 8], "texture": "#0"}}}, {"from": [2.85195, 1, 9.77164], "to": [4.85195, 2, 11.77164], "rotation": {"angle": -45, "axis": "y", "origin": [3.85195, 1, 10.77164]}, "faces": {"north": {"uv": [4, 4, 8, 6], "texture": "#0"}, "east": {"uv": [4, 4, 8, 6], "texture": "#0"}, "south": {"uv": [4, 4, 8, 6], "texture": "#0"}, "west": {"uv": [4, 4, 8, 6], "texture": "#0"}, "up": {"uv": [4, 8, 0, 4], "texture": "#0"}, "down": {"uv": [4, 4, 0, 8], "texture": "#0"}}}, {"from": [1, 1, 7], "to": [3, 2, 9], "rotation": {"angle": -22.5, "axis": "y", "origin": [5, 0.5, 8]}, "faces": {"north": {"uv": [4, 4, 8, 6], "texture": "#0"}, "east": {"uv": [4, 4, 8, 6], "texture": "#0"}, "south": {"uv": [4, 4, 8, 6], "texture": "#0"}, "west": {"uv": [4, 4, 8, 6], "texture": "#0"}, "up": {"uv": [4, 8, 0, 4], "texture": "#0"}, "down": {"uv": [4, 4, 0, 8], "texture": "#0"}}}, {"from": [3.22417, 0, 4.84568], "to": [5.22417, 1, 6.84568], "rotation": {"angle": 45, "axis": "y", "origin": [5.22417, 0.5, 4.84568]}, "faces": {"north": {"uv": [4, 4, 8, 6], "texture": "#0"}, "east": {"uv": [4, 4, 8, 6], "texture": "#0"}, "south": {"uv": [4, 4, 8, 6], "texture": "#0"}, "west": {"uv": [4, 4, 8, 6], "texture": "#0"}, "up": {"uv": [4, 8, 0, 4], "texture": "#0"}, "down": {"uv": [4, 4, 0, 8], "texture": "#0"}}}, {"from": [3.22417, 1, 4.84568], "to": [5.22417, 2, 6.84568], "rotation": {"angle": 45, "axis": "y", "origin": [5.22417, 0.5, 4.84568]}, "faces": {"north": {"uv": [4, 4, 8, 6], "texture": "#0"}, "east": {"uv": [4, 4, 8, 6], "texture": "#0"}, "south": {"uv": [4, 4, 8, 6], "texture": "#0"}, "west": {"uv": [4, 4, 8, 6], "texture": "#0"}, "up": {"uv": [4, 8, 0, 4], "texture": "#0"}, "down": {"uv": [4, 4, 0, 8], "texture": "#0"}}}, {"from": [1, 0, 7], "to": [3, 1, 9], "rotation": {"angle": -22.5, "axis": "y", "origin": [5, 0.5, 8]}, "faces": {"north": {"uv": [4, 4, 8, 6], "texture": "#0"}, "east": {"uv": [4, 4, 8, 6], "texture": "#0"}, "south": {"uv": [4, 4, 8, 6], "texture": "#0"}, "west": {"uv": [4, 4, 8, 6], "texture": "#0"}, "up": {"uv": [4, 8, 0, 4], "texture": "#0"}, "down": {"uv": [4, 4, 0, 8], "texture": "#0"}}}, {"from": [3.14178, 0, 2.23255], "to": [5.14178, 1, 4.23255], "rotation": {"angle": 22.5, "axis": "y", "origin": [4.14178, 0.5, 2.23255]}, "faces": {"north": {"uv": [4, 4, 8, 6], "texture": "#0"}, "east": {"uv": [4, 4, 8, 6], "texture": "#0"}, "south": {"uv": [4, 4, 8, 6], "texture": "#0"}, "west": {"uv": [4, 4, 8, 6], "texture": "#0"}, "up": {"uv": [4, 8, 0, 4], "rotation": 90, "texture": "#0"}, "down": {"uv": [4, 4, 0, 8], "rotation": 270, "texture": "#0"}}}, {"from": [4, 2, 10], "to": [6, 3, 12], "rotation": {"angle": -22.5, "axis": "y", "origin": [5, 0.5, 8]}, "faces": {"north": {"uv": [4, 4, 8, 6], "texture": "#0"}, "east": {"uv": [4, 4, 8, 6], "texture": "#0"}, "south": {"uv": [4, 4, 8, 6], "texture": "#0"}, "west": {"uv": [4, 4, 8, 6], "texture": "#0"}, "up": {"uv": [4, 8, 0, 4], "texture": "#0"}, "down": {"uv": [4, 4, 0, 8], "texture": "#0"}}}, {"from": [2.85195, 3, 9.77164], "to": [4.85195, 4, 11.77164], "rotation": {"angle": -45, "axis": "y", "origin": [3.85195, 3.5, 10.77164]}, "faces": {"north": {"uv": [4, 4, 8, 6], "texture": "#0"}, "east": {"uv": [4, 4, 8, 6], "texture": "#0"}, "south": {"uv": [4, 4, 8, 6], "texture": "#0"}, "west": {"uv": [4, 4, 8, 6], "texture": "#0"}, "up": {"uv": [4, 8, 0, 4], "texture": "#0"}, "down": {"uv": [4, 4, 0, 8], "texture": "#0"}}}, {"from": [10, 0, 9], "to": [12, 1, 11], "rotation": {"angle": 45, "axis": "y", "origin": [10, 0.5, 8]}, "faces": {"north": {"uv": [4, 8, 8, 10], "texture": "#0"}, "east": {"uv": [4, 8, 8, 10], "texture": "#0"}, "south": {"uv": [4, 8, 8, 10], "texture": "#0"}, "west": {"uv": [4, 8, 8, 10], "texture": "#0"}, "up": {"uv": [4, 12, 0, 8], "texture": "#0"}, "down": {"uv": [4, 8, 0, 12], "texture": "#0"}}}, {"from": [10, 1, 9], "to": [12, 2, 11], "rotation": {"angle": 45, "axis": "y", "origin": [10, 0.5, 8]}, "faces": {"north": {"uv": [4, 8, 8, 10], "texture": "#0"}, "east": {"uv": [4, 8, 8, 10], "texture": "#0"}, "south": {"uv": [4, 8, 8, 10], "texture": "#0"}, "west": {"uv": [4, 8, 8, 10], "texture": "#0"}, "up": {"uv": [4, 12, 0, 8], "texture": "#0"}, "down": {"uv": [4, 8, 0, 12], "texture": "#0"}}}, {"from": [10, 2, 9], "to": [12, 3, 11], "rotation": {"angle": 45, "axis": "y", "origin": [10, 0.5, 8]}, "faces": {"north": {"uv": [4, 8, 8, 10], "texture": "#0"}, "east": {"uv": [4, 8, 8, 10], "texture": "#0"}, "south": {"uv": [4, 8, 8, 10], "texture": "#0"}, "west": {"uv": [4, 8, 8, 10], "texture": "#0"}, "up": {"uv": [4, 12, 0, 8], "texture": "#0"}, "down": {"uv": [4, 8, 0, 12], "texture": "#0"}}}, {"from": [13.24264, 0, 5.58579], "to": [15.24264, 1, 7.58579], "rotation": {"angle": -22.5, "axis": "y", "origin": [14.24264, 1.5, 6.58579]}, "faces": {"north": {"uv": [4, 8, 8, 10], "texture": "#0"}, "east": {"uv": [4, 8, 8, 10], "texture": "#0"}, "south": {"uv": [4, 8, 8, 10], "texture": "#0"}, "west": {"uv": [4, 8, 8, 10], "texture": "#0"}, "up": {"uv": [4, 12, 0, 8], "rotation": 270, "texture": "#0"}, "down": {"uv": [4, 8, 0, 12], "rotation": 90, "texture": "#0"}}}, {"from": [13.24264, 1, 5.58579], "to": [15.24264, 2, 7.58579], "rotation": {"angle": -22.5, "axis": "y", "origin": [14.24264, 1.5, 6.58579]}, "faces": {"north": {"uv": [4, 8, 8, 10], "texture": "#0"}, "east": {"uv": [4, 8, 8, 10], "texture": "#0"}, "south": {"uv": [4, 8, 8, 10], "texture": "#0"}, "west": {"uv": [4, 8, 8, 10], "texture": "#0"}, "up": {"uv": [4, 12, 0, 8], "rotation": 270, "texture": "#0"}, "down": {"uv": [4, 8, 0, 12], "rotation": 90, "texture": "#0"}}}, {"from": [13.24264, 2, 5.58579], "to": [15.24264, 3, 7.58579], "rotation": {"angle": 22.5, "axis": "y", "origin": [14.24264, 1.5, 6.58579]}, "faces": {"north": {"uv": [4, 8, 8, 10], "texture": "#0"}, "east": {"uv": [4, 8, 8, 10], "texture": "#0"}, "south": {"uv": [4, 8, 8, 10], "texture": "#0"}, "west": {"uv": [4, 8, 8, 10], "texture": "#0"}, "up": {"uv": [4, 12, 0, 8], "texture": "#0"}, "down": {"uv": [4, 8, 0, 12], "texture": "#0"}}}, {"from": [10.70711, 0, 4.46447], "to": [12.70711, 1, 6.46447], "rotation": {"angle": -22.5, "axis": "y", "origin": [10.70711, 0.5, 4.46447]}, "faces": {"north": {"uv": [4, 8, 8, 10], "texture": "#0"}, "east": {"uv": [4, 8, 8, 10], "texture": "#0"}, "south": {"uv": [4, 8, 8, 10], "texture": "#0"}, "west": {"uv": [4, 8, 8, 10], "texture": "#0"}, "up": {"uv": [4, 12, 0, 8], "rotation": 270, "texture": "#0"}, "down": {"uv": [4, 8, 0, 12], "rotation": 90, "texture": "#0"}}}], "gui_light": "front", "display": {"thirdperson_righthand": {"translation": [0, 4, 0], "scale": [0.65, 0.65, 0.65]}, "thirdperson_lefthand": {"translation": [0, 4, 0], "scale": [0.65, 0.65, 0.65]}, "firstperson_righthand": {"translation": [0, 5, 0], "scale": [0.65, 0.65, 0.65]}, "firstperson_lefthand": {"translation": [0, 5, 0], "scale": [0.65, 0.65, 0.65]}, "ground": {"translation": [0, 2.5, 0], "scale": [0.61, 0.61, 0.61]}, "gui": {"rotation": [39, 120, 0], "translation": [0, 5.25, 0]}, "head": {"translation": [0, -35.25, 0]}, "fixed": {"rotation": [-90, 0, 0], "translation": [0, 0, -15.25], "scale": [2, 2, 2]}}}