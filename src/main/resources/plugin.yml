name: DownButNotOut
version: 1.1
main: me.rainstxrm.downbutnotout.DownButNotOut
authors: [DylCru, xef5000]
softdepend:
  - nickapi
api-version: 1.18
commands:
  setreviveclicks:
    description: Sets the amount of clicks needed to revive a player
    usage: /<command>
  dbnoreloadconfig:
    description: Reloads the plugin's config
    usage: /<command>
  setbleedouttime:
    description: Sets the amount of time (in seconds) before the player bleeds out (dies)
    usage: /<command>
  reviveplayer:
    description: Revives a downed player
    usage: /<command>
  removedbnostands:
    description: Removes plugin related armourstands
    usage: /<commands>
