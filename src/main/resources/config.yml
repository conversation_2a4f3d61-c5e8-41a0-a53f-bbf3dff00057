# AriesCoinflip Configuration

# Main GUI Configuration
gui:
  title: "&6&lCoinflip"
  rows: 6

  # Static Items Configuration
  items:
    # Statistics Item (Top Right Corner)
    statistics:
      enabled: true
      slot: 8
      material: PLAYER_HEAD
      name: "&a&lYour Statistics"
      lore:
        - "&7Click to view your statistics"
        - "&7"
        - "&7Games Played: &e{games_played}"
        - "&7Games Won: &a{games_won}"
        - "&7Games Lost: &c{games_lost}"
        - "&7Win Rate: &e{win_rate}%"

    # Close Button (Bottom Left Corner)
    close:
      enabled: true
      slot: 45
      material: BARRIER
      name: "&c&lClose Menu"
      lore:
        - "&7Click to close the menu"

    # Coinflip Offer Item (Used for displaying coinflip offers)
    offer-item:
      enabled: true
      material: PLAYER_HEAD
      name: "&e{player}'s Coinflip"
      lore:
        - "&7Amount: &a${amount}"
        - "&7Time remaining: &e{time_remaining}"
        - "&7"
        - "&aClick to accept"

  # Decorative Items
  decorative:
    # Tutorial Item (Slot 0)
    tutorial:
      enabled: true
      slot: 0
      material: BOOK
      name: "&6&lCreate a Coinflip"
      lore:
        - "&f| To create a coinflip, use the command"
        - "&f| /&6coinflip create &f[&amoney&f] [&6amount&f]"
        - "&r"
        - "&f| The minimum bet amount are"
        - "&f| &aMoney: $1000"
        - "&7To accept a coinflip:"
        - "&7- Click on a player's head"
        - "&7- Confirm your bet"
        - "&7"
        - "&7Good luck!"

    # Information Item
    info:
      enabled: true
      slot: 4
      material: PAPER
      name: "&b&lCoinflip"
      lore:
        - "&7| Coinflip for money!"
        - "&7| Minimum bet: $1000"
        - "&7| Maximum bet: $1,000,000"
        - "&7| Tax: 5%"
        - "&7| Timeout: 5 minutes"
        - "&7| To create a coinflip, use the command"
        - "&7| /&6coinflip create &f[&amoney&f] [&6amount&f]"

    # Example decorative item 1
    item1:
      enabled: true
      slot: 1
      material: GOLD_INGOT
      name: "&6&lGood luck!"
      lore:
        - "&r"

    # Border items (top and bottom rows)
    border:
      enabled: true
      slots: [2, 3, 5, 6, 7, 46, 47, 48, 49, 50, 51, 52, 53]
      material: BLACK_STAINED_GLASS_PANE
      name: " "
      lore: []

# Coinflip Settings
settings:
  # Minimum bet amount
  min_bet: 1000

  # Maximum bet amount
  max_bet: 1000000

  # Percentage of tax to take from the winner
  tax_percentage: 5

  # Time in seconds before a coinflip offer expires
  offer_timeout: 300

  # Sound effects
  sounds:
    open_menu: BLOCK_CHEST_OPEN
    close_menu: BLOCK_CHEST_CLOSE
    create_coinflip: ENTITY_EXPERIENCE_ORB_PICKUP
    accept_coinflip: ENTITY_PLAYER_LEVELUP
    win_coinflip: ENTITY_PLAYER_LEVELUP
    lose_coinflip: ENTITY_VILLAGER_NO

# Database Configuration
database:
  # Database type: sqlite or mysql
  type: "sqlite"

  # MySQL Configuration (only used if type is mysql)
  mysql:
    host: "localhost"
    port: 3306
    database: "coinflip"
    username: "root"
    password: "password"

# Messages
messages:
  prefix: "&6[&eCoinflip&6] "
  no_permission: "&cYou don't have permission to use this command."
  no_permission_create: "&cYou don't have permission to create coinflips."
  no_permission_reload: "&cYou don't have permission to reload the config."
  config_reloaded: "&aConfiguration reloaded successfully."
  created_coinflip: "&aYou created a coinflip for &e${amount}."
  accepted_coinflip: "&aYou accepted &e{player}'s &acoinflip for &e${amount}."
  won_coinflip: "&aYou won &e${amount} &afrom &e{player}!"
  lost_coinflip: "&cYou lost &e${amount} &cto &e{player}."
  insufficient_funds: "&cYou don't have enough money."
  minimum_bet: "&cThe minimum bet is &e${min_bet}."
  maximum_bet: "&cThe maximum bet is &e${max_bet}."
  coinflip_expired: "&cYour coinflip offer has expired."
  coinflip_cancelled: "&aYour coinflip offer has been cancelled."
