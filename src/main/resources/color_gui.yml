# Color Selection GUI Configuration
# This GUI is shown when players create or accept coinflips to select their color

gui:
  title: "&6&lSelect Your Color"
  rows: 3

# Available colors for coinflip selection
colors:
  red:
    enabled: true
    slot: 10
    material: RED_WOOL
    name: "&c&lRed"
    lore:
      - "&7Click to select red"
    glass_pane: RED_STAINED_GLASS_PANE
    
  blue:
    enabled: true
    slot: 11
    material: BLUE_WOOL
    name: "&9&lBlue"
    lore:
      - "&7Click to select blue"
    glass_pane: BLUE_STAINED_GLASS_PANE
    
  green:
    enabled: true
    slot: 12
    material: GREEN_WOOL
    name: "&a&lGreen"
    lore:
      - "&7Click to select green"
    glass_pane: GREEN_STAINED_GLASS_PANE
    
  yellow:
    enabled: true
    slot: 13
    material: YELLOW_WOOL
    name: "&e&lYellow"
    lore:
      - "&7Click to select yellow"
    glass_pane: YELLOW_STAINED_GLASS_PANE
    
  orange:
    enabled: true
    slot: 14
    material: ORANGE_WOOL
    name: "&6&lOrange"
    lore:
      - "&7Click to select orange"
    glass_pane: ORANGE_STAINED_GLASS_PANE
    
  purple:
    enabled: true
    slot: 15
    material: PURPLE_WOOL
    name: "&5&lPurple"
    lore:
      - "&7Click to select purple"
    glass_pane: PURPLE_STAINED_GLASS_PANE
    
  pink:
    enabled: true
    slot: 16
    material: PINK_WOOL
    name: "&d&lPink"
    lore:
      - "&7Click to select pink"
    glass_pane: PINK_STAINED_GLASS_PANE
    
  white:
    enabled: true
    slot: 19
    material: WHITE_WOOL
    name: "&f&lWhite"
    lore:
      - "&7Click to select white"
    glass_pane: WHITE_STAINED_GLASS_PANE
    
  black:
    enabled: true
    slot: 20
    material: BLACK_WOOL
    name: "&8&lBlack"
    lore:
      - "&7Click to select black"
    glass_pane: BLACK_STAINED_GLASS_PANE

# Decorative items
decorative:
  # Border items
  border:
    enabled: true
    slots: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 21, 22, 23, 24, 25, 26]
    material: BLACK_STAINED_GLASS_PANE
    name: " "
    lore: []

# Special items for accepter GUI
accepter_items:
  # Shows the creator's selected color
  creator_color:
    slot: 4
    name: "&e{creator_name}'s Color"
    lore:
      - "&7{creator_name} selected &f{color_name}"
      - "&7"
      - "&7Choose your color below"
      
  # Confirmation button (appears after color selection)
  confirm:
    slot: 22
    material: GREEN_WOOL
    name: "&a&lStart Coinflip!"
    lore:
      - "&7Click to begin the coinflip"
      - "&7with your selected colors"
