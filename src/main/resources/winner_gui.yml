# Winner Animation GUI Configuration
# This GUI shows the winner animation after the coinflip

gui:
  title: "&a&l{winner_name} Wins!"
  # Note: rows is inherited from rolling_gui (same size)

# Animation settings
animation:
  # Duration of the winner animation in ticks (20 ticks = 1 second)
  duration_ticks: 120
  
  # How often to add new glass panes (in ticks)
  fill_interval: 8
  
  # Maximum loops of the animation (0 = infinite until closed)
  max_loops: 3
  
  # Auto-close GUI after animation completes (in ticks, 0 = don't auto-close)
  auto_close_delay: 60

# Winner display
winner_display:
  # Winner's head position (center)
  head_slot: 13
  head_name: "&a&l{winner_name} Wins!"
  head_lore:
    - "&7Won: &a${amount}"
    - "&7Color: &f{winner_color}"
    - "&7"
    - "&7Congratulations!"

# Animation pattern for filling the GUI
# The animation starts from corners and moves inward
fill_pattern:
  # 27-slot GUI (3 rows) fill pattern
  # Each number represents the order in which slots are filled
  pattern_27:
    0: 1   # Top-left corner
    26: 1  # Bottom-right corner
    1: 2   # Next to top-left
    25: 2  # Next to bottom-right
    2: 3
    24: 3
    3: 4
    23: 4
    4: 5
    22: 5
    5: 6
    21: 6
    6: 7
    20: 7
    7: 8
    19: 8
    8: 9
    18: 9
    9: 10  # Left side
    17: 10 # Right side
    10: 11
    16: 11
    11: 12
    15: 12
    12: 13 # Will be skipped (player head)
    14: 13

# Sound effects during winner animation
sounds:
  # Sound played when each glass pane is placed
  fill_sound: BLOCK_NOTE_BLOCK_CHIME
  fill_volume: 0.3
  fill_pitch: 1.5
  
  # Sound played when animation completes
  complete_sound: ENTITY_FIREWORK_ROCKET_BLAST
  complete_volume: 1.0
  complete_pitch: 1.0
  
  # Sound played when GUI auto-closes
  close_sound: BLOCK_CHEST_CLOSE
  close_volume: 0.8
  close_pitch: 1.0
