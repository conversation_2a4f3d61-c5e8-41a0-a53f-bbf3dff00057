# QuantumCraft Regions Configuration
# This file defines quantum regions and their various states that players can experience
# The plugin automatically manages which state each player sees based on conditions

# Global settings for all regions
global_settings:
  # Default behavior when multiple states are unlocked
  multi_state_resolution: "highest_priority"  # Options: highest_priority, random, cycle_time

  # How often to check state conditions (in ticks, 20 = 1 second)
  condition_check_interval: 100

  # Whether to show debug messages for state changes
  debug_state_changes: false

  # Default messages (can be overridden per region)
  default_entry_message: "&7You have entered a quantum region..."
  default_exit_message: "&7You have left the quantum region."

  # Performance settings
  max_regions_per_check: 5  # Limit regions checked per tick for performance

  # Integration settings
  quest_plugin: "Quests"  # Plugin name for quest integration
  economy_plugin: "Vault"  # Plugin name for economy integration

# Main regions configuration
# The plugin will automatically generate the necessary keys for each region
# Required keys for each region: world, min_point, max_point
# Optional keys: name, priority, default_state_id, entry_message, exit_message, settings, states
# If states are not defined, a default state will be automatically created
regions:


  # Example of a fully configured region
  # my_region:
  #   world: "world"
  #   min_point: {x: 100, y: 64, z: 200}
  #   max_point: {x: 150, y: 100, z: 250}
  #   name: "My Region"  # Optional, defaults to region ID
  #   priority: 10  # Optional, auto-generated based on creation order if not specified
  #   default_state_id: "default"  # Optional, defaults to "default" or "REALITY"
  #   entry_message: "&6Welcome to My Region!"  # Optional
  #   exit_message: "&7You leave My Region."  # Optional
  #   
  #   # Optional region-specific settings
  #   settings:
  #     state_refresh_interval: 200  # How often to re-evaluate states (ticks)
  #     announce_state_changes: true  # Whether to announce state changes
  #     transition_effect: "fade_black"  # Options: none, fade_black, fade_white, particles
  #     transition_duration: 20  # ticks
  #   
  #   # Optional states configuration
  #   # If not specified, a default state will be automatically created
  #   states:
  #     default:
  #       name: "Default State"  # Optional, defaults to state ID
  #       description: "The default state for this region"  # Optional
  #       icon: "minecraft:stone"  # Optional
  #       priority: 1  # Optional, defaults to 1
  #       unlock_conditions: []  # Optional, defaults to empty list (always available)
  #       
  #       # Optional commands to execute when entering/exiting the state
  #       on_enter_commands: []
  #       on_exit_commands: []
  #       
  #       # Optional ambient effects
  #       ambient_effects:
  #         sounds: []
  #         particles: []
