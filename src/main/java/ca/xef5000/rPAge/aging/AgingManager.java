package ca.xef5000.rPAge.aging;

import ca.xef5000.rPAge.RPAge;
import ca.xef5000.rPAge.database.DatabaseManager;
import ca.xef5000.rPAge.database.PlayerData;
import ca.xef5000.rPAge.util.TimeUtil;
import org.bukkit.Bukkit;
import org.bukkit.attribute.Attribute;
import org.bukkit.attribute.AttributeInstance;
import org.bukkit.entity.Player;

import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.logging.Level;

/**
 * Manages the aging process for players.
 */
public class AgingManager {
    private final RPAge plugin;
    private final DatabaseManager databaseManager;
    private final Random random;

    private double sizeMin;
    private double sizeMax;
    private double sizeIncMin;
    private double sizeIncMax;
    private double biasMin;
    private double biasMax;
    private long agingInterval;

    /**
     * Creates a new AgingManager instance.
     *
     * @param plugin The plugin instance
     * @param databaseManager The database manager
     */
    public AgingManager(RPAge plugin, DatabaseManager databaseManager) {
        this.plugin = plugin;
        this.databaseManager = databaseManager;
        this.random = new Random();

        // Load configuration values
        loadConfig();
    }

    // New method to load (or reload) configuration
    private void loadConfig() {
        plugin.reloadConfig(); // Ensure the latest config is used
        this.sizeMin = plugin.getConfig().getDouble("size-min", 0.5);
        this.sizeMax = plugin.getConfig().getDouble("size-max", 2.0);
        this.sizeIncMin = plugin.getConfig().getDouble("size-inc-min", 0.05);
        this.sizeIncMax = plugin.getConfig().getDouble("size-inc-max", 0.15);

        String agingTimeString = plugin.getConfig().getString("aging-time", "1d");
        this.agingInterval = TimeUtil.parseTime(agingTimeString);
        if (this.agingInterval <= 0) {
            plugin.getLogger().warning("Invalid aging-time format: " + agingTimeString + ". Using default of 1 day.");
            this.agingInterval = 24 * 60 * 60 * 1000; // 1 day in milliseconds
        }
    }

    /**
     * Checks if it's time to age players and performs aging if necessary.
     *
     * @return true if aging was performed, false otherwise
     */
    public boolean checkAndPerformAging() {
        plugin.getLogger().info("Checking and performing aging for players...");
        Map<UUID, PlayerData> players = databaseManager.getAllPlayers();
        boolean anyPlayerAged = false;

        for (PlayerData playerData : players.values()) {
            if (TimeUtil.isTimeToAge(playerData.getLastAge(), agingInterval)) {
                agePlayer(playerData);
                anyPlayerAged = true;
            }
        }

        if (anyPlayerAged) {
            plugin.getLogger().info("Aging process complete for applicable players.");
        } else {
            plugin.getLogger().info("No players were due for aging.");
        }
        return anyPlayerAged;
    }

    /**
     * Performs aging for a specific player.
     *
     * @param playerData The data of the player to age
     */
    public void agePlayer(PlayerData playerData) {
        //plugin.getLogger().info("Attempting to age player " + playerData.getName());

        if (playerData.getAge() >= plugin.getConfig().getInt("max-age", 100)) {
            //plugin.getLogger().info("Player " + playerData.getName() + " has reached the maximum age. Skipping aging.");
            return;
        }

        // Increment age
        playerData.incrementAge();

        // Calculate new size
        if (playerData.getAge() < plugin.getConfig().getInt("stop-growing-age", 18)) {
            double sizeIncrease = calculateSizeIncrease();
            double newSize = Math.min(sizeMax, playerData.getSize() + sizeIncrease);
            playerData.setSize(newSize);
        }

        // Update last aged time
        playerData.setLastAge(System.currentTimeMillis());

        // Save updated player data
        databaseManager.savePlayer(
                playerData.getUuid(),
                playerData.getName(),
                playerData.getAge(),
                playerData.getSize(),
                playerData.getLastAge() // Pass the new lastAge
        );

        // Apply size to online player if they're online
        Player player = Bukkit.getPlayer(playerData.getUuid());
        if (player != null && player.isOnline()) {
            applyPlayerSize(player, playerData.getSize());
        }

//        plugin.getLogger().info(String.format(
//                "Aged player %s to age %d with new size %.2f. Last aged timestamp updated.",
//                playerData.getName(),
//                playerData.getAge(),
//                playerData.getSize()
//        ));
    }

    /**
     * Calculates a random size increase within the configured bounds.
     *
     * @return The size increase
     */
    private double calculateSizeIncrease() {
        return sizeIncMin + (random.nextDouble() * (sizeIncMax - sizeIncMin));
    }

    /**
     * Applies the size to a player using the generic.scale attribute.
     *
     * @param player The player
     * @param size The size to apply
     */
    public void applyPlayerSize(Player player, double size) {
        try {
            AttributeInstance scaleAttribute = player.getAttribute(Attribute.GENERIC_SCALE);
            if (scaleAttribute != null) {
                scaleAttribute.setBaseValue(size);
                //.getLogger().info("Applied size " + size + " to player " + player.getName());
            } else {
                plugin.getLogger().warning("Could not get GENERIC_SCALE attribute for player " + player.getName());
            }
        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "Error applying size to player " + player.getName(), e);
        }
    }

    /**
     * Initializes a player in the database if they don't exist yet.
     *
     * @param player The player to initialize
     */
    public void initializePlayer(Player player) {
        PlayerData playerData = databaseManager.getPlayer(player.getUniqueId());

        if (playerData == null) {
            // New player, initialize with default values
            long currentTime = System.currentTimeMillis();
            int initialAge = plugin.getConfig().getInt("starting-age", 0);
            databaseManager.savePlayer(
                    player.getUniqueId(),
                    player.getName(),
                    initialAge, // Initial age
                    sizeMin, // Initial size
                    currentTime // Initial lastAge
            );

            // Apply initial size
            applyPlayerSize(player, sizeMin);

            plugin.getLogger().info("Initialized new player " + player.getName() + " with size " + sizeMin + " and last_age " + currentTime);
        } else {
            // Existing player, apply their current size
            applyPlayerSize(player, playerData.getSize());

            // Update their last seen time and name (in case it changed)
            // Also ensure lastAge is part of the save, even if not explicitly changed here,
            // to maintain data integrity with the savePlayer method signature.
            playerData.updateLastSeen();
            databaseManager.savePlayer(
                    playerData.getUuid(),
                    player.getName(), // Update name in case it changed
                    playerData.getAge(),
                    playerData.getSize(),
                    playerData.getLastAge() // Ensure lastAge is correctly passed
            );
        }
    }

    /**
     * Resets a player's data to the initial state.
     *
     * @param player The player to reset
     */
    public void resetPlayerData(Player player) {
        // Remove existing data first (optional, as savePlayer can overwrite)
        // databaseManager.deletePlayer(player.getUniqueId()); // Already done by the command

        // Initialize with default values, similar to initializePlayer for a new player
        long currentTime = System.currentTimeMillis();
        int initialAge = plugin.getConfig().getInt("starting-age", 0);
        double initialSize = plugin.getConfig().getDouble("size-min", 0.5); // Use size-min for reset

        databaseManager.savePlayer(
                player.getUniqueId(),
                player.getName(),
                initialAge,
                initialSize,
                currentTime // Set lastAge to current time to prevent immediate re-aging
        );

        // Apply initial size
        applyPlayerSize(player, initialSize);

        plugin.getLogger().info("Reset player data for " + player.getName() + ". New age: " + initialAge + ", new size: " + initialSize);
    }

    /**
     * Gets the aging interval in milliseconds.
     *
     * @return The aging interval
     */
    public long getAgingInterval() {
        return agingInterval;
    }
}
