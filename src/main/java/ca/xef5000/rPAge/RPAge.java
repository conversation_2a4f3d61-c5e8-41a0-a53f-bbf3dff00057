package ca.xef5000.rPAge;

import ca.xef5000.rPAge.aging.AgingManager;
import ca.xef5000.rPAge.commands.ReloadCommand;
import ca.xef5000.rPAge.commands.ResetUserCommand;
import ca.xef5000.rPAge.database.DatabaseManager;
import ca.xef5000.rPAge.listeners.PlayerListener;
import ca.xef5000.rPAge.placeholders.RPAgePlaceholders;
import ca.xef5000.rPAge.tasks.AgingTask;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.command.PluginCommand;
import org.bukkit.Bukkit;

public final class RPAge extends JavaPlugin {
    private DatabaseManager databaseManager;
    private AgingManager agingManager;
    private AgingTask agingTask;

    @Override
    public void onEnable() {
        // Save default config if it doesn't exist
        saveDefaultConfig();

        // Initialize database manager
        this.databaseManager = new DatabaseManager(this);

        // Initialize aging manager
        this.agingManager = new AgingManager(this, databaseManager);

        // Register player listener
        PlayerListener playerListener = new PlayerListener(this, databaseManager, agingManager);
        getServer().getPluginManager().registerEvents(playerListener, this);

        // Schedule aging task
        this.agingTask = new AgingTask(this, agingManager);
        agingTask.schedule();

        // Register commands
        PluginCommand reloadCmd = getCommand("rpreload");
        if (reloadCmd != null) {
            reloadCmd.setExecutor(new ReloadCommand(this));
        } else {
            getLogger().severe("Failed to register rpreload command!");
        }

        PluginCommand resetUserCmd = getCommand("resetuser");
        if (resetUserCmd != null) {
            resetUserCmd.setExecutor(new ResetUserCommand(this, databaseManager, agingManager));
        } else {
            getLogger().severe("Failed to register resetuser command!");
        }

        // Register PlaceholderAPI expansion
        if (Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
            new RPAgePlaceholders(this, databaseManager, agingManager).register();
            getLogger().info("Successfully registered PlaceholderAPI placeholders.");
        } else {
            getLogger().info("PlaceholderAPI not found, placeholders will not be available.");
        }

        getLogger().info("RPAge plugin has been enabled!");
    }

    @Override
    public void onDisable() {
        // Close database connection
        if (databaseManager != null) {
            databaseManager.closeConnection();
        }

        getLogger().info("RPAge plugin has been disabled!");
    }

    // Method to reload managers and tasks
    public void reloadManagers() {
        // Reload config from disk
        super.reloadConfig();

        // Re-initialize database manager
        // (Closing old connection and opening new one based on potentially new config)
        if (databaseManager != null) {
            databaseManager.closeConnection();
        }
        this.databaseManager = new DatabaseManager(this);

        // Re-initialize aging manager with new config
        this.agingManager = new AgingManager(this, databaseManager);

        // Cancel existing aging task
        if (this.agingTask != null) {
            this.agingTask.cancel();
        }
        // Schedule new aging task with potentially new interval
        this.agingTask = new AgingTask(this, agingManager);
        this.agingTask.schedule();

        getLogger().info("RPAge managers and tasks have been reloaded.");
    }
}
