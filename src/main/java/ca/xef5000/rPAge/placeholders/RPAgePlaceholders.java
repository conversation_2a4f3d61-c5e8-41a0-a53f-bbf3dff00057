package ca.xef5000.rPAge.placeholders;

import ca.xef5000.rPAge.RPAge;
import ca.xef5000.rPAge.aging.AgingManager;
import ca.xef5000.rPAge.database.DatabaseManager;
import ca.xef5000.rPAge.database.PlayerData;
import ca.xef5000.rPAge.util.TimeUtil;
import me.clip.placeholderapi.expansion.PlaceholderExpansion;
import org.bukkit.OfflinePlayer;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.text.DecimalFormat;

public class RPAgePlaceholders extends PlaceholderExpansion {

    private final RPAge plugin;
    private final DatabaseManager databaseManager;
    private final AgingManager agingManager;
    private final DecimalFormat df = new DecimalFormat("0.00");

    public RPAgePlaceholders(RPAge plugin, DatabaseManager databaseManager, AgingManager agingManager) {
        this.plugin = plugin;
        this.databaseManager = databaseManager;
        this.agingManager = agingManager;
    }

    @Override
    public @NotNull String getIdentifier() {
        return "rpage";
    }

    @Override
    public @NotNull String getAuthor() {
        return String.join(", ", plugin.getPluginMeta().getAuthors());
    }

    @Override
    public @NotNull String getVersion() {
        return plugin.getPluginMeta().getVersion();
    }

    @Override
    public boolean persist() {
        return true; // Placeholder values can change, so we don't want to cache them indefinitely
    }

    @Override
    public @Nullable String onRequest(OfflinePlayer offlinePlayer, @NotNull String params) {
        if (offlinePlayer == null) {
            return null;
        }

        PlayerData playerData = databaseManager.getPlayer(offlinePlayer.getUniqueId());

        if (params.equalsIgnoreCase("age")) {
            return playerData != null ? String.valueOf(playerData.getAge()) : "0";
        }

        if (params.equalsIgnoreCase("size")) {
            return playerData != null ? df.format(playerData.getSize()) : df.format(plugin.getConfig().getDouble("size-min", 0.5));
        }

        if (params.equalsIgnoreCase("last_aged")) {
            return playerData != null ? TimeUtil.formatDuration(System.currentTimeMillis() - playerData.getLastAge()) : "Never";
        }

        if (params.equalsIgnoreCase("next_age")) {
            return playerData != null ?
                    agingManager.getAgingInterval() - (System.currentTimeMillis() - playerData.getLastAge()) > 0 ?
                    TimeUtil.formatDuration(agingManager.getAgingInterval() - (System.currentTimeMillis() - playerData.getLastAge())) :
                            plugin.getConfig().getString("messages.placeholder-now", "Now")
                    : "Unknown";
        }

        return null; // Unknown placeholder
    }
}

