package ca.xef5000.rPAge.commands;

import ca.xef5000.rPAge.RPAge;
import ca.xef5000.rPAge.aging.AgingManager;
import ca.xef5000.rPAge.database.DatabaseManager;
import ca.xef5000.rPAge.database.PlayerData;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class RPageCommand implements CommandExecutor, TabCompleter {
    private final RPAge plugin;
    private final DatabaseManager databaseManager;
    private final AgingManager agingManager;

    public RPageCommand(RPAge plugin, DatabaseManager databaseManager, AgingManager agingManager) {
        this.plugin = plugin;
        this.databaseManager = databaseManager;
        this.agingManager = agingManager;
    }

    @Override
    public boolean onCommand(@NotNull CommandSender sender, @NotNull Command command, @NotNull String label, @NotNull String[] args) {
        if (args.length == 0) {
            sendUsage(sender);
            return true;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "reload":
                handleReload(sender);
                break;
            case "reset":
                handleReset(sender, args);
                break;
            case "age":
                handleAge(sender, args);
                break;
            default:
                sendUsage(sender);
                break;
        }
        return true;
    }

    private void handleReload(CommandSender sender) {
        if (!sender.hasPermission("rpage.reload")) {
            sender.sendMessage(Component.text("You do not have permission to use this command.", NamedTextColor.RED));
            return;
        }
        plugin.reloadManagers();
        sender.sendMessage(Component.text("RPAge configuration and managers reloaded successfully!", NamedTextColor.GREEN));
        plugin.getLogger().info("RPAge configuration reloaded by " + sender.getName());
    }

    private void handleReset(CommandSender sender, String[] args) {
        if (!sender.hasPermission("rpage.reset")) {
            sender.sendMessage(Component.text("You do not have permission to use this command.", NamedTextColor.RED));
            return;
        }

        if (args.length < 2) {
            sender.sendMessage(Component.text("Usage: /rpage reset <player>", NamedTextColor.RED));
            return;
        }

        String playerName = args[1];
        OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(playerName);

        if (!offlinePlayer.hasPlayedBefore() && !offlinePlayer.isOnline()) {
            sender.sendMessage(Component.text("Player " + playerName + " not found or has never played on this server.", NamedTextColor.RED));
            return;
        }

        databaseManager.deletePlayer(offlinePlayer.getUniqueId());

        Player onlinePlayer = offlinePlayer.isOnline() ? (Player) offlinePlayer : null;

        if (onlinePlayer != null) {
            agingManager.resetPlayerData(onlinePlayer);
            sender.sendMessage(Component.text("Player " + playerName + "'s data has been reset. They have been set to the starting age and size.", NamedTextColor.GREEN));
        } else {
            sender.sendMessage(Component.text("Player " + playerName + "'s data has been reset from the database. Their age and size will be reset when they next join.", NamedTextColor.GREEN));
        }
        plugin.getLogger().info("Player " + playerName + " (UUID: " + offlinePlayer.getUniqueId() + ") data has been reset by " + sender.getName());
    }

    private void handleAge(CommandSender sender, String[] args) {
        if (!sender.hasPermission("rpage.age")) {
            sender.sendMessage(Component.text("You do not have permission to use this command.", NamedTextColor.RED));
            return;
        }

        if (args.length < 2) {
            sender.sendMessage(Component.text("Usage: /rpage age <player>", NamedTextColor.RED));
            return;
        }

        String playerName = args[1];
        OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(playerName);
        int howMany = args.length > 2 ? Integer.parseInt(args[2]) : 1;

        if (!offlinePlayer.hasPlayedBefore() && !offlinePlayer.isOnline()) {
            sender.sendMessage(Component.text("Player " + playerName + " not found or has never played on this server.", NamedTextColor.RED));
            return;
        }

        PlayerData playerData = databaseManager.getPlayer(offlinePlayer.getUniqueId());

        if (playerData == null) {
            sender.sendMessage(Component.text("Player " + playerName + " not found in the database.", NamedTextColor.RED));
            return;
        }
        for (int i = 0; i < howMany; i++) {
            plugin.getAgingManager().agePlayer(playerData);
        }

        sender.sendMessage(Component.text("Player " + playerName + " has been aged by " + howMany + " years. New age: " + playerData.getAge(), NamedTextColor.GREEN));
    }

    private void sendUsage(CommandSender sender) {
        sender.sendMessage(Component.text("--- RPAge Commands ---", NamedTextColor.GOLD));
        sender.sendMessage(Component.text("/rpage reload - Reloads the plugin configuration.", NamedTextColor.YELLOW));
        sender.sendMessage(Component.text("/rpage reset <player> - Resets a player's data.", NamedTextColor.YELLOW));
    }

    @Override
    public List<String> onTabComplete(@NotNull CommandSender sender, @NotNull Command command, @NotNull String alias, @NotNull String[] args) {
        if (args.length == 1) {
            List<String> subcommands = new ArrayList<>();
            if (sender.hasPermission("rpage.reload")) {
                subcommands.add("reload");
            }
            if (sender.hasPermission("rpage.reset")) {
                subcommands.add("reset");
            }
            if (sender.hasPermission("rpage.age")) {
                subcommands.add("age");
            }

            return subcommands.stream()
                    .filter(s -> s.startsWith(args[0].toLowerCase()))
                    .collect(Collectors.toList());
        }

        if (args.length == 2 && args[0].equalsIgnoreCase("reset")) {
            if (sender.hasPermission("rpage.reset")) {
                return Bukkit.getOnlinePlayers().stream()
                        .map(Player::getName)
                        .filter(name -> name.toLowerCase().startsWith(args[1].toLowerCase()))
                        .collect(Collectors.toList());
            }
        } else if (args.length == 2 && args[0].equalsIgnoreCase("age")) {
            if (sender.hasPermission("rpage.age")) {
                return Bukkit.getOnlinePlayers().stream()
                        .map(Player::getName)
                        .filter(name -> name.toLowerCase().startsWith(args[1].toLowerCase()))
                        .collect(Collectors.toList());
            }
        }
        return new ArrayList<>();
    }
}

