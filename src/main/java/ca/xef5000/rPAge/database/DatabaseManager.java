package ca.xef5000.rPAge.database;

import ca.xef5000.rPAge.RPAge;
import org.bukkit.configuration.ConfigurationSection;

import java.io.File;
import java.sql.*;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.logging.Level;

public class DatabaseManager {
    private final RPAge plugin;
    private Connection connection;
    private final String dbType;
    private final String sqliteFile;
    private final String mysqlHost;
    private final int mysqlPort;
    private final String mysqlDatabase;
    private final String mysqlUsername;
    private final String mysqlPassword;

    public DatabaseManager(RPAge plugin) {
        this.plugin = plugin;

        ConfigurationSection dbConfig = plugin.getConfig().getConfigurationSection("database");
        this.dbType = dbConfig.getString("type", "sqlite");

        // SQLite settings
        this.sqliteFile = dbConfig.getConfigurationSection("sqlite").getString("file", "players.db");

        // MySQL settings
        ConfigurationSection mysqlConfig = dbConfig.getConfigurationSection("mysql");
        this.mysqlHost = mysqlConfig.getString("host", "localhost");
        this.mysqlPort = mysqlConfig.getInt("port", 3306);
        this.mysqlDatabase = mysqlConfig.getString("database", "rpage");
        this.mysqlUsername = mysqlConfig.getString("username", "root");
        this.mysqlPassword = mysqlConfig.getString("password", "password");

        // Initialize database
        initializeDatabase();
    }

    private void initializeDatabase() {
        try {
            if (connection != null && !connection.isClosed()) {
                return;
            }

            if (dbType.equalsIgnoreCase("mysql")) {
                // MySQL connection
                Class.forName("com.mysql.jdbc.Driver");
                connection = DriverManager.getConnection(
                        "jdbc:mysql://" + mysqlHost + ":" + mysqlPort + "/" + mysqlDatabase,
                        mysqlUsername, mysqlPassword);
            } else {
                // SQLite connection
                Class.forName("org.sqlite.JDBC");
                File dataFolder = new File(plugin.getDataFolder(), sqliteFile);
                connection = DriverManager.getConnection("jdbc:sqlite:" + dataFolder);
            }

            // Create tables if they don't exist
            createTables();

        } catch (SQLException | ClassNotFoundException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to initialize database", e);
        }
    }

    private void createTables() {
        try (Statement statement = connection.createStatement()) {
            int minAge = plugin.getConfig().getInt("starting-age", 0);
            // Create players table
            statement.execute(
                    "CREATE TABLE IF NOT EXISTS players (" +
                    "uuid VARCHAR(36) PRIMARY KEY, " +
                    "name VARCHAR(16) NOT NULL, " +
                    "age INT NOT NULL DEFAULT " + minAge + ", " +
                    "size DOUBLE NOT NULL, " +
                    "last_seen BIGINT NOT NULL," +
                    "last_age BIGINT NOT NULL DEFAULT 0," +
                    "size_bias DOUBLE NOT NULL DEFAULT 0.5" +
                    ");"
            );
        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to create database tables", e);
        }
    }

    public void closeConnection() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Error closing database connection", e);
        }
    }

    public void savePlayer(UUID uuid, String name, int age, double size, long lastAge) {
        try {
            initializeDatabase(); // Ensure connection is valid

            String sql = "INSERT OR REPLACE INTO players (uuid, name, age, size, last_seen, last_age) VALUES (?, ?, ?, ?, ?, ?)";
            if (dbType.equalsIgnoreCase("mysql")) {
                sql = "INSERT INTO players (uuid, name, age, size, last_seen, last_age) VALUES (?, ?, ?, ?, ?, ?) " +
                      "ON DUPLICATE KEY UPDATE name = VALUES(name), age = VALUES(age), size = VALUES(size), last_seen = VALUES(last_seen), last_age = VALUES(last_age)";
            }

            try (PreparedStatement statement = connection.prepareStatement(sql)) {
                statement.setString(1, uuid.toString());
                statement.setString(2, name);
                statement.setInt(3, age);
                statement.setDouble(4, size);
                statement.setLong(5, System.currentTimeMillis());
                statement.setLong(6, lastAge);
                statement.executeUpdate();
            }
        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to save player data", e);
        }
    }

    public Map<UUID, PlayerData> getAllPlayers() {
        Map<UUID, PlayerData> players = new HashMap<>();

        try {
            initializeDatabase(); // Ensure connection is valid

            String sql = "SELECT uuid, name, age, size, last_seen, last_age FROM players";
            try (PreparedStatement statement = connection.prepareStatement(sql);
                 ResultSet resultSet = statement.executeQuery()) {

                while (resultSet.next()) {
                    UUID uuid = UUID.fromString(resultSet.getString("uuid"));
                    String name = resultSet.getString("name");
                    int age = resultSet.getInt("age");
                    double size = resultSet.getDouble("size");
                    long lastSeen = resultSet.getLong("last_seen");
                    long lastAge = resultSet.getLong("last_age");

                    players.put(uuid, new PlayerData(uuid, name, age, size, lastSeen, lastAge));
                }
            }
        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to retrieve player data", e);
        }

        return players;
    }

    public PlayerData getPlayer(UUID uuid) {
        try {
            initializeDatabase(); // Ensure connection is valid

            String sql = "SELECT uuid, name, age, size, last_seen, last_age FROM players WHERE uuid = ?";
            try (PreparedStatement statement = connection.prepareStatement(sql)) {
                statement.setString(1, uuid.toString());

                try (ResultSet resultSet = statement.executeQuery()) {
                    if (resultSet.next()) {
                        String name = resultSet.getString("name");
                        int age = resultSet.getInt("age");
                        double size = resultSet.getDouble("size");
                        long lastSeen = resultSet.getLong("last_seen");
                        long lastAge = resultSet.getLong("last_age");

                        return new PlayerData(uuid, name, age, size, lastSeen, lastAge);
                    }
                }
            }
        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to retrieve player data", e);
        }

        return null;
    }

    public void deletePlayer(UUID uuid) {
        try {
            initializeDatabase(); // Ensure connection is valid
            String sql = "DELETE FROM players WHERE uuid = ?";
            try (PreparedStatement statement = connection.prepareStatement(sql)) {
                statement.setString(1, uuid.toString());
                statement.executeUpdate();
                plugin.getLogger().info("Deleted player data for UUID: " + uuid.toString());
            }
        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to delete player data for UUID: " + uuid.toString(), e);
        }
    }
}
