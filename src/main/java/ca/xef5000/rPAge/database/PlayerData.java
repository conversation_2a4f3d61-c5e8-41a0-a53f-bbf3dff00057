package ca.xef5000.rPAge.database;

import java.util.UUID;

/**
 * Represents player data for the RPAge plugin.
 */
public class PlayerData {
    private final UUID uuid;
    private final String name;
    private int age;
    private double size;
    private long lastSeen;
    private long lastAge; // Added field
    private double sizeBias; // Size bias affects how much a player grows

    /**
     * Creates a new PlayerData instance.
     *
     * @param uuid     The player's UUID
     * @param name     The player's name
     * @param age      The player's age
     * @param size     The player's size (scale attribute)
     * @param lastSeen The timestamp when the player was last seen
     * @param lastAge  The timestamp when the player last aged
     */
    public PlayerData(UUID uuid, String name, int age, double size, long lastSeen, long lastAge) { // Added lastAge to constructor
        this.uuid = uuid;
        this.name = name;
        this.age = age;
        this.size = size;
        this.lastSeen = lastSeen;
        this.lastAge = lastAge; // Initialize lastAge
    }

    /**
     * Gets the player's UUID.
     *
     * @return The player's UUID
     */
    public UUID getUuid() {
        return uuid;
    }

    /**
     * Gets the player's name.
     *
     * @return The player's name
     */
    public String getName() {
        return name;
    }

    /**
     * Gets the player's age.
     *
     * @return The player's age
     */
    public int getAge() {
        return age;
    }

    /**
     * Sets the player's age.
     *
     * @param age The new age
     */
    public void setAge(int age) {
        this.age = age;
    }

    /**
     * Gets the player's size (scale attribute).
     *
     * @return The player's size
     */
    public double getSize() {
        return size;
    }

    /**
     * Sets the player's size (scale attribute).
     *
     * @param size The new size
     */
    public void setSize(double size) {
        this.size = size;
    }

    /**
     * Gets the timestamp when the player was last seen.
     *
     * @return The last seen timestamp
     */
    public long getLastSeen() {
        return lastSeen;
    }

    /**
     * Updates the last seen timestamp to the current time.
     */
    public void updateLastSeen() {
        this.lastSeen = System.currentTimeMillis();
    }

    /**
     * Sets the last seen timestamp.
     *
     * @param lastSeen The new last seen timestamp
     */
    public void setLastSeen(long lastSeen) {
        this.lastSeen = lastSeen;
    }

    /**
     * Gets the timestamp when the player last aged.
     *
     * @return The last aged timestamp
     */
    public long getLastAge() {
        return lastAge;
    }

    /**
     * Sets the timestamp when the player last aged.
     *
     * @param lastAge The new last aged timestamp
     */
    public void setLastAge(long lastAge) {
        this.lastAge = lastAge;
    }

    /**
     * Increments the player's age by 1.
     */
    public void incrementAge() {
        this.age++;
    }
}
