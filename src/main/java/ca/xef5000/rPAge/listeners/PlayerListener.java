package ca.xef5000.rPAge.listeners;

import ca.xef5000.rPAge.RPAge;
import ca.xef5000.rPAge.aging.AgingManager;
import ca.xef5000.rPAge.database.DatabaseManager;
import ca.xef5000.rPAge.database.PlayerData;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;

/**
 * Listener for player-related events.
 */
public class PlayerListener implements Listener {
    private final RPAge plugin;
    private final DatabaseManager databaseManager;
    private final AgingManager agingManager;

    /**
     * Creates a new PlayerListener instance.
     *
     * @param plugin The plugin instance
     * @param databaseManager The database manager
     * @param agingManager The aging manager
     */
    public PlayerListener(RPAge plugin, DatabaseManager databaseManager, AgingManager agingManager) {
        this.plugin = plugin;
        this.databaseManager = databaseManager;
        this.agingManager = agingManager;
    }

    /**
     * Handles player join events.
     *
     * @param event The player join event
     */
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Initialize the player in the database and apply their size
        agingManager.initializePlayer(player);
        
        plugin.getLogger().info("Player " + player.getName() + " joined and was initialized");
    }

    /**
     * Handles player quit events.
     *
     * @param event The player quit event
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        PlayerData playerData = databaseManager.getPlayer(player.getUniqueId());
        
        if (playerData != null) {
            // Update the player's last seen time
            playerData.updateLastSeen();
            databaseManager.savePlayer(
                    playerData.getUuid(),
                    playerData.getName(),
                    playerData.getAge(),
                    playerData.getSize()
            );
            
            plugin.getLogger().info("Updated last seen time for player " + player.getName());
        }
    }
}