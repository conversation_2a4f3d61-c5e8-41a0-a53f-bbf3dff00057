package ca.xef5000.rPAge.tasks;

import ca.xef5000.rPAge.RPAge;
import ca.xef5000.rPAge.aging.AgingManager;

/**
 * Task that periodically checks if it's time to age players.
 */
public class AgingTask implements Runnable {
    private final RPAge plugin;
    private final AgingManager agingManager;

    /**
     * Creates a new AgingTask instance.
     *
     * @param plugin The plugin instance
     * @param agingManager The aging manager
     */
    public AgingTask(RPAge plugin, AgingManager agingManager) {
        this.plugin = plugin;
        this.agingManager = agingManager;
    }

    @Override
    public void run() {
        plugin.getLogger().info("Checking if it's time to age players...");
        
        boolean aged = agingManager.checkAndPerformAging();
        
        if (aged) {
            plugin.getLogger().info("Players have been aged successfully.");
        } else {
            plugin.getLogger().info("It's not time to age players yet.");
        }
    }

    /**
     * Schedules this task to run periodically.
     */
    public void schedule() {
        // Get the check interval from config (in minutes)
        int checkIntervalMinutes = plugin.getConfig().getInt("check-interval", 15);
        long checkIntervalTicks = checkIntervalMinutes * 60 * 20; // Convert minutes to ticks (20 ticks = 1 second)
        
        // Schedule the task to run periodically
        plugin.getServer().getScheduler().runTaskTimer(plugin, this, 20 * 60, checkIntervalTicks); // Start after 1 minute
        
        plugin.getLogger().info("Aging task scheduled to run every " + checkIntervalMinutes + " minutes.");
    }
}