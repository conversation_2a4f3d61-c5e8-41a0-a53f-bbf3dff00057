package ca.xef5000.visualSheath.utils;

import ca.xef5000.visualSheath.VisualSheath;
import dev.lone.itemsadder.api.CustomStack;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;

/**
 * Utility class for ItemsAdder integration
 */
public class ItemsAdderIntegration {
    
    private static boolean itemsAdderEnabled = false;
    private static VisualSheath plugin;
    
    /**
     * Initialize the ItemsAdder integration
     * @param plugin The VisualSheath plugin instance
     */
    public static void initialize(VisualSheath plugin) {
        ItemsAdderIntegration.plugin = plugin;
        
        // Check if ItemsAdder is present
        if (Bukkit.getPluginManager().getPlugin("ItemsAdder") != null) {
            itemsAdderEnabled = true;
            plugin.getLogger().info("ItemsAdder found! Integration enabled.");
        } else {
            plugin.getLogger().info("ItemsAdder not found. Integration disabled.");
        }
    }
    
    /**
     * Check if ItemsAdder is enabled
     * @return true if ItemsAdder is enabled
     */
    public static boolean isEnabled() {
        return itemsAdderEnabled;
    }
    
    /**
     * Check if an item is a custom ItemsAdder item
     * @param item The item to check
     * @return true if the item is a custom ItemsAdder item
     */
    public static boolean isCustomItem(ItemStack item) {
        if (!itemsAdderEnabled || item == null || item.getType() == Material.AIR) {
            return false;
        }
        
        try {
            // Use reflection to avoid direct dependency
            CustomStack customStack = CustomStack.byItemStack(item);
            return customStack != null;
        } catch (Exception e) {
            plugin.getLogger().warning("Error checking if item is a custom ItemsAdder item: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Get the namespace ID of a custom ItemsAdder item
     * @param item The item to get the namespace ID for
     * @return The namespace ID, or null if not a custom item
     */
    public static String getNamespacedID(ItemStack item) {
        if (!itemsAdderEnabled || item == null || item.getType() == Material.AIR) {
            return null;
        }
        
        try {
            // Use reflection to avoid direct dependency
            Class<?> customStackClass = Class.forName("dev.lone.itemsadder.api.CustomStack");
            Object customStack = customStackClass.getMethod("byItemStack", ItemStack.class).invoke(null, item);
            
            if (customStack == null) {
                return null;
            }
            
            return (String) customStackClass.getMethod("getNamespacedID").invoke(customStack);
        } catch (Exception e) {
            plugin.getLogger().warning("Error getting namespaced ID for custom ItemsAdder item: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Get a custom ItemsAdder item by its namespace ID
     * @param namespacedID The namespace ID of the item
     * @return The ItemStack, or null if not found
     */
    public static ItemStack getItemByNamespacedID(String namespacedID) {
        if (!itemsAdderEnabled || namespacedID == null) {
            return null;
        }
        
        try {
            // Use reflection to avoid direct dependency
            Class<?> customStackClass = Class.forName("dev.lone.itemsadder.api.CustomStack");
            Object customStack = customStackClass.getMethod("getInstance", String.class).invoke(null, namespacedID);
            
            if (customStack == null) {
                return null;
            }
            
            return (ItemStack) customStackClass.getMethod("getItemStack").invoke(customStack);
        } catch (Exception e) {
            plugin.getLogger().warning("Error getting custom ItemsAdder item by namespaced ID: " + e.getMessage());
            return null;
        }
    }
}