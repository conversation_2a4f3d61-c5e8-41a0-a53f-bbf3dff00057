package ca.xef5000.visualSheath.managers;

import ca.xef5000.visualSheath.VisualSheath;
import ca.xef5000.visualSheath.events.SheathableItemsCheckEvent;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.inventory.ItemStack;
import org.bukkit.Material;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Queue;
import java.util.LinkedList;
import java.util.List;
import java.util.ArrayList;

/**
 * Manages tracking of player inventories for sheathable items
 */
public class InventoryTrackingManager {
    private final VisualSheath plugin;
    private final Map<UUID, Boolean> playerSheathableStatus = new ConcurrentHashMap<>();
    private int taskId = -1;
    private int batchSize = 20; // Process 20 players per tick
    private int checkInterval = 40; // Check every 2 seconds (40 ticks)

    /**
     * Constructor for InventoryTrackingManager
     * @param plugin The VisualSheath plugin instance
     */
    public InventoryTrackingManager(VisualSheath plugin) {
        this.plugin = plugin;
        
        // Load configuration values
        this.batchSize = plugin.getConfig().getInt("performance.batch-size", 20);
        this.checkInterval = plugin.getConfig().getInt("performance.check-interval", 40);
    }

    /**
     * Start tracking player inventories
     */
    public void startTracking() {
        // Cancel any existing task
        stopTracking();
        
        // Start a new task to check player inventories periodically using batching
        taskId = new BukkitRunnable() {
            private Queue<Player> playerQueue = new LinkedList<>();
            
            @Override
            public void run() {
                // If queue is empty, refill it with all online players
                if (playerQueue.isEmpty()) {
                    playerQueue.addAll(Bukkit.getOnlinePlayers());
                }
                
                // Process a batch of players
                int processed = 0;
                while (!playerQueue.isEmpty() && processed < batchSize) {
                    Player player = playerQueue.poll();
                    if (player != null && player.isOnline()) {
                        checkPlayer(player);
                        processed++;
                    }
                }
            }
        }.runTaskTimer(plugin, checkInterval, 1L).getTaskId(); // Run every tick, but process players in batches
    }

    /**
     * Stop tracking player inventories
     */
    public void stopTracking() {
        if (taskId != -1) {
            Bukkit.getScheduler().cancelTask(taskId);
            taskId = -1;
        }
    }

    /**
     * Check a specific player for sheathable items
     * @param player The player to check
     */
    public void checkPlayer(Player player) {
        // Skip if player is in a disabled world
        if (plugin.isWorldDisabled(player.getWorld().getName())) {
            return;
        }
        
        UUID playerId = player.getUniqueId();
        boolean hadSheathableItems = playerSheathableStatus.getOrDefault(playerId, false);
        
        // Check if player has any sheathable items (excluding offhand)
        boolean hasSheathableItems = plugin.getSheathManager().playerHasSheathableItems(player);
        
        // Update the player's status
        playerSheathableStatus.put(playerId, hasSheathableItems);
        
        // Fire the event if the status has changed or we need to enforce the sheath
        if (hasSheathableItems != hadSheathableItems || hasSheathableItems) {
            SheathableItemsCheckEvent event = new SheathableItemsCheckEvent(player, hasSheathableItems, hadSheathableItems);
            Bukkit.getPluginManager().callEvent(event);
        }
        
        // If player no longer has sheathable items, clear the offhand if it contains a sheathable item
        if (plugin.isEntityVisualizationMode()) {
            return;
        }
        if (!hasSheathableItems) {
            ItemStack offhandItem = player.getInventory().getItemInOffHand();
            if (offhandItem != null && offhandItem.getType() != Material.AIR && 
                plugin.getSheathManager().isSheathable(offhandItem.getType())) {
                player.getInventory().setItemInOffHand(new ItemStack(Material.AIR));
            }
        }
    }

    /**
     * Remove a player from tracking when they leave
     * @param playerId The UUID of the player to remove
     */
    public void removePlayer(UUID playerId) {
        playerSheathableStatus.remove(playerId);
    }
    
    /**
     * Force check all online players immediately
     * Useful for config reloads
     */
    public void forceCheckAllPlayers() {
        new BukkitRunnable() {
            @Override
            public void run() {
                List<Player> players = new ArrayList<>(Bukkit.getOnlinePlayers());
                for (Player player : players) {
                    checkPlayer(player);
                }
            }
        }.runTask(plugin);
    }
}