package ca.xef5000.visualSheath.managers;

import ca.xef5000.visualSheath.VisualSheath;
import ca.xef5000.visualSheath.utils.ItemsAdderIntegration;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.inventory.ItemStack;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

/**
 * Manages custom models for sheath visualization
 */
public class ModelManager {

    private final VisualSheath plugin;
    private final Map<String, String> itemModelMappings;
    private final Map<String, FileConfiguration> loadedModels;
    private final File modelsFolder;

    /**
     * Constructor for ModelManager
     * @param plugin The VisualSheath plugin instance
     */
    public ModelManager(VisualSheath plugin) {
        this.plugin = plugin;
        this.itemModelMappings = new HashMap<>();
        this.loadedModels = new HashMap<>();
        this.modelsFolder = new File(plugin.getDataFolder(), "models");
        
        // Create models folder if it doesn't exist
        if (!modelsFolder.exists()) {
            modelsFolder.mkdirs();
        }
        
        // Save default models
        saveDefaultModels();
        
        // Load model mappings from config
        loadModelMappings();
    }

    /**
     * Save default model files
     */
    private void saveDefaultModels() {
        // Save Trident.yml if it doesn't exist
        File tridentModel = new File(modelsFolder, "Trident.yml");
        if (!tridentModel.exists()) {
            try (InputStream in = plugin.getResource("models/Trident.yml")) {
                if (in != null) {
                    Files.copy(in, tridentModel.toPath());
                    plugin.getLogger().info("Created default model: Trident.yml");
                }
            } catch (IOException e) {
                plugin.getLogger().log(Level.WARNING, "Failed to create default model: Trident.yml", e);
            }
        }
    }

    /**
     * Load model mappings from config
     */
    private void loadModelMappings() {
        // Clear existing mappings
        itemModelMappings.clear();
        
        // Get model mappings from config
        ConfigurationSection modelsSection = plugin.getConfig().getConfigurationSection("models");
        if (modelsSection != null) {
            for (String key : modelsSection.getKeys(false)) {
                String modelFile = modelsSection.getString(key);
                if (modelFile != null && !modelFile.isEmpty()) {
                    itemModelMappings.put(key.toUpperCase(), modelFile);
                }
            }
        }
        
        plugin.getLogger().info("Loaded " + itemModelMappings.size() + " model mappings");
    }

    /**
     * Load all model files
     */
    public void loadAllModels() {
        // Clear existing loaded models
        loadedModels.clear();
        
        // Load each model file
        for (String modelFile : itemModelMappings.values()) {
            loadModel(modelFile);
        }
        
        plugin.getLogger().info("Loaded " + loadedModels.size() + " model files");
    }

    /**
     * Load a specific model file
     * @param modelFileName The name of the model file
     * @return The loaded model configuration, or null if loading failed
     */
    private FileConfiguration loadModel(String modelFileName) {
        // Check if model is already loaded
        if (loadedModels.containsKey(modelFileName)) {
            return loadedModels.get(modelFileName);
        }
        
        // Create file object
        File modelFile = new File(modelsFolder, modelFileName);
        
        // Check if file exists
        if (!modelFile.exists()) {
            plugin.getLogger().warning("Model file not found: " + modelFileName);
            return null;
        }
        
        // Load the model file
        FileConfiguration modelConfig = YamlConfiguration.loadConfiguration(modelFile);
        
        // Store the loaded model
        loadedModels.put(modelFileName, modelConfig);
        
        return modelConfig;
    }

    /**
     * Get the model for an item
     * @param item The item to get the model for
     * @return The model configuration, or null if no model is defined
     */
    public FileConfiguration getModelForItem(ItemStack item) {
        if (item == null) {
            return null;
        }
        
        String itemKey = null;
        
        // Check if it's a vanilla item
        if (item.getType() != Material.AIR) {
            itemKey = item.getType().name();
        }
        
        // Check if it's an ItemsAdder item
        if (ItemsAdderIntegration.isEnabled() && ItemsAdderIntegration.isCustomItem(item)) {
            itemKey = ItemsAdderIntegration.getNamespacedID(item);
        }
        
        // If no item key found, return null
        if (itemKey == null) {
            return null;
        }
        
        // Get the model file name for this item
        String modelFileName = itemModelMappings.get(itemKey.toUpperCase());
        if (modelFileName == null) {
            return null;
        }
        
        // Load and return the model
        return loadModel(modelFileName);
    }

    /**
     * Get the list of entity configurations from a model
     * @param modelConfig The model configuration
     * @return List of entity configurations
     */
    public List<EntityConfig> getEntitiesFromModel(FileConfiguration modelConfig) {
        List<EntityConfig> entities = new ArrayList<>();
        
        if (modelConfig == null) {
            return entities;
        }
        
        List<?> entitiesList = modelConfig.getList("entities");
        if (entitiesList == null) {
            return entities;
        }
        
        for (Object entityObj : entitiesList) {
            if (entityObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> entityMap = (Map<String, Object>) entityObj;
                
                EntityConfig entityConfig = new EntityConfig();
                
                // Get scale values
                entityConfig.scale = getFloat(entityMap, "scale", 0.5f);
                entityConfig.scaleX = getFloat(entityMap, "scalex", 1.0f);
                entityConfig.scaleY = getFloat(entityMap, "scaley", 1.0f);
                entityConfig.scaleZ = getFloat(entityMap, "scalez", 1.0f);
                
                // Get offset values
                if (entityMap.containsKey("offset") && entityMap.get("offset") instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> offsetMap = (Map<String, Object>) entityMap.get("offset");
                    entityConfig.offsetX = getFloat(offsetMap, "x", -0.15f);
                    entityConfig.offsetY = getFloat(offsetMap, "y", 0.5f);
                    entityConfig.offsetZ = getFloat(offsetMap, "z", -0.25f);
                }
                
                // Get rotation values
                if (entityMap.containsKey("rotation") && entityMap.get("rotation") instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> rotationMap = (Map<String, Object>) entityMap.get("rotation");
                    entityConfig.rotationX = getFloat(rotationMap, "x", 0f);
                    entityConfig.rotationY = getFloat(rotationMap, "y", 0f);
                    entityConfig.rotationZ = getFloat(rotationMap, "z", 45f);
                }
                
                // Get crouch offset values
                if (entityMap.containsKey("crouch") && entityMap.get("crouch") instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> crouchMap = (Map<String, Object>) entityMap.get("crouch");
                    
                    // Get crouch offset values
                    if (crouchMap.containsKey("offset") && crouchMap.get("offset") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> crouchOffsetMap = (Map<String, Object>) crouchMap.get("offset");
                        entityConfig.crouchOffsetX = getFloat(crouchOffsetMap, "x", 0f);
                        entityConfig.crouchOffsetY = getFloat(crouchOffsetMap, "y", 0f);
                        entityConfig.crouchOffsetZ = getFloat(crouchOffsetMap, "z", 0f);
                    }
                    
                    // Get crouch rotation values
                    if (crouchMap.containsKey("rotation") && crouchMap.get("rotation") instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> crouchRotationMap = (Map<String, Object>) crouchMap.get("rotation");
                        entityConfig.crouchRotationX = getFloat(crouchRotationMap, "x", 0f);
                        entityConfig.crouchRotationY = getFloat(crouchRotationMap, "y", 0f);
                        entityConfig.crouchRotationZ = getFloat(crouchRotationMap, "z", 0f);
                    }
                }
                
                // Get custom item
                if (entityMap.containsKey("item")) {
                    entityConfig.customItem = String.valueOf(entityMap.get("item"));
                }
                
                entities.add(entityConfig);
            }
        }
        
        return entities;
    }

    /**
     * Helper method to get a float value from a map
     * @param map The map to get the value from
     * @param key The key to get
     * @param defaultValue The default value if the key is not found
     * @return The float value
     */
    private float getFloat(Map<String, Object> map, String key, float defaultValue) {
        if (map.containsKey(key)) {
            Object value = map.get(key);
            if (value instanceof Number) {
                return ((Number) value).floatValue();
            }
        }
        return defaultValue;
    }

    /**
     * Get a custom item from a model
     * @param itemString The item string from the model
     * @return The ItemStack, or null if not found
     */
    public ItemStack getCustomItemFromModel(String itemString) {
        if (itemString == null || itemString.isEmpty()) {
            return null;
        }
        
        // Check if it's an ItemsAdder item
        if (itemString.contains(":") && ItemsAdderIntegration.isEnabled()) {
            return ItemsAdderIntegration.getItemByNamespacedID(itemString);
        }
        
        // Try to parse as a vanilla material
        try {
            Material material = Material.valueOf(itemString.toUpperCase());
            return new ItemStack(material);
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("Invalid item in model: " + itemString);
            return null;
        }
    }

    /**
     * Reload all models
     */
    public void reloadModels() {
        loadModelMappings();
        loadAllModels();
    }

    /**
     * Entity configuration class
     */
    public static class EntityConfig {
        public float scale = 0.5f;
        public float scaleX = 1.0f;
        public float scaleY = 1.0f;
        public float scaleZ = 1.0f;
        public float offsetX = -0.15f;
        public float offsetY = 0.5f;
        public float offsetZ = -0.25f;
        public float rotationX = 0f;
        public float rotationY = 0f;
        public float rotationZ = 45f;
        public String customItem = null;
        // Crouch-specific adjustments
        public float crouchOffsetX = 0f;
        public float crouchOffsetY = 0f;
        public float crouchOffsetZ = 0f;
        public float crouchRotationX = 0f;
        public float crouchRotationY = 0f;
        public float crouchRotationZ = 0f;
    }
}
