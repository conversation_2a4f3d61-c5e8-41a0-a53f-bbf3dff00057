package ca.xef5000.dualWeaponExtension.listeners;

import ca.xef5000.dualWeaponExtension.DualWeaponExtension;
import ca.xef5000.dualWeaponExtension.config.ConfigManager;
import ca.xef5000.dualWeaponExtension.managers.ItemsAdderManager;
import ca.xef5000.dualWeaponExtension.managers.OffhandManager;
import ca.xef5000.dualWeaponExtension.models.DualWeapon;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.event.player.PlayerDropItemEvent;
import org.bukkit.event.player.PlayerItemHeldEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerSwapHandItemsEvent;
import org.bukkit.inventory.EquipmentSlot;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.PlayerInventory;
import org.bukkit.scheduler.BukkitRunnable;

/**
 * Listens for events related to dual weapons.
 */
public class DualWeaponListener implements Listener {
    private final DualWeaponExtension plugin;
    private final ConfigManager configManager;
    private final ItemsAdderManager itemsAdderManager;
    private final OffhandManager offhandManager;

    /**
     * Creates a new dual weapon listener.
     *
     * @param plugin The plugin instance
     * @param configManager The config manager
     * @param itemsAdderManager The ItemsAdder manager
     * @param offhandManager The offhand manager
     */
    public DualWeaponListener(DualWeaponExtension plugin, ConfigManager configManager, 
                             ItemsAdderManager itemsAdderManager, OffhandManager offhandManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.itemsAdderManager = itemsAdderManager;
        this.offhandManager = offhandManager;
    }

    /**
     * Handles a player joining the server.
     * Checks if they have a dual weapon equipped and sets up the secondary weapon if needed.
     *
     * @param event The event
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Schedule a delayed task to check for dual weapons after the player has fully joined
        new BukkitRunnable() {
            @Override
            public void run() {
                if (player.isOnline()) {
                    checkAndEquipDualWeapon(player);
                }
            }
        }.runTaskLater(plugin, 5L); // 5 ticks = 0.25 seconds
    }

    /**
     * Handles a player quitting the server.
     * Restores their offhand item if they have one stored.
     *
     * @param event The event
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        
        // Restore offhand item if they have one stored
        if (offhandManager.hasStoredOffhandItem(player)) {
            offhandManager.restoreOffhandItem(player);
        }
    }

    /**
     * Handles a player changing their held item.
     * Checks if they equipped or unequipped a dual weapon.
     *
     * @param event The event
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onItemHeldChange(PlayerItemHeldEvent event) {
        Player player = event.getPlayer();
        
        // Schedule a delayed task to check for dual weapons after the item has been changed
        new BukkitRunnable() {
            @Override
            public void run() {
                if (player.isOnline()) {
                    checkAndEquipDualWeapon(player);
                }
            }
        }.runTaskLater(plugin, 1L);
    }

    /**
     * Handles a player swapping their hand items.
     * Prevents swapping if the offhand item is a secondary weapon.
     *
     * @param event The event
     */
    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    public void onHandSwap(PlayerSwapHandItemsEvent event) {
        Player player = event.getPlayer();
        ItemStack mainHandItem = player.getInventory().getItemInMainHand();
        ItemStack offHandItem = player.getInventory().getItemInOffHand();
        
        // If the main hand item is a dual weapon, prevent swapping
        if (itemsAdderManager.isDualWeapon(mainHandItem)) {
            String mainWeaponId = itemsAdderManager.getItemsAdderId(mainHandItem);
            DualWeapon dualWeapon = configManager.getDualWeapon(mainWeaponId);
            
            if (dualWeapon != null && dualWeapon.isPreventSwitch()) {
                event.setCancelled(true);
                configManager.logDebug("Prevented hand swap for dual weapon: " + mainWeaponId);
            }
        }
    }

    /**
     * Handles a player dropping an item.
     * Prevents dropping if the item is a secondary weapon.
     *
     * @param event The event
     */
    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    public void onItemDrop(PlayerDropItemEvent event) {
        Player player = event.getPlayer();
        ItemStack mainHandItem = player.getInventory().getItemInMainHand();
        
        // If the main hand item is a dual weapon, prevent dropping the offhand item
        if (itemsAdderManager.isDualWeapon(mainHandItem)) {
            ItemStack droppedItem = event.getItemDrop().getItemStack();
            ItemStack offHandItem = player.getInventory().getItemInOffHand();
            
            // If the dropped item is the offhand item
            if (offHandItem != null && !offHandItem.getType().isAir() && 
                droppedItem.isSimilar(offHandItem)) {
                
                String mainWeaponId = itemsAdderManager.getItemsAdderId(mainHandItem);
                DualWeapon dualWeapon = configManager.getDualWeapon(mainWeaponId);
                
                if (dualWeapon != null && dualWeapon.isPreventDrop()) {
                    event.setCancelled(true);
                    configManager.logDebug("Prevented dropping secondary weapon for: " + mainWeaponId);
                }
            }
        }
    }

    /**
     * Handles a player clicking in an inventory.
     * Prevents moving the secondary weapon if needed.
     *
     * @param event The event
     */
    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        ItemStack mainHandItem = player.getInventory().getItemInMainHand();
        
        // If the main hand item is a dual weapon
        if (itemsAdderManager.isDualWeapon(mainHandItem)) {
            // Check if the click involves the offhand slot (40 is the offhand slot in player inventory)
            if (event.getSlot() == 40) {
                
                String mainWeaponId = itemsAdderManager.getItemsAdderId(mainHandItem);
                DualWeapon dualWeapon = configManager.getDualWeapon(mainWeaponId);
                
                if (dualWeapon != null && dualWeapon.isPreventSwitch()) {
                    event.setCancelled(true);
                    configManager.logDebug("Prevented inventory click for secondary weapon: " + mainWeaponId);
                }
            }
        }
    }

    /**
     * Handles a player dragging items in an inventory.
     * Prevents dragging to the offhand slot if a dual weapon is equipped.
     *
     * @param event The event
     */
    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    public void onInventoryDrag(InventoryDragEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getWhoClicked();
        ItemStack mainHandItem = player.getInventory().getItemInMainHand();
        
        // If the main hand item is a dual weapon
        if (itemsAdderManager.isDualWeapon(mainHandItem)) {
            // Check if the drag involves the offhand slot (45 is the offhand slot in player inventory)
            if (event.getRawSlots().contains(40) || event.getRawSlots().contains(45)) {
                
                String mainWeaponId = itemsAdderManager.getItemsAdderId(mainHandItem);
                DualWeapon dualWeapon = configManager.getDualWeapon(mainWeaponId);
                
                if (dualWeapon != null && dualWeapon.isPreventSwitch()) {
                    event.setCancelled(true);
                    configManager.logDebug("Prevented inventory drag for secondary weapon: " + mainWeaponId);
                }
            }
        }
    }

    /**
     * Handles a player dying.
     * Restores their offhand item if they have one stored.
     *
     * @param event The event
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerDeath(PlayerDeathEvent event) {
        Player player = event.getEntity();
        
        // Restore offhand item if they have one stored
        if (offhandManager.hasStoredOffhandItem(player)) {
            offhandManager.restoreOffhandItem(player);
        }
    }

    /**
     * Checks if a player has a dual weapon equipped and sets up the secondary weapon if needed.
     *
     * @param player The player to check
     */
    private void checkAndEquipDualWeapon(Player player) {
        PlayerInventory inventory = player.getInventory();
        ItemStack mainHandItem = inventory.getItemInMainHand();
        
        // Check if the main hand item is a dual weapon
        if (itemsAdderManager.isDualWeapon(mainHandItem)) {
            String mainWeaponId = itemsAdderManager.getItemsAdderId(mainHandItem);
            configManager.logDebug("Player " + player.getName() + " equipped dual weapon: " + mainWeaponId);
            
            // Get the secondary weapon
            ItemStack secondaryWeapon = itemsAdderManager.getSecondaryWeaponItem(mainHandItem);
            
            if (secondaryWeapon != null) {
                // Store the current offhand item if it exists and is not already the secondary weapon
                ItemStack currentOffhandItem = inventory.getItemInOffHand();
                
                if (currentOffhandItem != null && !currentOffhandItem.getType().isAir() && 
                    !itemsAdderManager.getItemsAdderId(currentOffhandItem).equals(
                        configManager.getDualWeapon(mainWeaponId).getSecondaryWeaponId())) {
                    
                    offhandManager.storeOffhandItem(player);
                }
                
                // Set the secondary weapon in the offhand
                inventory.setItemInOffHand(secondaryWeapon);
                configManager.logDebug("Set secondary weapon for player " + player.getName() + ": " + 
                                      configManager.getDualWeapon(mainWeaponId).getSecondaryWeaponId());
            }
        } else {
            // If the player doesn't have a dual weapon equipped, restore their offhand item if they have one stored
            ItemStack offHandItem = inventory.getItemInOffHand();
            
            // Only restore if the current offhand is empty or is a secondary weapon
            if (offHandItem == null || offHandItem.getType().isAir() || 
                (itemsAdderManager.isItemsAdderItem(offHandItem) && 
                 isSecondaryWeapon(itemsAdderManager.getItemsAdderId(offHandItem)))) {
                
                if (offhandManager.hasStoredOffhandItem(player)) {
                    offhandManager.restoreOffhandItem(player);
                    configManager.logDebug("Restored offhand item for player " + player.getName());
                } else if (isSecondaryWeapon(itemsAdderManager.getItemsAdderId(offHandItem))) {
                    // If there's no stored item but the offhand has a secondary weapon, clear it
                    inventory.setItemInOffHand(null);
                    configManager.logDebug("Cleared secondary weapon for player " + player.getName());
                }
            }
        }
    }

    /**
     * Checks if an ItemsAdder ID is a secondary weapon.
     *
     * @param itemsAdderId The ItemsAdder ID to check
     * @return True if the ID is a secondary weapon, false otherwise
     */
    private boolean isSecondaryWeapon(String itemsAdderId) {
        if (itemsAdderId == null) {
            return false;
        }
        
        for (DualWeapon dualWeapon : configManager.getDualWeapons().values()) {
            if (dualWeapon.getSecondaryWeaponId().equals(itemsAdderId)) {
                return true;
            }
        }
        
        return false;
    }
}