package ca.xef5000.dualWeaponExtension.managers;

import ca.xef5000.dualWeaponExtension.DualWeaponExtension;
import ca.xef5000.dualWeaponExtension.config.ConfigManager;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Manages the storage and retrieval of offhand items.
 */
public class OffhandManager {
    private final DualWeaponExtension plugin;
    private final ConfigManager configManager;
    private final Map<UUID, ItemStack> storedOffhandItems = new HashMap<>();

    /**
     * Creates a new offhand manager.
     *
     * @param plugin The plugin instance
     * @param configManager The config manager
     */
    public OffhandManager(DualWeaponExtension plugin, ConfigManager configManager) {
        this.plugin = plugin;
        this.configManager = configManager;
    }

    /**
     * Stores the player's current offhand item if it exists.
     *
     * @param player The player
     * @return True if an item was stored, false otherwise
     */
    public boolean storeOffhandItem(Player player) {
        ItemStack offhandItem = player.getInventory().getItemInOffHand();
        if (offhandItem != null && !offhandItem.getType().isAir()) {
            storedOffhandItems.put(player.getUniqueId(), offhandItem.clone());
            configManager.logDebug("Stored offhand item for player " + player.getName());
            return true;
        }
        return false;
    }

    /**
     * Restores the player's stored offhand item if it exists.
     *
     * @param player The player
     * @return True if an item was restored, false otherwise
     */
    public boolean restoreOffhandItem(Player player) {
        ItemStack storedItem = storedOffhandItems.remove(player.getUniqueId());
        if (storedItem != null) {
            player.getInventory().setItemInOffHand(storedItem);
            configManager.logDebug("Restored offhand item for player " + player.getName());
            return true;
        }
        return false;
    }

    /**
     * Clears the player's offhand slot.
     *
     * @param player The player
     */
    public void clearOffhand(Player player) {
        player.getInventory().setItemInOffHand(null);
    }

    /**
     * Checks if a player has a stored offhand item.
     *
     * @param player The player
     * @return True if the player has a stored offhand item, false otherwise
     */
    public boolean hasStoredOffhandItem(Player player) {
        return storedOffhandItems.containsKey(player.getUniqueId());
    }

    /**
     * Gets a player's stored offhand item without removing it.
     *
     * @param player The player
     * @return The stored offhand item, or null if none exists
     */
    public ItemStack getStoredOffhandItem(Player player) {
        return storedOffhandItems.get(player.getUniqueId());
    }

    /**
     * Clears all stored offhand items.
     */
    public void clearAllStoredItems() {
        storedOffhandItems.clear();
    }
}