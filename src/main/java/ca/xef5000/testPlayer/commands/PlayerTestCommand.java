package ca.xef5000.testPlayer.commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.ItemDisplay;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.Location;
// New imports
import org.bukkit.NamespacedKey;
import org.bukkit.persistence.PersistentDataType;
import ca.xef5000.testPlayer.TestPlayer;

public class PlayerTestCommand implements CommandExecutor {

    public static final NamespacedKey ITEM_DISPLAY_TAG_KEY = new NamespacedKey(TestPlayer.getPlugin(TestPlayer.class), "custom_item_display_tag");

    @Override
    public boolean onCommand(@NotNull CommandSender sender, @NotNull Command command, @NotNull String label, @NotNull String[] args) {
        if (!(sender instanceof Player player)) return false;

        // Get a location slightly in front of the player
        Location spawnLocation = player.getLocation().add(player.getLocation().getDirection().multiply(2));

        ItemDisplay itemDisplay = player.getWorld().spawn(spawnLocation, ItemDisplay.class);

        // Add a persistent data tag
        itemDisplay.getPersistentDataContainer().set(ITEM_DISPLAY_TAG_KEY, PersistentDataType.STRING, "testplayer_display");

        // Create a diamond sword ItemStack
        ItemStack diamondSword = new ItemStack(Material.DIAMOND_SWORD);

        // Set the item for the ItemDisplay
        itemDisplay.setItemStack(diamondSword);
        player.addPassenger(itemDisplay);

        return true;
    }
}
