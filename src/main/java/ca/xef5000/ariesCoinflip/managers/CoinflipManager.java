package ca.xef5000.ariesCoinflip.managers;

import ca.xef5000.ariesCoinflip.AriesCoinflip;
import ca.xef5000.ariesCoinflip.utils.ConfigManager;
import org.bukkit.Bukkit;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class CoinflipManager implements Listener {
    private final AriesCoinflip plugin;
    private final ConfigManager configManager;
    private final DataManager dataManager;
    private final GUIManager guiManager;

    private final Map<UUID, Boolean> creatingCoinflip;

    public CoinflipManager(AriesCoinflip plugin, ConfigManager configManager, DataManager dataManager, GUIManager guiManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.dataManager = dataManager;
        this.guiManager = guiManager;
        this.creatingCoinflip = new HashMap<>();

        // Register this as a listener
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }

    public void startCoinflipCreation(Player player) {
        // Check if player already has an active offer
        if (dataManager.hasActiveOffer(player.getUniqueId())) {
            player.sendMessage(ConfigManager.colorize("&cYou already have an active coinflip offer."));
            return;
        }

        // Mark player as creating a coinflip
        creatingCoinflip.put(player.getUniqueId(), true);

        // Close the inventory
        player.closeInventory();

        // Prompt for bet amount
        player.sendMessage(ConfigManager.colorize("&eEnter the amount you want to bet in chat:"));
        player.sendMessage(ConfigManager.colorize("&7(Min: &a$" + configManager.getMinBet() + "&7, Max: &a$" + configManager.getMaxBet() + "&7)"));
        player.sendMessage(ConfigManager.colorize("&7Type 'cancel' to cancel."));
    }

    /**
     * Creates a coinflip directly without requiring chat interaction
     * 
     * @param player The player creating the coinflip
     * @param amount The amount to bet
     */
    public void createCoinflip(Player player, double amount) {
        // Check if player already has an active offer
        if (dataManager.hasActiveOffer(player.getUniqueId())) {
            player.sendMessage(ConfigManager.colorize("&cYou already have an active coinflip offer."));
            return;
        }

        // Check minimum bet
        if (amount < configManager.getMinBet()) {
            player.sendMessage(configManager.getMessage("minimum_bet")
                    .replace("{min_bet}", String.format("%.2f", configManager.getMinBet())));
            return;
        }

        // Check maximum bet
        if (amount > configManager.getMaxBet()) {
            player.sendMessage(configManager.getMessage("maximum_bet")
                    .replace("{max_bet}", String.format("%.2f", configManager.getMaxBet())));
            return;
        }

        // Check if player has enough money
        if (!AriesCoinflip.getEconomy().has(player, amount)) {
            player.sendMessage(configManager.getMessage("insufficient_funds"));
            return;
        }

        // Withdraw money from player (put in escrow)
        AriesCoinflip.getEconomy().withdrawPlayer(player, amount);

        // Create the coinflip offer
        dataManager.addCoinflipOffer(player, amount, configManager.getOfferTimeout());

        // Send confirmation message
        player.sendMessage(configManager.getMessage("created_coinflip")
                .replace("{amount}", String.format("%.2f", amount)));

        // Play sound
        String soundName = configManager.getConfig().getString("settings.sounds.create_coinflip");
        if (soundName != null) {
            try {
                player.playSound(player.getLocation(), Sound.valueOf(soundName), 1.0f, 1.0f);
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("Invalid sound: " + soundName);
            }
        }

        // Update all open inventories
        guiManager.updateOpenInventories();
    }

    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();

        // Check if player is creating a coinflip
        if (!creatingCoinflip.containsKey(player.getUniqueId())) {
            return;
        }

        // Cancel the chat message
        event.setCancelled(true);

        // Remove player from creating state
        creatingCoinflip.remove(player.getUniqueId());

        String message = event.getMessage();

        // Check if player wants to cancel
        if (message.equalsIgnoreCase("cancel")) {
            player.sendMessage(ConfigManager.colorize("&cCoinflip creation cancelled."));

            // Reopen the menu
            Bukkit.getScheduler().runTask(plugin, () -> guiManager.openMainMenu(player));
            return;
        }

        // Parse bet amount
        double betAmount;
        try {
            betAmount = Double.parseDouble(message);
        } catch (NumberFormatException e) {
            player.sendMessage(ConfigManager.colorize("&cInvalid amount. Please enter a valid number."));

            // Reopen the menu
            Bukkit.getScheduler().runTask(plugin, () -> guiManager.openMainMenu(player));
            return;
        }

        // Check minimum bet
        if (betAmount < configManager.getMinBet()) {
            player.sendMessage(configManager.getMessage("minimum_bet")
                    .replace("{min_bet}", String.format("%.2f", configManager.getMinBet())));

            // Reopen the menu
            Bukkit.getScheduler().runTask(plugin, () -> guiManager.openMainMenu(player));
            return;
        }

        // Check maximum bet
        if (betAmount > configManager.getMaxBet()) {
            player.sendMessage(configManager.getMessage("maximum_bet")
                    .replace("{max_bet}", String.format("%.2f", configManager.getMaxBet())));

            // Reopen the menu
            Bukkit.getScheduler().runTask(plugin, () -> guiManager.openMainMenu(player));
            return;
        }

        // Check if player has enough money and create the coinflip
        Bukkit.getScheduler().runTask(plugin, () -> {
            // Check if player has enough money
            if (!AriesCoinflip.getEconomy().has(player, betAmount)) {
                player.sendMessage(configManager.getMessage("insufficient_funds"));
                guiManager.openMainMenu(player);
                return;
            }

            // Withdraw money from player (put in escrow)
            AriesCoinflip.getEconomy().withdrawPlayer(player, betAmount);

            // Create the coinflip offer
            dataManager.addCoinflipOffer(player, betAmount, configManager.getOfferTimeout());

            // Send confirmation message
            player.sendMessage(configManager.getMessage("created_coinflip")
                    .replace("{amount}", String.format("%.2f", betAmount)));

            // Play sound
            String soundName = configManager.getConfig().getString("settings.sounds.create_coinflip");
            if (soundName != null) {
                try {
                    player.playSound(player.getLocation(), org.bukkit.Sound.valueOf(soundName), 1.0f, 1.0f);
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("Invalid sound: " + soundName);
                }
            }

            // Update all open inventories
            guiManager.updateOpenInventories();

            // Reopen the menu for the player
            guiManager.openMainMenu(player);
        });
    }
}
