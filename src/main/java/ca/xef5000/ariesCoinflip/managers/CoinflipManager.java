package ca.xef5000.ariesCoinflip.managers;

import ca.xef5000.ariesCoinflip.AriesCoinflip;
import ca.xef5000.ariesCoinflip.utils.ConfigManager;
import org.bukkit.Bukkit;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class CoinflipManager implements Listener {
    private final AriesCoinflip plugin;
    private final ConfigManager configManager;
    private final DataManager dataManager;
    private final GUIManager guiManager;

    private final Map<UUID, Boolean> creatingCoinflip;

    public CoinflipManager(AriesCoinflip plugin, ConfigManager configManager, DataManager dataManager, GUIManager guiManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.dataManager = dataManager;
        this.guiManager = guiManager;
        this.creatingCoinflip = new HashMap<>();

        // Register this as a listener
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }

    /**
     * Creates a coinflip directly without requiring chat interaction
     * 
     * @param player The player creating the coinflip
     * @param amount The amount to bet
     */
    public void createCoinflip(Player player, double amount) {
        // Check if player already has an active offer
        if (dataManager.hasActiveOffer(player.getUniqueId())) {
            player.sendMessage(ConfigManager.colorize("&cYou already have an active coinflip offer."));
            return;
        }

        // Check minimum bet
        if (amount < configManager.getMinBet()) {
            player.sendMessage(configManager.getMessage("minimum_bet")
                    .replace("{min_bet}", String.format("%.2f", configManager.getMinBet())));
            return;
        }

        // Check maximum bet
        if (amount > configManager.getMaxBet()) {
            player.sendMessage(configManager.getMessage("maximum_bet")
                    .replace("{max_bet}", String.format("%.2f", configManager.getMaxBet())));
            return;
        }

        // Check if player has enough money
        if (!AriesCoinflip.getEconomy().has(player, amount)) {
            player.sendMessage(configManager.getMessage("insufficient_funds"));
            return;
        }

        // Open color selection GUI instead of immediately creating the coinflip
        plugin.getColorGUIManager().openCreatorColorGUI(player, amount);
    }
}
