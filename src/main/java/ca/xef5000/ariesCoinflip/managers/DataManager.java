package ca.xef5000.ariesCoinflip.managers;

import ca.xef5000.ariesCoinflip.AriesCoinflip;
import ca.xef5000.ariesCoinflip.database.DatabaseManager;
import ca.xef5000.ariesCoinflip.database.MySQLManager;
import ca.xef5000.ariesCoinflip.database.SQLiteManager;
import ca.xef5000.ariesCoinflip.models.CoinflipOffer;
import ca.xef5000.ariesCoinflip.models.PlayerStats;
import org.bukkit.entity.Player;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class DataManager {
    private final AriesCoinflip plugin;
    private final Map<UUID, PlayerStats> playerStats;
    private final Map<UUID, CoinflipOffer> activeOffers;
    private final DatabaseManager databaseManager;

    public DataManager(AriesCoinflip plugin) {
        this.plugin = plugin;
        this.playerStats = new ConcurrentHashMap<>();
        this.activeOffers = new ConcurrentHashMap<>();

        // Initialize database manager based on config
        String dbType = plugin.getConfigManager().getConfig().getString("database.type", "sqlite");

        if (dbType.equalsIgnoreCase("mysql")) {
            String host = plugin.getConfigManager().getConfig().getString("database.mysql.host", "localhost");
            int port = plugin.getConfigManager().getConfig().getInt("database.mysql.port", 3306);
            String database = plugin.getConfigManager().getConfig().getString("database.mysql.database", "coinflip");
            String username = plugin.getConfigManager().getConfig().getString("database.mysql.username", "root");
            String password = plugin.getConfigManager().getConfig().getString("database.mysql.password", "password");

            this.databaseManager = new MySQLManager(plugin, host, port, database, username, password);
        } else {
            this.databaseManager = new SQLiteManager(plugin);
        }

        // Initialize database and load data
        databaseManager.initialize();
        loadData();
    }

    public void loadData() {
        // Load player stats from database
        playerStats.putAll(databaseManager.loadPlayerStats());

        // Load escrow balances from database and update EscrowManager
        Map<UUID, Double> escrowBalances = databaseManager.loadEscrowBalances();
        for (Map.Entry<UUID, Double> entry : escrowBalances.entrySet()) {
            plugin.getEscrowManager().setBalance(entry.getKey(), entry.getValue());
        }

        // Load coinflip offers from database
        activeOffers.putAll(databaseManager.loadCoinflipOffers());
        plugin.getLogger().info("Loaded " + activeOffers.size() + " coinflip offers from database");
    }

    public void saveData() {
        // Save player stats to database
        for (PlayerStats stats : playerStats.values()) {
            databaseManager.savePlayerStats(stats);
        }

        // Save escrow balances to database
        Map<UUID, Double> escrowBalances = plugin.getEscrowManager().getAllBalances();
        for (Map.Entry<UUID, Double> entry : escrowBalances.entrySet()) {
            databaseManager.saveEscrowBalance(entry.getKey(), entry.getValue());
        }
    }

    /**
     * Close the database connection
     */
    public void closeDatabase() {
        if (databaseManager != null) {
            databaseManager.close();
        }
    }

    public PlayerStats getPlayerStats(Player player) {
        return getPlayerStats(player.getUniqueId(), player.getName());
    }

    public PlayerStats getPlayerStats(UUID uuid, String name) {
        if (!playerStats.containsKey(uuid)) {
            playerStats.put(uuid, new PlayerStats(uuid, name));
        }
        return playerStats.get(uuid);
    }

    public void addCoinflipOffer(Player player, double amount, int timeoutSeconds) {
        CoinflipOffer offer = new CoinflipOffer(player, amount, timeoutSeconds);
        activeOffers.put(player.getUniqueId(), offer);
    }

    /**
     * Removes a coinflip offer
     * 
     * @param playerUUID The UUID of the player who created the offer
     * @param returnMoney Whether to return the money to the player (true for expired offers, false for accepted offers)
     */
    public void removeCoinflipOffer(UUID playerUUID, boolean returnMoney) {
        CoinflipOffer offer = activeOffers.get(playerUUID);
        if (offer != null && returnMoney) {
            // Return the money to the player if the offer is being removed due to expiration
            org.bukkit.OfflinePlayer offlinePlayer = org.bukkit.Bukkit.getOfflinePlayer(playerUUID);
            AriesCoinflip.getEconomy().depositPlayer(offlinePlayer, offer.getAmount());

            // Notify the player if they're online
            org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(playerUUID);
            if (player != null && player.isOnline()) {
                player.sendMessage(plugin.getConfigManager().getMessage("coinflip_expired"));
            }
        }

        activeOffers.remove(playerUUID);
    }

    /**
     * Removes a coinflip offer without returning money (for backward compatibility)
     * 
     * @param playerUUID The UUID of the player who created the offer
     */
    public void removeCoinflipOffer(UUID playerUUID) {
        removeCoinflipOffer(playerUUID, false);
    }

    public CoinflipOffer getCoinflipOffer(UUID playerUUID) {
        return activeOffers.get(playerUUID);
    }

    public List<CoinflipOffer> getActiveOffers() {
        // Remove expired offers
        List<UUID> expiredOffers = activeOffers.values().stream()
                .filter(CoinflipOffer::isExpired)
                .map(CoinflipOffer::getPlayerUUID)
                .collect(Collectors.toList());

        // Return money for expired offers
        expiredOffers.forEach(uuid -> removeCoinflipOffer(uuid, true));

        // Return remaining active offers
        return new ArrayList<>(activeOffers.values());
    }

    public boolean hasActiveOffer(UUID playerUUID) {
        return activeOffers.containsKey(playerUUID) && !activeOffers.get(playerUUID).isExpired();
    }
}
