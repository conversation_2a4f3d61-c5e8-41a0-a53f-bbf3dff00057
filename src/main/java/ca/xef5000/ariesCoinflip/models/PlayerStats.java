package ca.xef5000.ariesCoinflip.models;

import java.util.UUID;

public class PlayerStats {
    private final UUID playerUUID;
    private final String playerName;
    private int gamesPlayed;
    private int gamesWon;
    private int gamesLost;
    private double totalWon;
    private double totalLost;

    public PlayerStats(UUID playerUUID, String playerName) {
        this.playerUUID = playerUUID;
        this.playerName = playerName;
        this.gamesPlayed = 0;
        this.gamesWon = 0;
        this.gamesLost = 0;
        this.totalWon = 0;
        this.totalLost = 0;
    }

    /**
     * Constructor for loading from database
     */
    public PlayerStats(UUID playerUUID, String playerName, int gamesPlayed, int gamesWon, int gamesLost, double totalWon, double totalLost) {
        this.playerUUID = playerUUID;
        this.playerName = playerName;
        this.gamesPlayed = gamesPlayed;
        this.gamesWon = gamesWon;
        this.gamesLost = gamesLost;
        this.totalWon = totalWon;
        this.totalLost = totalLost;
    }

    public UUID getPlayerUUID() {
        return playerUUID;
    }

    public String getPlayerName() {
        return playerName;
    }

    public int getGamesPlayed() {
        return gamesPlayed;
    }

    public int getGamesWon() {
        return gamesWon;
    }

    public int getGamesLost() {
        return gamesLost;
    }

    public double getTotalWon() {
        return totalWon;
    }

    public double getTotalLost() {
        return totalLost;
    }

    public double getNetProfit() {
        return totalWon - totalLost;
    }

    public double getWinRate() {
        if (gamesPlayed == 0) {
            return 0;
        }
        return (double) gamesWon / gamesPlayed * 100;
    }

    public void addWin(double amount) {
        gamesPlayed++;
        gamesWon++;
        totalWon += amount;
    }

    public void addLoss(double amount) {
        gamesPlayed++;
        gamesLost++;
        totalLost += amount;
    }
}
