package ca.xef5000.ariesCoinflip.models;

import org.bukkit.entity.Player;

import java.util.UUID;

/**
 * Represents an active coinflip session with two players
 */
public class ActiveCoinflip {
    private final UUID creatorUUID;
    private final String creatorName;
    private final UUID accepterUUID;
    private final String accepterName;
    private final double amount;
    private final CoinflipColor creatorColor;
    private final CoinflipColor accepterColor;
    private final long startTime;
    private final boolean creatorWins;

    /**
     * Creates a new active coinflip
     * 
     * @param creator The player who created the coinflip
     * @param accepter The player who accepted the coinflip
     * @param amount The bet amount
     * @param creatorColor The color selected by the creator
     * @param accepterColor The color selected by the accepter
     * @param creatorWins Whether the creator wins this coinflip
     */
    public ActiveCoinflip(Player creator, Player accepter, double amount, 
                         CoinflipColor creatorColor, CoinflipColor accepterColor, boolean creatorWins) {
        this.creatorUUID = creator.getUniqueId();
        this.creatorName = creator.getName();
        this.accepterUUID = accepter.getUniqueId();
        this.accepterName = accepter.getName();
        this.amount = amount;
        this.creatorColor = creatorColor;
        this.accepterColor = accepterColor;
        this.startTime = System.currentTimeMillis();
        this.creatorWins = creatorWins;
    }

    /**
     * Get the creator's UUID
     * @return The creator's UUID
     */
    public UUID getCreatorUUID() {
        return creatorUUID;
    }

    /**
     * Get the creator's name
     * @return The creator's name
     */
    public String getCreatorName() {
        return creatorName;
    }

    /**
     * Get the accepter's UUID
     * @return The accepter's UUID
     */
    public UUID getAccepterUUID() {
        return accepterUUID;
    }

    /**
     * Get the accepter's name
     * @return The accepter's name
     */
    public String getAccepterName() {
        return accepterName;
    }

    /**
     * Get the bet amount
     * @return The bet amount
     */
    public double getAmount() {
        return amount;
    }

    /**
     * Get the creator's selected color
     * @return The creator's color
     */
    public CoinflipColor getCreatorColor() {
        return creatorColor;
    }

    /**
     * Get the accepter's selected color
     * @return The accepter's color
     */
    public CoinflipColor getAccepterColor() {
        return accepterColor;
    }

    /**
     * Get the start time of this coinflip
     * @return The start time in milliseconds
     */
    public long getStartTime() {
        return startTime;
    }

    /**
     * Check if the creator wins this coinflip
     * @return True if creator wins, false if accepter wins
     */
    public boolean doesCreatorWin() {
        return creatorWins;
    }

    /**
     * Get the winner's UUID
     * @return The winner's UUID
     */
    public UUID getWinnerUUID() {
        return creatorWins ? creatorUUID : accepterUUID;
    }

    /**
     * Get the winner's name
     * @return The winner's name
     */
    public String getWinnerName() {
        return creatorWins ? creatorName : accepterName;
    }

    /**
     * Get the loser's UUID
     * @return The loser's UUID
     */
    public UUID getLoserUUID() {
        return creatorWins ? accepterUUID : creatorUUID;
    }

    /**
     * Get the loser's name
     * @return The loser's name
     */
    public String getLoserName() {
        return creatorWins ? accepterName : creatorName;
    }

    /**
     * Get the winner's color
     * @return The winner's color
     */
    public CoinflipColor getWinnerColor() {
        return creatorWins ? creatorColor : accepterColor;
    }

    /**
     * Get the loser's color
     * @return The loser's color
     */
    public CoinflipColor getLoserColor() {
        return creatorWins ? accepterColor : creatorColor;
    }

    /**
     * Check if a player is involved in this coinflip
     * @param playerUUID The player's UUID
     * @return True if the player is either creator or accepter
     */
    public boolean isPlayerInvolved(UUID playerUUID) {
        return creatorUUID.equals(playerUUID) || accepterUUID.equals(playerUUID);
    }
}
