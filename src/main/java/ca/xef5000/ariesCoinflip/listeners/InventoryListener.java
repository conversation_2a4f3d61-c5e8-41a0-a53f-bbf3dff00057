package ca.xef5000.ariesCoinflip.listeners;

import ca.xef5000.ariesCoinflip.AriesCoinflip;
import ca.xef5000.ariesCoinflip.managers.DataManager;
import ca.xef5000.ariesCoinflip.managers.GUIManager;
import ca.xef5000.ariesCoinflip.models.CoinflipOffer;
import ca.xef5000.ariesCoinflip.models.PlayerStats;
import ca.xef5000.ariesCoinflip.utils.ConfigManager;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.SkullMeta;

import java.util.Random;
import java.util.UUID;

public class InventoryListener implements Listener {
    private final AriesCoinflip plugin;
    private final ConfigManager configManager;
    private final DataManager dataManager;
    private final GUIManager guiManager;
    private final Random random;

    public InventoryListener(AriesCoinflip plugin, ConfigManager configManager, DataManager dataManager, GUIManager guiManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.dataManager = dataManager;
        this.guiManager = guiManager;
        this.random = new Random();
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (event.getView().getTitle().equals(configManager.getGuiTitle())) {
            event.setCancelled(true);

            if (event.getCurrentItem() == null || event.getCurrentItem().getType() == Material.AIR) {
                return;
            }

            Player player = (Player) event.getWhoClicked();
            ItemStack clickedItem = event.getCurrentItem();

            // Check if it's the close button
            if (isCloseButton(event.getSlot())) {
                player.closeInventory();
                playSound(player, "close_menu");
                return;
            }

            // Check if it's the statistics button
            if (isStatisticsButton(event.getSlot())) {
                guiManager.openStatisticsMenu(player);
                return;
            }

            // Check if it's a coinflip offer (player head in the coinflip area)
            if (isCoinflipOffer(event.getSlot()) && clickedItem.getType() == Material.PLAYER_HEAD) {
                handleCoinflipAccept(player, clickedItem);
                return;
            }

            // Note: Creating coinflips by clicking empty slots has been replaced by the /coinflip create command
        } else if (event.getView().getTitle().equals(ConfigManager.colorize("&a&lYour Statistics"))) {
            event.setCancelled(true);

            if (event.getCurrentItem() == null || event.getCurrentItem().getType() == Material.AIR) {
                return;
            }

            Player player = (Player) event.getWhoClicked();

            // Check if it's the back button
            if (event.getSlot() == 26 && event.getCurrentItem().getType() == Material.ARROW) {
                guiManager.openMainMenu(player);
                return;
            }
        }
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getView().getTitle().equals(configManager.getGuiTitle())) {
            Player player = (Player) event.getPlayer();
            playSound(player, "close_menu");
        }
    }

    private boolean isCloseButton(int slot) {
        int closeButtonSlot = configManager.getConfig().getInt("gui.items.close.slot", 45);
        return slot == closeButtonSlot;
    }

    private boolean isStatisticsButton(int slot) {
        int statsButtonSlot = configManager.getConfig().getInt("gui.items.statistics.slot", 8);
        return slot == statsButtonSlot;
    }

    private boolean isCoinflipOffer(int slot) {
        // Coinflip offers are in rows 2-5 (slots 9-44)
        return slot >= 9 && slot <= 44;
    }

    private void handleCoinflipAccept(Player player, ItemStack clickedItem) {
        if (!(clickedItem.getItemMeta() instanceof SkullMeta)) {
            return;
        }

        SkullMeta skullMeta = (SkullMeta) clickedItem.getItemMeta();
        if (skullMeta.getOwningPlayer() == null) {
            return;
        }

        UUID offerPlayerUUID = skullMeta.getOwningPlayer().getUniqueId();

        // Don't allow players to accept their own offers
        if (offerPlayerUUID.equals(player.getUniqueId())) {
            player.sendMessage(ConfigManager.colorize("&cYou cannot accept your own coinflip offer."));
            return;
        }

        CoinflipOffer offer = dataManager.getCoinflipOffer(offerPlayerUUID);
        if (offer == null || offer.isExpired()) {
            player.sendMessage(ConfigManager.colorize("&cThis coinflip offer is no longer available."));
            return;
        }

        // Check if accepting player has enough money
        double betAmount = offer.getAmount();
        if (!AriesCoinflip.getEconomy().has(player, betAmount)) {
            player.sendMessage(configManager.getMessage("insufficient_funds"));
            return;
        }

        // Remove the offer (don't return money since it will be handled in the coinflip)
        dataManager.removeCoinflipOffer(offerPlayerUUID, false);

        // Determine the winner (50/50 chance)
        boolean playerWins = random.nextBoolean();

        Player offerPlayer = Bukkit.getPlayer(offerPlayerUUID);
        if (offerPlayer != null && offerPlayer.isOnline()) {
            // Update stats for both players
            PlayerStats playerStats = dataManager.getPlayerStats(player);
            PlayerStats offerPlayerStats = dataManager.getPlayerStats(offerPlayer);
            double lostAmount = offer.getAmount();
            double tax = lostAmount * plugin.getConfigManager().getTaxPercentage() / 100;
            double winAmount = lostAmount - tax;

            if (playerWins) {
                // Player wins
                playerStats.addWin(offer.getAmount());
                offerPlayerStats.addLoss(offer.getAmount());

                player.sendMessage(configManager.getMessage("won_coinflip")
                        .replace("{amount}", String.format("%.2f", winAmount))
                        .replace("{player}", offerPlayer.getName()));

                offerPlayer.sendMessage(configManager.getMessage("lost_coinflip")
                        .replace("{amount}", String.format("%.2f", lostAmount))
                        .replace("{player}", player.getName()));

                playSound(player, "win_coinflip");
                playSound(offerPlayer, "lose_coinflip");

                // Player wins, so deposit winnings to them
                // Note: Offer creator's money was already taken when they created the offer
                AriesCoinflip.getEconomy().depositPlayer(player, winAmount);
            } else {
                // Offer player wins
                playerStats.addLoss(offer.getAmount());
                offerPlayerStats.addWin(offer.getAmount());

                player.sendMessage(configManager.getMessage("lost_coinflip")
                        .replace("{amount}", String.format("%.2f", lostAmount))
                        .replace("{player}", offerPlayer.getName()));

                offerPlayer.sendMessage(configManager.getMessage("won_coinflip")
                        .replace("{amount}", String.format("%.2f", winAmount))
                        .replace("{player}", player.getName()));

                playSound(player, "lose_coinflip");
                playSound(offerPlayer, "win_coinflip");

                // Offer player wins, so withdraw from accepting player and deposit to offer creator
                AriesCoinflip.getEconomy().withdrawPlayer(player, lostAmount);
                AriesCoinflip.getEconomy().depositPlayer(offerPlayer, winAmount);
            }

            // Save the updated stats
            dataManager.saveData();
        }

        // Refresh the GUI for all players
        guiManager.updateOpenInventories();
    }

    private void playSound(Player player, String soundKey) {
        String soundName = configManager.getConfig().getString("settings.sounds." + soundKey);
        if (soundName != null) {
            try {
                Sound sound = Sound.valueOf(soundName);
                player.playSound(player.getLocation(), sound, 1.0f, 1.0f);
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("Invalid sound: " + soundName);
            }
        }
    }
}
