package ca.xef5000.ariesCoinflip;

import ca.xef5000.ariesCoinflip.commands.CoinflipCommand;
import ca.xef5000.ariesCoinflip.commands.CoinflipTabCompleter;
import ca.xef5000.ariesCoinflip.listeners.InventoryListener;
import ca.xef5000.ariesCoinflip.managers.AnimationManager;
import ca.xef5000.ariesCoinflip.managers.ColorGUIManager;
import ca.xef5000.ariesCoinflip.managers.CoinflipManager;
import ca.xef5000.ariesCoinflip.managers.DataManager;
import ca.xef5000.ariesCoinflip.managers.EscrowManager;
import ca.xef5000.ariesCoinflip.managers.GUIManager;
import ca.xef5000.ariesCoinflip.placeholders.CoinflipPlaceholders;
import ca.xef5000.ariesCoinflip.utils.ConfigManager;
import net.milkbowl.vault.economy.Economy;
import org.bukkit.Bukkit;
import org.bukkit.plugin.RegisteredServiceProvider;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitTask;

public final class AriesCoinflip extends JavaPlugin {
    private ConfigManager configManager;
    private DataManager dataManager;
    private GUIManager guiManager;
    private CoinflipManager coinflipManager;
    private EscrowManager escrowManager;
    private BukkitTask updateTask;

    private static Economy economy;

    @Override
    public void onEnable() {
        // Initialize managers
        configManager = new ConfigManager(this);
        escrowManager = new EscrowManager(this);
        dataManager = new DataManager(this);
        guiManager = new GUIManager(this, configManager, dataManager);
        coinflipManager = new CoinflipManager(this, configManager, dataManager, guiManager);


        // Register command
        getCommand("coinflip").setExecutor(new CoinflipCommand(this, configManager, guiManager));
        getCommand("coinflip").setTabCompleter(new CoinflipTabCompleter());

        // Register listeners
        Bukkit.getPluginManager().registerEvents(new InventoryListener(this, configManager, dataManager, guiManager), this);

        // Start GUI update task (updates every second)
        updateTask = Bukkit.getScheduler().runTaskTimer(this, guiManager::updateOpenInventories, 20L, 20L);

        if (!setupEconomy() ) {
            getLogger().severe(String.format("[%s] - Disabled due to no Vault dependency found!", getDescription().getName()));
            getServer().getPluginManager().disablePlugin(this);
            return;
        }

        // Register PlaceholderAPI expansion if available
        if (Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
            getLogger().info("PlaceholderAPI found! Registering placeholders...");
            new CoinflipPlaceholders(this).register();
        }

        getLogger().info("AriesCoinflip has been enabled!");
    }

    @Override
    public void onDisable() {
        // Save data
        if (dataManager != null) {
            dataManager.saveData();
            dataManager.closeDatabase();
        }

        // Cancel tasks
        if (updateTask != null) {
            updateTask.cancel();
        }

        getLogger().info("AriesCoinflip has been disabled!");
    }

    private boolean setupEconomy() {
        if (getServer().getPluginManager().getPlugin("Vault") == null) {
            return false;
        }
        RegisteredServiceProvider<Economy> rsp = getServer().getServicesManager().getRegistration(Economy.class);
        if (rsp == null) {
            return false;
        }
        economy = rsp.getProvider();
        return economy != null;
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    public DataManager getDataManager() {
        return dataManager;
    }

    public GUIManager getGuiManager() {
        return guiManager;
    }

    public CoinflipManager getCoinflipManager() {
        return coinflipManager;
    }

    public EscrowManager getEscrowManager() {
        return escrowManager;
    }

    public static Economy getEconomy() {
        return economy;
    }
}
