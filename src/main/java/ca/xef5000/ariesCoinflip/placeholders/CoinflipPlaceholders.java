package ca.xef5000.ariesCoinflip.placeholders;

import ca.xef5000.ariesCoinflip.AriesCoinflip;
import ca.xef5000.ariesCoinflip.models.PlayerStats;
import me.clip.placeholderapi.expansion.PlaceholderExpansion;
import org.bukkit.OfflinePlayer;
import org.jetbrains.annotations.NotNull;

import java.text.DecimalFormat;

/**
 * This class will be registered through the register-method in the 
 * plugins onEnable-method.
 */
public class CoinflipPlaceholders extends PlaceholderExpansion {

    private final AriesCoinflip plugin;
    private final DecimalFormat decimalFormat;

    /**
     * Since we register the expansion inside our own plugin, we
     * can simply use this method here to get an instance of our
     * plugin.
     *
     * @param plugin The instance of our plugin.
     */
    public CoinflipPlaceholders(AriesCoinflip plugin) {
        this.plugin = plugin;
        this.decimalFormat = new DecimalFormat("#,##0.00");
    }

    /**
     * Because this is an internal class,
     * you must override this method to let <PERSON><PERSON><PERSON><PERSON>
     * know to not unregister your expansion class when
     * PlaceholderAPI is reloaded
     *
     * @return true to persist through reloads
     */
    @Override
    public boolean persist() {
        return true;
    }

    /**
     * Because this is a internal class, this check is not needed
     * and we can simply return {@code true}
     *
     * @return Always true since it's an internal class.
     */
    @Override
    public boolean canRegister() {
        return true;
    }

    /**
     * The name of the person who created this expansion should go here.
     * <br>For convenience do we return the author from the plugin.yml
     *
     * @return The name of the author as a String.
     */
    @Override
    public @NotNull String getAuthor() {
        return plugin.getDescription().getAuthors().toString();
    }

    /**
     * The placeholder identifier should go here.
     * <br>This is what tells PlaceholderAPI to call our onRequest
     * method to obtain a value if a placeholder starts with our
     * identifier.
     * <br>The identifier has to be lowercase and can't contain _ or %
     *
     * @return The identifier in {@code %<identifier>_<value>%} as String.
     */
    @Override
    public @NotNull String getIdentifier() {
        return "coinflip";
    }

    /**
     * This is the version of the expansion.
     * <br>You don't have to use numbers, since it is set as a String.
     *
     * For convenience do we return the version from the plugin.yml
     *
     * @return The version as a String.
     */
    @Override
    public @NotNull String getVersion() {
        return plugin.getDescription().getVersion();
    }

    /**
     * This is the method called when a placeholder with our identifier
     * is found and needs a value.
     * <br>We specify the value identifier in this method.
     * <br>Since version 2.9.1 can you use OfflinePlayers in your requests.
     *
     * @param player A {@link org.bukkit.OfflinePlayer OfflinePlayer}.
     * @param identifier A String containing the identifier/value.
     *
     * @return Possibly-null String of the requested identifier.
     */
    @Override
    public String onRequest(OfflinePlayer player, @NotNull String identifier) {
        if (player == null) {
            return "";
        }

        // Get the player's stats
        PlayerStats stats = plugin.getDataManager().getPlayerStats(player.getUniqueId(), player.getName());

        // %coinflip_games_played%
        if (identifier.equals("games_played")) {
            return String.valueOf(stats.getGamesPlayed());
        }

        // %coinflip_games_won%
        if (identifier.equals("games_won")) {
            return String.valueOf(stats.getGamesWon());
        }

        // %coinflip_games_lost%
        if (identifier.equals("games_lost")) {
            return String.valueOf(stats.getGamesLost());
        }

        // %coinflip_win_rate%
        if (identifier.equals("win_rate")) {
            return String.format("%.2f", stats.getWinRate());
        }

        // %coinflip_total_won%
        if (identifier.equals("total_won")) {
            return decimalFormat.format(stats.getTotalWon());
        }

        // %coinflip_total_lost%
        if (identifier.equals("total_lost")) {
            return decimalFormat.format(stats.getTotalLost());
        }

        // %coinflip_net_profit%
        if (identifier.equals("net_profit")) {
            return decimalFormat.format(stats.getNetProfit());
        }

        // We return null if an invalid placeholder (something that is not handled in the above if statements) was provided
        return null;
    }
}