package ca.xef5000.ariesCoinflip.database;

import ca.xef5000.ariesCoinflip.models.CoinflipOffer;
import ca.xef5000.ariesCoinflip.models.PlayerStats;
import org.bukkit.entity.Player;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Interface for database operations
 */
public interface DatabaseManager {

    /**
     * Initialize the database connection and create tables if they don't exist
     */
    void initialize();

    /**
     * Close the database connection
     */
    void close();

    /**
     * Load player stats from the database
     * 
     * @return Map of player UUIDs to PlayerStats objects
     */
    Map<UUID, PlayerStats> loadPlayerStats();

    /**
     * Save player stats to the database
     * 
     * @param stats PlayerStats object to save
     */
    void savePlayerStats(PlayerStats stats);

    /**
     * Load escrow balances from the database
     * 
     * @return Map of player UUIDs to escrow balances
     */
    Map<UUID, Double> loadEscrowBalances();

    /**
     * Save escrow balance to the database
     * 
     * @param playerUUID Player UUID
     * @param balance Escrow balance
     */
    void saveEscrowBalance(UUID playerUUID, double balance);

    /**
     * Remove escrow balance from the database
     * 
     * @param playerUUID Player UUID
     */
    void removeEscrowBalance(UUID playerUUID);

    /**
     * Load all coinflip offers from the database
     * 
     * @return Map of player UUIDs to CoinflipOffer objects
     */
    Map<UUID, CoinflipOffer> loadCoinflipOffers();

    /**
     * Save a coinflip offer to the database
     * 
     * @param offer CoinflipOffer object to save
     */
    void saveCoinflipOffer(CoinflipOffer offer);

    /**
     * Remove a coinflip offer from the database
     * 
     * @param playerUUID UUID of the player who created the offer
     */
    void removeCoinflipOffer(UUID playerUUID);
}
