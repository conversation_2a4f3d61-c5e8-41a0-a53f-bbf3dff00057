package ca.xef5000.ariesCoinflip.database;

import ca.xef5000.ariesCoinflip.AriesCoinflip;
import ca.xef5000.ariesCoinflip.models.CoinflipOffer;
import ca.xef5000.ariesCoinflip.models.PlayerStats;
import org.bukkit.Bukkit;

import java.sql.*;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.logging.Level;

public class MySQLManager implements DatabaseManager {
    private final AriesCoinflip plugin;
    private Connection connection;
    private final String host;
    private final int port;
    private final String database;
    private final String username;
    private final String password;

    public MySQLManager(AriesCoinflip plugin, String host, int port, String database, String username, String password) {
        this.plugin = plugin;
        this.host = host;
        this.port = port;
        this.database = database;
        this.username = username;
        this.password = password;
    }

    @Override
    public void initialize() {
        try {
            Class.forName("com.mysql.jdbc.Driver");
            connection = DriverManager.getConnection(
                    "jdbc:mysql://" + host + ":" + port + "/" + database + "?useSSL=false",
                    username,
                    password
            );

            // Create tables if they don't exist
            createTables();

            plugin.getLogger().info("MySQL database connection established.");
        } catch (SQLException | ClassNotFoundException e) {
            plugin.getLogger().log(Level.SEVERE, "Could not connect to MySQL database", e);
        }
    }

    private void createTables() {
        try (Statement statement = connection.createStatement()) {
            // Create player_stats table
            statement.execute("CREATE TABLE IF NOT EXISTS player_stats (" +
                    "uuid VARCHAR(36) PRIMARY KEY, " +
                    "name VARCHAR(16) NOT NULL, " +
                    "games_played INT NOT NULL, " +
                    "games_won INT NOT NULL, " +
                    "games_lost INT NOT NULL, " +
                    "total_won DOUBLE NOT NULL, " +
                    "total_lost DOUBLE NOT NULL" +
                    ")");

            // Create escrow_balances table
            statement.execute("CREATE TABLE IF NOT EXISTS escrow_balances (" +
                    "uuid VARCHAR(36) PRIMARY KEY, " +
                    "balance DOUBLE NOT NULL" +
                    ")");

            // Create coinflip_offers table
            statement.execute("CREATE TABLE IF NOT EXISTS coinflip_offers (" +
                    "uuid VARCHAR(36) PRIMARY KEY, " +
                    "player_name VARCHAR(16) NOT NULL, " +
                    "amount DOUBLE NOT NULL, " +
                    "creation_time BIGINT NOT NULL, " +
                    "timeout_seconds INT NOT NULL" +
                    ")");

            plugin.getLogger().info("Database tables created or already exist.");
        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Could not create database tables", e);
        }
    }

    @Override
    public void close() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                plugin.getLogger().info("MySQL database connection closed.");
            }
        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Could not close MySQL database connection", e);
        }
    }

    @Override
    public Map<UUID, PlayerStats> loadPlayerStats() {
        Map<UUID, PlayerStats> playerStats = new HashMap<>();

        try (Statement statement = connection.createStatement();
             ResultSet resultSet = statement.executeQuery("SELECT * FROM player_stats")) {

            while (resultSet.next()) {
                UUID uuid = UUID.fromString(resultSet.getString("uuid"));
                String name = resultSet.getString("name");
                int gamesPlayed = resultSet.getInt("games_played");
                int gamesWon = resultSet.getInt("games_won");
                int gamesLost = resultSet.getInt("games_lost");
                double totalWon = resultSet.getDouble("total_won");
                double totalLost = resultSet.getDouble("total_lost");

                // Use the constructor that takes all stats fields
                PlayerStats stats = new PlayerStats(uuid, name, gamesPlayed, gamesWon, gamesLost, totalWon, totalLost);

                playerStats.put(uuid, stats);
            }

        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Could not load player stats from database", e);
        }

        return playerStats;
    }

    @Override
    public void savePlayerStats(PlayerStats stats) {
        String sql = "INSERT INTO player_stats (uuid, name, games_played, games_won, games_lost, total_won, total_lost) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?) " +
                "ON DUPLICATE KEY UPDATE " +
                "name = VALUES(name), " +
                "games_played = VALUES(games_played), " +
                "games_won = VALUES(games_won), " +
                "games_lost = VALUES(games_lost), " +
                "total_won = VALUES(total_won), " +
                "total_lost = VALUES(total_lost)";

        try (PreparedStatement statement = connection.prepareStatement(sql)) {
            statement.setString(1, stats.getPlayerUUID().toString());
            statement.setString(2, stats.getPlayerName());
            statement.setInt(3, stats.getGamesPlayed());
            statement.setInt(4, stats.getGamesWon());
            statement.setInt(5, stats.getGamesLost());
            statement.setDouble(6, stats.getTotalWon());
            statement.setDouble(7, stats.getTotalLost());

            statement.executeUpdate();
        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Could not save player stats to database", e);
        }
    }

    @Override
    public Map<UUID, Double> loadEscrowBalances() {
        Map<UUID, Double> escrowBalances = new HashMap<>();

        try (Statement statement = connection.createStatement();
             ResultSet resultSet = statement.executeQuery("SELECT * FROM escrow_balances")) {

            while (resultSet.next()) {
                UUID uuid = UUID.fromString(resultSet.getString("uuid"));
                double balance = resultSet.getDouble("balance");

                escrowBalances.put(uuid, balance);
            }

        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Could not load escrow balances from database", e);
        }

        return escrowBalances;
    }

    @Override
    public void saveEscrowBalance(UUID playerUUID, double balance) {
        String sql = "INSERT INTO escrow_balances (uuid, balance) VALUES (?, ?) " +
                "ON DUPLICATE KEY UPDATE balance = VALUES(balance)";

        try (PreparedStatement statement = connection.prepareStatement(sql)) {
            statement.setString(1, playerUUID.toString());
            statement.setDouble(2, balance);

            statement.executeUpdate();
        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Could not save escrow balance to database", e);
        }
    }

    @Override
    public void removeEscrowBalance(UUID playerUUID) {
        String sql = "DELETE FROM escrow_balances WHERE uuid = ?";

        try (PreparedStatement statement = connection.prepareStatement(sql)) {
            statement.setString(1, playerUUID.toString());

            statement.executeUpdate();
        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Could not remove escrow balance from database", e);
        }
    }

    @Override
    public Map<UUID, CoinflipOffer> loadCoinflipOffers() {
        Map<UUID, CoinflipOffer> coinflipOffers = new HashMap<>();

        try (Statement statement = connection.createStatement();
             ResultSet resultSet = statement.executeQuery("SELECT * FROM coinflip_offers")) {

            while (resultSet.next()) {
                UUID uuid = UUID.fromString(resultSet.getString("uuid"));
                String playerName = resultSet.getString("player_name");
                double amount = resultSet.getDouble("amount");
                long creationTime = resultSet.getLong("creation_time");
                int timeoutSeconds = resultSet.getInt("timeout_seconds");

                // Create a CoinflipOffer object using the constructor that takes all fields
                CoinflipOffer offer = new CoinflipOffer(uuid, playerName, amount, creationTime, timeoutSeconds);

                coinflipOffers.put(uuid, offer);
            }

        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Could not load coinflip offers from database", e);
        }

        return coinflipOffers;
    }

    @Override
    public void saveCoinflipOffer(CoinflipOffer offer) {
        String sql = "INSERT INTO coinflip_offers (uuid, player_name, amount, creation_time, timeout_seconds) " +
                "VALUES (?, ?, ?, ?, ?) " +
                "ON DUPLICATE KEY UPDATE " +
                "player_name = VALUES(player_name), " +
                "amount = VALUES(amount), " +
                "creation_time = VALUES(creation_time), " +
                "timeout_seconds = VALUES(timeout_seconds)";

        try (PreparedStatement statement = connection.prepareStatement(sql)) {
            statement.setString(1, offer.getPlayerUUID().toString());
            statement.setString(2, offer.getPlayerName());
            statement.setDouble(3, offer.getAmount());
            statement.setLong(4, offer.getCreationTime());
            statement.setInt(5, offer.getTimeoutSeconds());

            statement.executeUpdate();
        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Could not save coinflip offer to database", e);
        }
    }

    @Override
    public void removeCoinflipOffer(UUID playerUUID) {
        String sql = "DELETE FROM coinflip_offers WHERE uuid = ?";

        try (PreparedStatement statement = connection.prepareStatement(sql)) {
            statement.setString(1, playerUUID.toString());

            statement.executeUpdate();
        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Could not remove coinflip offer from database", e);
        }
    }
}
