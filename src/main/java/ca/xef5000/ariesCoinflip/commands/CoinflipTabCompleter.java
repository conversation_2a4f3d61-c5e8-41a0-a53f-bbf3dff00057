package ca.xef5000.ariesCoinflip.commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.util.StringUtil;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class CoinflipTabCompleter implements TabCompleter {

    private static final List<String> SUBCOMMANDS = Arrays.asList("create", "reload");
    private static final List<String> CREATE_ARGS_CURRENCY = List.of("money"); // Changed from Arrays.asList

    @Nullable
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (!(sender instanceof Player)) {
            return null; // Or Collections.emptyList();
        }

        Player player = (Player) sender;

        final List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            StringUtil.copyPartialMatches(args[0], SUBCOMMANDS, completions);
        } else if (args.length == 2) {
            if (args[0].equalsIgnoreCase("create")) {
                if (player.hasPermission("ariescoinflip.create")) {
                    StringUtil.copyPartialMatches(args[1], CREATE_ARGS_CURRENCY, completions);
                }
            }
        } else if (args.length == 3) {
            if (args[0].equalsIgnoreCase("create") && args[1].equalsIgnoreCase("money")) {
                if (player.hasPermission("ariescoinflip.create")) {
                    completions.add("<amount>"); // Suggests the type of input expected
                }
            }
        }

        return completions;
    }
}
