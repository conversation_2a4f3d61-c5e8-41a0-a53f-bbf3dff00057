package ca.xef5000.ariesCoinflip.commands;

import ca.xef5000.ariesCoinflip.AriesCoinflip;
import ca.xef5000.ariesCoinflip.managers.CoinflipManager;
import ca.xef5000.ariesCoinflip.managers.GUIManager;
import ca.xef5000.ariesCoinflip.utils.ConfigManager;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

public class CoinflipCommand implements CommandExecutor {
    private final ConfigManager configManager;
    private final GUIManager guiManager;
    private final CoinflipManager coinflipManager;

    public CoinflipCommand(AriesCoinflip plugin, ConfigManager configManager, GUIManager guiManager) {
        this.configManager = configManager;
        this.guiManager = guiManager;
        this.coinflipManager = plugin.getCoinflipManager();
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("This command can only be used by players.");
            return true;
        }

        Player player = (Player) sender;

        // Check if there are any arguments
        if (args.length > 0) {
            // Handle subcommands
            if (args[0].equalsIgnoreCase("create")) {
                if (!player.hasPermission("ariescoinflip.create")) {
                    player.sendMessage(configManager.getMessage("no_permission_create"));
                    return true;
                }
                // Handle create subcommand
                if (args.length < 3) {
                    player.sendMessage(ConfigManager.colorize("&cUsage: /coinflip create [currency] [amount]"));
                    return true;
                }

                String currency = args[1].toLowerCase();
                if (!currency.equals("money")) {
                    player.sendMessage(ConfigManager.colorize("&cOnly 'money' currency is currently supported."));
                    return true;
                }

                try {
                    double amount = Double.parseDouble(args[2]);
                    coinflipManager.createCoinflip(player, amount);
                } catch (NumberFormatException e) {
                    player.sendMessage(ConfigManager.colorize("&cInvalid amount. Please enter a valid number."));
                }
                return true;
            } else if (args[0].equalsIgnoreCase("reload")) {
                if (!player.hasPermission("ariescoinflip.reload")) {
                    player.sendMessage(configManager.getMessage("no_permission_reload"));
                    return true;
                }
                configManager.loadConfig();
                // Reload config for other managers if necessary
                // For example, if GUIManager caches titles or other config values, it might need a refresh method.
                // For now, assuming ConfigManager.loadConfig() is sufficient for its own state.
                // And other managers fetch values on-demand using getConfigManager().getX()
                player.sendMessage(configManager.getMessage("config_reloaded"));
                return true;
            }
        }

        // Default: Open the main coinflip menu
        if (!player.hasPermission("ariescoinflip.use")) {
            player.sendMessage(configManager.getMessage("no_permission"));
            return true;
        }
        guiManager.openMainMenu(player);
        return true;
    }
}
