package ca.xef5000.trueDark.config;

import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.plugin.java.JavaPlugin;

/**
 * Manages the configuration for the TrueDark plugin.
 */
public class ConfigManager {
    private final JavaPlugin plugin;
    private FileConfiguration config;

    // Configuration values
    private int visibilityRadius;
    private int darknessThreshold;
    private boolean enableNightDarkness;
    private boolean enableCaveDarkness;
    private Material darknessMaterial;
    private int chunksPerTick;
    private int updateFrequency;
    private int blockStepSize;
    private boolean enableAsync;
    private boolean debug;

    /**
     * Creates a new ConfigManager instance.
     *
     * @param plugin The plugin instance
     */
    public ConfigManager(JavaPlugin plugin) {
        this.plugin = plugin;
        loadConfig();
    }

    /**
     * Loads or reloads the configuration from the config.yml file.
     */
    public void loadConfig() {
        plugin.saveDefaultConfig();
        plugin.reloadConfig();
        config = plugin.getConfig();

        // Load configuration values
        visibilityRadius = config.getInt("visibility-radius", 5);
        darknessThreshold = config.getInt("darkness-threshold", 7);
        enableNightDarkness = config.getBoolean("enable-night-darkness", true);
        enableCaveDarkness = config.getBoolean("enable-cave-darkness", true);

        String materialName = config.getString("darkness-material", "BLACK_CONCRETE");
        try {
            darknessMaterial = Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("Invalid material in config: " + materialName + ". Using BLACK_CONCRETE instead.");
            darknessMaterial = Material.BLACK_CONCRETE;
        }

        chunksPerTick = config.getInt("chunks-per-tick", 3);
        updateFrequency = config.getInt("update-frequency", 10);
        blockStepSize = config.getInt("block-step-size", 8);
        enableAsync = config.getBoolean("enable-async", true);
        debug = config.getBoolean("debug", false);
    }

    /**
     * Gets the visibility radius around players.
     *
     * @return The visibility radius in blocks
     */
    public int getVisibilityRadius() {
        return visibilityRadius;
    }

    /**
     * Gets the darkness threshold.
     *
     * @return The darkness threshold (0-15)
     */
    public int getDarknessThreshold() {
        return darknessThreshold;
    }

    /**
     * Checks if night darkness is enabled.
     *
     * @return true if night darkness is enabled
     */
    public boolean isNightDarknessEnabled() {
        return enableNightDarkness;
    }

    /**
     * Checks if cave darkness is enabled.
     *
     * @return true if cave darkness is enabled
     */
    public boolean isCaveDarknessEnabled() {
        return enableCaveDarkness;
    }

    /**
     * Gets the material to use for darkness.
     *
     * @return The darkness material
     */
    public Material getDarknessMaterial() {
        return darknessMaterial;
    }

    /**
     * Gets the maximum number of chunks to process per tick.
     *
     * @return The chunks per tick
     */
    public int getChunksPerTick() {
        return chunksPerTick;
    }

    /**
     * Gets the update frequency in ticks.
     *
     * @return The update frequency
     */
    public int getUpdateFrequency() {
        return updateFrequency;
    }

    /**
     * Checks if debug mode is enabled.
     *
     * @return true if debug mode is enabled
     */
    public boolean isDebugEnabled() {
        return debug;
    }
}
