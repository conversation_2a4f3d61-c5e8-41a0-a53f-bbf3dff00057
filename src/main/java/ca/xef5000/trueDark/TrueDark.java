package ca.xef5000.trueDark;

import ca.xef5000.trueDark.config.ConfigManager;
import com.github.retrooper.packetevents.PacketEvents;
import io.github.retrooper.packetevents.factory.spigot.SpigotPacketEventsBuilder;
import org.bukkit.plugin.java.JavaPlugin;

public final class TrueDark extends JavaPlugin {
    private ConfigManager configManager;

    @Override
    public void onLoad() {
        PacketEvents.setAPI(SpigotPacketEventsBuilder.build(this));
        //On Bukkit, calling this here is essential, hence the name "load"
        PacketEvents.getAPI().load();
    }

    @Override
    public void onEnable() {
        // Save default config
        saveDefaultConfig();
        configManager = new ConfigManager(this);
        configManager.loadConfig();

        PacketEvents.getAPI().init();

        getLogger().info("TrueDark has been enabled!");
    }

    @Override
    public void onDisable() {
        PacketEvents.getAPI().terminate();

        getLogger().info("TrueDark has been disabled!");
    }
}
