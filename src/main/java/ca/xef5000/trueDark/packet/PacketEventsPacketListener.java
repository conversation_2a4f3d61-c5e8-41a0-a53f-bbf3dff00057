package ca.xef5000.trueDark.packet;

import ca.xef5000.trueDark.TrueDark;
import com.github.retrooper.packetevents.event.PacketListener;
import com.github.retrooper.packetevents.event.PacketSendEvent;
import com.github.retrooper.packetevents.protocol.packettype.PacketType;
import com.github.retrooper.packetevents.protocol.player.User;
import com.github.retrooper.packetevents.protocol.world.chunk.BaseChunk;
import com.github.retrooper.packetevents.protocol.world.chunk.Column;
import com.github.retrooper.packetevents.protocol.world.states.WrappedBlockState;
import com.github.retrooper.packetevents.protocol.world.states.type.StateTypes;
import com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerChunkData;
import org.bukkit.World;
import org.bukkit.entity.Player;

public class PacketEventsPacketListener implements PacketListener {

    private final TrueDark plugin;

    public PacketEventsPacketListener(TrueDark plugin) {
        this.plugin = plugin;
    }


    @Override
    public void onPacketSend(PacketSendEvent event) {
        // The user represents the player.
        User user = event.getUser();
        // Identify what kind of packet it is.
        if (event.getPacketType() != PacketType.Play.Server.CHUNK_DATA)
            return;
        // Use the correct wrapper to process this packet.
        WrapperPlayServerChunkData chunkData = new WrapperPlayServerChunkData(event);
        Column column = chunkData.getColumn();
        int chunkX = column.getX();
        int chunkZ = column.getZ();
        Player player = event.getPlayer();

        BaseChunk[] chunks = column.getChunks();

        for (int i = 0; i < chunks.length; i++) {
            final int chunkI = i;
            BaseChunk chunk = chunks[chunkI];
            if (chunk == null) continue;
            modifyChunk(chunk, chunkX, chunkZ, chunkI, player.getWorld(), player);
        }
    }

    private void modifyChunk(BaseChunk chunk, int chunkX, int chunkZ, int chunkI, World world, Player player) {
        WrappedBlockState darkBlock = WrappedBlockState.getDefaultState(StateTypes.BLACK_CONCRETE);
        for (int y = 0; y < 16; y++) {
            for (int x = 0; x < 16; x++) {
                for (int z = 0; z < 16; z++) {
                    WrappedBlockState blockStates = chunk.get(x, y, z);
                    if (blockStates == null) continue;

                    int globalX = (chunkX << 4) + x;
                    int globalZ = (chunkZ << 4) + z;
                    int globalY = (chunkI << 4) + y + world.getMinHeight();

                    if (blockStates.getType() == StateTypes.AIR && getDistance(globalX, globalY, globalZ, player) > 5 && isDark(globalX, globalY, globalZ, world) ) {
                        chunk.set(x, y, z, darkBlock);
                    }
                }
            }
        }
    }

    private double getDistance(int x, int y, int z, Player player) {
        return Math.sqrt(Math.pow(x - player.getLocation().getBlockX(), 2) + Math.pow(y - player.getLocation().getBlockY(), 2) + Math.pow(z - player.getLocation().getBlockZ(), 2));
    }

    private boolean isDark(int x, int y, int z, World world) {
        return world.getBlockAt(x, y, z).getLightLevel() <= plugin.getConfigManager().getDarknessThreshold();
    }
}
