package ca.xef5000.trueDark.listeners;

import ca.xef5000.trueDark.TrueDark;
import ca.xef5000.trueDark.config.ConfigManager;
import com.github.retrooper.packetevents.PacketEvents;
import com.github.retrooper.packetevents.protocol.player.User;
import com.github.retrooper.packetevents.protocol.world.states.WrappedBlockState;
import com.github.retrooper.packetevents.protocol.world.states.type.StateTypes;
import com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerMultiBlockChange;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.block.data.BlockData;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerChangedWorldEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.event.player.PlayerQuitEvent;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class DarknessManager implements Listener {

    private final TrueDark plugin;
    private final Map<UUID, Set<Location>> revealedBlocks = new ConcurrentHashMap<>();
    private final Map<UUID, Long> playerLastMoveUpdateTimeMillis = new ConcurrentHashMap<>();
    private long moveUpdateCooldownMillis;

    private int clearRadiusSquared;         // e.g., 5*5 = 25 (blocks within this are shown as real)
    private int darkenRadiusSquared;        // e.g., 15*15 = 225 (blocks between clear and darken radius can be faked)
    private int darknessThreshold;          // e.g., 4 (light level below or equal to this is "dark")
    private int moveUpdateCooldownTicks;    // e.g., 3-5 ticks
    private Material fakeDarkBlockMaterial = Material.BLACK_CONCRETE;

    public DarknessManager(TrueDark plugin) {
        this.plugin = plugin;
        loadConfigValues(); // Method to load values from your plugin's config
        Bukkit.getPluginManager().registerEvents(this, plugin);
    }

    private void loadConfigValues() {
        ConfigManager config = plugin.getConfigManager();
        this.clearRadiusSquared = config.getVisibilityRadius() * config.getVisibilityRadius();
        this.darkenRadiusSquared = config.getVisibilityRadius() * 3 * config.getVisibilityRadius() * 3;
        this.darknessThreshold = config.getDarknessThreshold();
        this.moveUpdateCooldownTicks = config.getUpdateFrequency();
        this.moveUpdateCooldownMillis = config.getUpdateFrequency() * 50L;
        this.fakeDarkBlockMaterial = config.getDarknessMaterial();
    }

    @EventHandler(priority = org.bukkit.event.EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();
        Location from = event.getFrom();
        Location to = event.getTo();

        if (to == null || (from.getBlockX() == to.getBlockX() && from.getBlockY() == to.getBlockY() && from.getBlockZ() == to.getBlockZ())) {
            return; // Only rotation or minor movement within the same block
        }

        long currentTime = System.currentTimeMillis();
        if (playerLastMoveUpdateTimeMillis.getOrDefault(player.getUniqueId(), 0L) + moveUpdateCooldownMillis > currentTime) {
            return; // Cooldown
        }
        playerLastMoveUpdateTimeMillis.put(player.getUniqueId(), currentTime);

        processPlayerMovement(player);
    }

    private void processPlayerMovement(Player player) {
        if (!player.isOnline()) {
            cleanupPlayer(player.getUniqueId());
            return;
        }

        Location playerLocation = player.getLocation();
        World world = player.getWorld();
        Set<Location> currentRevealedBlocks = revealedBlocks.computeIfAbsent(player.getUniqueId(), k -> new HashSet<>());
        Set<Location> newRevealedBlocks = new HashSet<>(); // Blocks that will be revealed

        // Iterate a bounding box that covers the maximum "darkenRadius"
        int maxRadius = (int) Math.sqrt(darkenRadiusSquared);
        int playerBlockX = playerLocation.getBlockX();
        int playerBlockY = playerLocation.getBlockY();
        int playerBlockZ = playerLocation.getBlockZ();

        for (int dx = -maxRadius; dx <= maxRadius; dx++) {
            for (int dy = -maxRadius; dy <= maxRadius; dy++) { // Consider a smaller vertical radius for performance
                for (int dz = -maxRadius; dz <= maxRadius; dz++) {
                    int currentX = playerBlockX + dx;
                    int currentY = playerBlockY + dy; // Be mindful of Y range relative to player
                    int currentZ = playerBlockZ + dz;

                    // Ensure Y is within world bounds
                    if (currentY < world.getMinHeight() || currentY > world.getMaxHeight()) {
                        continue;
                    }

                    Location blockLocation = new Location(world, currentX, currentY, currentZ);
                    double distanceSq = playerLocation.distanceSquared(blockLocation);

                    //Block actualBlock = blockLocation.getBlock(); // Get the real block on the server

                    // 1. Handle the "Clear Zone" (within clearRadius)
                    if (distanceSq <= clearRadiusSquared) {
                        if (currentRevealedBlocks.contains(blockLocation)) {
                            // This block was previously faked by us, revert it
                            newRevealedBlocks.add(blockLocation);
                        }
                    }
                }
            }
        }

        Set<Location> blocksToDarken = new HashSet<>();
        for (Location currentlyRevealed : currentRevealedBlocks) {
            if (!newRevealedBlocks.contains(currentlyRevealed))
                blocksToDarken.add(currentlyRevealed);
        }

        // Update the authoritative set of faked blocks for this player
        revealedBlocks.put(player.getUniqueId(), newRevealedBlocks);
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        cleanupAndRevertPlayer(event.getPlayer());
    }

    @EventHandler
    public void onPlayerChangedWorld(PlayerChangedWorldEvent event) {
        cleanupAndRevertPlayer(event.getPlayer()); // Revert blocks from old world
        // Optionally, trigger an immediate update for the new world if needed
        // Bukkit.getScheduler().runTaskLater(plugin, () -> processPlayerMovement(event.getPlayer()), 1L);
    }

    private void multiBlockChange(Set<Location> locations, Material material, Player player) {
        User user = PacketEvents.getAPI().getPlayerManager().getUser(player);
        BlockData blockData = Bukkit.createBlockData(material);
        WrappedBlockState.getDefaultState(StateTypes.getByName(material.name()));
        int globalBlockId = PacketEvents.getAPI().getNMSUtils().getBlockStateId(blockData);
        Map<ChunkSectionIdentifier, List<Location>> locationsByChunkSection = new HashMap<>();
        for (Location loc : locations) {
            // Ensure the location is in the player's world, though this packet doesn't strictly require it,
            // sending blocks for a world the player isn't in is usually pointless.
            // For simplicity, we assume all locations are valid for this player.

            int chunkX = loc.getBlockX() >> 4; // loc.getBlockX() / 16
            int sectionY = loc.getBlockY() >> 4; // loc.getBlockY() / 16 (this is the Y index of the chunk section)
            int chunkZ = loc.getBlockZ() >> 4; // loc.getBlockZ() / 16

            ChunkSectionIdentifier csi = new ChunkSectionIdentifier(chunkX, sectionY, chunkZ);
            locationsByChunkSection.computeIfAbsent(csi, k -> new ArrayList<>()).add(loc);
        }

        for (Map.Entry<ChunkSectionIdentifier, List<Location>> entry : locationsByChunkSection.entrySet()) {
            ChunkSectionIdentifier csi = entry.getKey();
            List<Location> sectionLocations = entry.getValue();

            WrapperPlayServerMultiBlockChange.EncodedBlock[] encodedBlocks =
                    new WrapperPlayServerMultiBlockChange.EncodedBlock[sectionLocations.size()];

            for (int i = 0; i < sectionLocations.size(); i++) {
                Location loc = sectionLocations.get(i);

                // Calculate relative coordinates within the chunk section (0-15)
                int relX = loc.getBlockX() & 0xF; // loc.getBlockX() % 16
                int relY = loc.getBlockY() & 0xF; // loc.getBlockY() % 16 (relative to section start)
                int relZ = loc.getBlockZ() & 0xF; // loc.getBlockZ() % 16

                // Pack relative coordinates into a short:
                // X (4 bits), Z (4 bits), Y (8 bits)
                // Note: The y in EncodedBlock is often the lower 8 bits, meaning relative to the chunk section.
                // The packet structure is: (XZ packed into one byte, Y into another byte for each block)
                // The `short position` in `EncodedBlock` constructor is: (relX << 12) | (relZ << 8) | relY
                // This seems to be how PacketEvents expects it for its wrapper.
                short packedPosition = (short) ((relX << 12) | (relZ << 8) | relY);

                encodedBlocks[i] = new WrapperPlayServerMultiBlockChange.EncodedBlock(globalBlockId, packedPosition);
            }

        user.sendPacket(new WrapperPlayServerMultiBlockChange());
    }

    private void cleanupAndRevertPlayer(Player player) {
        Set<Location> fakedBlocks = revealedBlocks.remove(player.getUniqueId());
        playerLastMoveUpdateTimeMillis.remove(player.getUniqueId());

        if (fakedBlocks != null && player.isOnline()) { // Check isOnline in case of immediate disconnect
            for (Location loc : fakedBlocks) {
                // Check if the chunk is loaded before trying to get the block
                // This is important especially if the player is quitting/changing worlds rapidly
                if (loc.isWorldLoaded() && loc.getChunk().isLoaded()) {
                    Block actualBlock = loc.getBlock();
                    player.sendBlockChange(loc, actualBlock.getBlockData());
                }
            }
        }
    }

    private void cleanupPlayer(UUID playerUUID) { // For when player is not online
        revealedBlocks.remove(playerUUID);
        playerLastMoveUpdateTimeMillis.remove(playerUUID);
    }

    // Call this in onDisable of your plugin
    public void disable() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            cleanupAndRevertPlayer(player);
        }
        revealedBlocks.clear();
        playerLastMoveUpdateTimeMillis.clear();
    }

    private record ChunkSectionIdentifier(int chunkX, int sectionY, int chunkZ) {
    }
}
