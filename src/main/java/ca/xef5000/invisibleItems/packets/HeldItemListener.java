package ca.xef5000.invisibleItems.packets;

import com.github.retrooper.packetevents.event.PacketListener;
import com.github.retrooper.packetevents.event.PacketReceiveEvent;
import com.github.retrooper.packetevents.protocol.packettype.PacketType;

public class HeldItemListener implements PacketListener {

    @Override
    public void onPacketReceive(PacketReceiveEvent event) {
        try {
            if (!(event.getPacketType() == PacketType.Play.Client.HELD_ITEM_CHANGE))
                return;



        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
