package fr.minepiece.client.render.layers;

import fr.minepiece.client.model.modifications.AbstractPlayerModification;
import fr.minepiece.client.model.registry.ModelRegistry;
import fr.minepiece.common.capability.playermodifications.IPlayerModifications;
import fr.minepiece.common.capability.playermodifications.PlayerModificationsStorage;
import net.minecraft.client.entity.AbstractClientPlayer;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.entity.RenderPlayer;
import net.minecraft.client.renderer.entity.layers.LayerRenderer;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

import java.util.ArrayList;
import java.util.List;

/**
 * Optimized layer for rendering custom player skin modifications
 */
@SideOnly(Side.CLIENT)
public class OptimizedLayerCustomSkinRender implements LayerRenderer<AbstractClientPlayer> {
    private final RenderPlayer playerRenderer;
    
    public OptimizedLayerCustomSkinRender(RenderPlayer playerRenderer) {
        this.playerRenderer = playerRenderer;
    }
    
    @Override
    public void doRenderLayer(AbstractClientPlayer entitylivingbaseIn, float limbSwing, float limbSwingAmount, float partialTicks, float ageInTicks, float netHeadYaw, float headPitch, float scale) {
        // Get player modifications
        IPlayerModifications modifications = entitylivingbaseIn.getCapability(PlayerModificationsStorage.PLAYER_MODIFICATIONS_CAPABILITY, null);
        if (modifications == null || modifications.getModifications().isEmpty()) {
            return;
        }
        
        GlStateManager.pushMatrix();
        
        // Get all modifications
        List<AbstractPlayerModification> playerModifications = new ArrayList<>();
        for (String modificationName : modifications.getModifications()) {
            AbstractPlayerModification modification = ModelRegistry.getModification(modificationName);
            if (modification != null) {
                playerModifications.add(modification);
            }
        }
        
        // Render all modifications
        for (AbstractPlayerModification modification : playerModifications) {
            modification.render(playerRenderer, entitylivingbaseIn, limbSwing, limbSwingAmount, partialTicks, ageInTicks, netHeadYaw, headPitch, scale);
        }
        
        GlStateManager.popMatrix();
    }
    
    @Override
    public boolean shouldCombineTextures() {
        return false;
    }
}
