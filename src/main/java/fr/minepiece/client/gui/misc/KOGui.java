package fr.minepiece.client.gui.misc;

import fr.minepiece.Reference;
import fr.minepiece.client.gui.component.button.GuiAPI;
import fr.minepiece.client.gui.component.button.GuiUtils;
import net.minecraft.client.gui.GuiScreen;
import net.minecraft.util.ResourceLocation;

public class KOGui extends GuiAPI {

    private static final ResourceLocation background = new ResourceLocation(Reference.MOD_ID,"textures/gui/new/composants/death/background.png");

    private static final ResourceLocation check = new ResourceLocation(Reference.MOD_ID,"textures/gui/new/composants/death/check-off.png");
    private static final ResourceLocation hoveredCheck = new ResourceLocation(Reference.MOD_ID,"textures/gui/new/composants/popup/check-on.png");


    private static final ResourceLocation close = new ResourceLocation(Reference.MOD_ID,"textures/gui/new/composants/popup/x-off.png");
    private static final ResourceLocation hoveredClose = new ResourceLocation(Reference.MOD_ID,"textures/gui/new/composants/popup/x-on.png");

    public KOGui(GuiScreen prev) {
        super(prev, "ko");
    }

    @Override
    public void initGui() {
        super.initGui();
    }

    @Override
    public void drawBackground(int mouseX, int mouseY, float ticks) {
        this.drawDefaultBackground();
        GuiUtils.drawImageTransparent(width(31.0F),height(27.93F), background, width(37.96F), height(44.05F));
    }
}
