package fr.minepiece.client.gui.component.textarea;


import fr.minepiece.client.gui.component.TextAlign;
import fr.minepiece.client.gui.component.TextOverflow;
import fr.minepiece.client.gui.component.button.GuiComponent;
import fr.minepiece.client.gui.component.button.GuiUtils;
import fr.minepiece.client.gui.component.fontv3.IFont;
import net.minecraft.client.Minecraft;

import java.awt.*;
import java.util.ArrayList;

public class GuiCustomTextLabel extends GuiComponent {
    public String text;

    public IFont font;

    public int fontSize;

    public Color textColor;

    public TextAlign textAlign;

    public TextOverflow textOverflow;

    public boolean textShadow;

    public Color textShadowColor;

    public GuiCustomTextLabel(double x, double y, double width, double height, String text, IFont font, int fontSize, Color color) {
        super(x, y, width,height);
        this.text = text;
        this.font = font;
        this.fontSize = fontSize;
        this.textAlign = TextAlign.LEFT;
        this.textColor = color;
        this.textOverflow = TextOverflow.NONE;
        this.textShadowColor = Color.BLACK;
    }

    public void draw(Minecraft mc, int mouseX, int mouseY) {
        super.draw(mc, mouseX, mouseY);

        int posX = 0;
        int strWidth = GuiUtils.getStringWidth(Minecraft.getMinecraft(), this.text, this.font, this.fontSize);

        if (strWidth <= this.width) {
            if (this.textAlign == TextAlign.CENTER) {
                posX = (int) -(this.width / 2.0D - (GuiUtils.getStringWidth(mc, this.text, this.font, this.fontSize) / 2));
            } else if (this.textAlign == TextAlign.RIGHT) {
                posX = GuiUtils.getStringWidth(mc, this.text, this.font, this.fontSize);
            }
            GuiUtils.drawStringWithCustomFont(mc, this.text, this.x - posX, this.y+3, this.textColor, this.font, this.fontSize);
        } else {
            int posY = (int) this.y;
            int lineHeight = (int) font.getFontHeight() + 1;
            ArrayList<String> lines = wrapText(this.text, this.width, this.font, this.fontSize);

            // Ajuster posY pour remonter légèrement le texte de la première ligne
            if (lines.size() > 1) {
                int lineHeightOffset = (int) ((lineHeight - font.getFontHeight()) / 2.0f) + 2;
                posY -= lineHeightOffset;
            }

            for (String line : lines) {
                if (this.textAlign == TextAlign.CENTER) {
                    posX = (int) -(this.width / 2.0D - (GuiUtils.getStringWidth(mc, line, this.font, this.fontSize) / 2));
                } else if (this.textAlign == TextAlign.RIGHT) {
                    posX = GuiUtils.getStringWidth(mc, line, this.font, this.fontSize);
                }

                GuiUtils.drawStringWithCustomFont(mc, line, this.x - posX, posY, this.textColor, this.font, this.fontSize);
                posY += lineHeight;
            }
        }
    }

    public static ArrayList<String> wrapText(String text, double width, IFont font, float fontSize) {
        ArrayList<String> lines = new ArrayList<>();
        StringBuilder currentLine = new StringBuilder();
        String[] words = text.split(" ");

        for (String word : words) {
            // Vérifier si le mot lui-même dépasse la largeur spécifiée
            float wordWidth = font.getWidth(word, fontSize);
            if (wordWidth > width) {
                // Si le mot dépasse la largeur, le découper en plusieurs parties
                String[] subWords = splitWord(word, width, font, fontSize);
                for (String subWord : subWords) {
                    float lineWidth = font.getWidth(currentLine.toString(), fontSize);
                    float subWordWidth = font.getWidth(subWord, fontSize);

                    if (lineWidth + subWordWidth <= width) {
                        currentLine.append(subWord).append(" ");
                    } else {
                        lines.add(currentLine.toString().trim());
                        currentLine = new StringBuilder(subWord + " ");
                    }
                }
            } else {
                float lineWidth = font.getWidth(currentLine.toString(), fontSize);

                if (lineWidth + wordWidth <= width) {
                    currentLine.append(word).append(" ");
                } else {
                    lines.add(currentLine.toString().trim());
                    currentLine = new StringBuilder(word + " ");
                }
            }
        }

        // Ajouter la dernière ligne à la liste
        lines.add(currentLine.toString().trim());

        return lines;
    }

    private static String[] splitWord(String word, double width, IFont font, float fontSize) {
        ArrayList<String> subWords = new ArrayList<>();
        StringBuilder currentSubWord = new StringBuilder();

        for (int i = 0; i < word.length(); i++) {
            char c = word.charAt(i);
            float subWordWidth = font.getWidth(currentSubWord.toString() + c, fontSize);

            if (subWordWidth <= width) {
                currentSubWord.append(c);
            } else {
                // Le sous-mot dépasse la largeur, l'ajouter à la liste et recommencer avec un nouveau sous-mot
                subWords.add(currentSubWord.toString());
                currentSubWord = new StringBuilder(String.valueOf(c));
            }
        }

        // Ajouter le dernier sous-mot à la liste
        subWords.add(currentSubWord.toString());

        return subWords.toArray(new String[0]);
    }


  /*  public static ArrayList<String> wrapText(String text, double width, IFont font, float fontSize) {
        ArrayList<String> lines = new ArrayList<>();
        StringBuilder currentLine = new StringBuilder();
        String[] words = text.split(" ");

        for (String word : words) {
            float lineWidth = font.getWidth(currentLine.toString(), fontSize);
            float wordWidth = font.getWidth(word, fontSize);

            if (lineWidth + wordWidth <= width) {
                currentLine.append(word).append(" ");
            } else {
                lines.add(currentLine.toString().trim());
                currentLine = new StringBuilder(word + " ");
            }
        }

        // Ajouter la dernière ligne à la liste
        lines.add(currentLine.toString().trim());

        return lines;
    }*/

    public boolean onClick(int mouseX, int mouseY, int mouseButton) {
        return false;
    }

    public void onRelease(int mouseX, int mouseY, int mouseButton) {
    }

    public void onHover(int mouseX, int mouseY) {
    }

    public void onHoverOut(int mouseX, int mouseY) {
    }

    public void fixedUpdate() {
    }

    public void onKeyTyped(char c, int key) {
    }

    public GuiCustomTextLabel setTextColor(Color textColor) {
        this.textColor = textColor;
        return this;
    }

    public GuiCustomTextLabel setTextAlign(TextAlign textAlign) {
        this.textAlign = textAlign;
        return this;
    }

    public GuiCustomTextLabel setTextOverflow(TextOverflow textOverflow) {
        this.textOverflow = textOverflow;
        return this;
    }

    public GuiCustomTextLabel setTextShadow(boolean textShadow) {
        this.textShadow = textShadow;
        return this;
    }

    public GuiCustomTextLabel setTextShadowColor(Color textShadowColor) {
        this.textShadowColor = textShadowColor;
        return this;
    }

    public void setText(String text) {
        this.text = text;
    }

    public void setFont(IFont font) {
        this.font = font;
    }

    public void setFontSize(int fontSize) {
        this.fontSize = fontSize;
    }
}
