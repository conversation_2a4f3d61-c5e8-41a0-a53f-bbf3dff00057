package fr.minepiece.client.gui.component.button;

import fr.minepiece.client.gui.component.AbstractUI;
import fr.minepiece.client.gui.component.HoverObject;
import fr.minepiece.client.gui.component.button.scroll.ScrollableArea;
import fr.minepiece.client.gui.component.fontv3.IFont;
import fr.minepiece.client.ClientProxy;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.texture.TextureManager;
import net.minecraft.util.ResourceLocation;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

public abstract class GuiComponent {

    private static final Map<String, AtomicReference<ResourceLocation>> cacheImage = new HashMap<>();
    public final double defaultX;
    public final double defaultY;
    public final double defaultWidth;
    public final double defaultHeight;
    public double x;
    public double y;
    public double width;
    public double height;
    public int zindex;
    public AbstractUI ui;
    public GuiComponent parent;
    public List<GuiComponent> children = new ArrayList<>();
    public boolean hovered;
    public boolean enabled = true;
    public boolean visible = true;
    public ScrollableArea area;
    public boolean solid = true;
    public double scroll;
    private List<String> hovers = new ArrayList<>();
    private Color hoverColor;
    private final IFont hoverFont = ClientProxy.text;
    private String tooltipsText;

    public GuiComponent(double x, double y, double width, double height) {
        this.defaultX = x;
        this.defaultY = y;
        this.x = x;
        this.y = y;
        this.defaultWidth = width;
        this.defaultHeight = height;
        this.width = width;
        this.height = height;
    }

    public double getDefaultX() {
        return this.defaultX;
    }

    public double getDefaultY() {
        return this.defaultY;
    }

    public double getX() {
        return this.x;
    }

    public void setX(double x) {
        this.x = x;
    }

    public double getY() {
        return this.y;
    }

    public void setY(double y) {
        this.y = y;
    }

    public double getDefaultWidth() {
        return this.defaultWidth;
    }

    public double getDefaultHeight() {
        return this.defaultHeight;
    }

    public double getWidth() {
        return this.width;
    }

    public void setWidth(double width) {
        this.width = width;
    }

    public double getHeight() {
        return this.height;
    }

    public void setHeight(double height) {
        this.height = height;
    }

    public int getZindex() {
        return this.zindex;
    }

    public GuiComponent setZindex(int zindex) {
        this.zindex = zindex;
        return this;
    }

    public AbstractUI getUi() {
        return this.ui;
    }

    public void setUi(AbstractUI ui) {
        this.ui = ui;
    }

    public GuiComponent getParent() {
        return this.parent;
    }

    //private FontObj hoverFont = Fonts.MONTSERRAT_BOLD.getFont();

    public void setParent(GuiComponent parent) {
        this.parent = parent;
    }

    public List<GuiComponent> getChildren() {
        return this.children;
    }

    public void setChildren(List<GuiComponent> children) {
        this.children = children;
    }

    public boolean isHovered() {
        return this.hovered;
    }

    public void setHovered(boolean hovered) {
        this.hovered = hovered;
    }

    public boolean isEnabled() {
        return this.enabled;
    }

    public GuiComponent setEnabled(boolean enabled) {
        this.enabled = enabled;
        return this;
    }

    public boolean isVisible() {
        return this.visible;
    }

    public GuiComponent setVisible(boolean visible) {
        this.visible = visible;
        return this;
    }

    public List<String> getHovers() {
        return this.hovers;
    }

    public void setHovers(List<String> hovers) {
        this.hovers = hovers;
    }

    public Color getHoverColor() {
        return this.hoverColor;
    }

    public GuiComponent setHoverColor(Color color) {
        this.hoverColor = color;
        return this;
    }

    public boolean isSolid() {
        return this.solid;
    }

    public GuiComponent setSolid(boolean solid) {
        this.solid = solid;
        return this;
    }

    public double getScroll() {
        return this.scroll;
    }

    public void setScroll(double scroll) {
        this.scroll = scroll;
    }

    public void update(Minecraft mc, int mouseX, int mouseY) {
        updateScroll();
    }

    private void updateScroll() {
        if (this.area != null) {
            int fps = Integer.parseInt((Minecraft.getMinecraft()).debug.split(" fps")[0]);
            double diff = this.defaultY + this.scroll - this.y;
            double absDiff = Math.abs(diff);
            double offset = 0.2D / (((fps == 0) ? 1 : fps) / 60.0F) * absDiff / 3.0D;
            if (this.area.isCursorScrolling())
                offset = 0.4D / (((fps == 0) ? 1 : fps) / 60.0F) * absDiff;
            if (absDiff > (this.area.isCursorScrolling() ? 0.4D : 0.2D)) {
                this.y += (diff > 0.0D) ? offset : -offset;
            } else {
                this.y = this.defaultY + this.scroll;
            }
        }
    }

    public void draw(Minecraft mc, int mouseX, int mouseY) {
        if (!this.hovered && isHovered(mouseX, mouseY)) {
            onHover(mouseX, mouseY);
        }
        if (this.hovered && !isHovered(mouseX, mouseY)) {
            onHoverOut(mouseX, mouseY);
        }
        this.hovered = isHovered(mouseX, mouseY);
        if (isHovered(mouseX, mouseY))
            GuiUtils.hovers.add(new HoverObject(mouseX, mouseY, this.hovers, this.hoverFont, this.hoverColor));
    }

    public boolean isHovered(int mouseX, int mouseY) {
        if (getUi() != null && (getUi()).popup != null)
            return false;
        ScrollableArea checkArea = getArea();
        if (checkArea != null && !checkArea.inArea(mouseX, mouseY))
            return false;
        return (mouseX > getAbsoluteX() && mouseX < getAbsoluteX() + this.width && mouseY > getAbsoluteY() && mouseY < getAbsoluteY() + this.height);
    }

    public ScrollableArea getArea() {
        return (getParent() != null) ? getParent().getArea() : this.area;
    }

    public GuiComponent setArea(ScrollableArea area) {
        this.area = area;
        return this;
    }

    public GuiComponent addHover(String text) {
        this.hovers.add(text);
        return this;
    }

    public GuiComponent addHover(List<String> texts) {
        this.hovers.addAll(texts);
        return this;
    }

    public GuiComponent addChild(GuiComponent node) {
        if (getChildren().contains(node))
            return this;
        node.setParent(this);
        node.setUi(this.ui);
        this.children.add(node);
        return this;
    }

    public double getAbsoluteX() {
        return (getParent() != null) ? (getParent().getAbsoluteX() + getX()) : getX();
    }

    public double getAbsoluteY() {
        return (getParent() != null) ? (getParent().getAbsoluteY() + getY()) : getY();
    }

    public void drawDebug() {
        if (this.area != null && !this.area.inArea(this)) {
            GuiUtils.drawBorder(getAbsoluteX(), getAbsoluteY(), getAbsoluteX() + this.width, getAbsoluteY() + this.height, new Color(53, 59, 72));
            return;
        }
        GuiUtils.drawBorder(getAbsoluteX(), getAbsoluteY(), getAbsoluteX() + this.width, getAbsoluteY() + this.height, !this.enabled ? new Color(53, 59, 72) : (isHovered() ? new Color(87, 88, 187) : new Color(237, 76, 103)));
    }

    public int width(int value) {
        return (int) (this.width / 100.0D * value);
    }

    public int height(int value) {
        return (int) (this.height / 100.0D * value);
    }

    public float width(float value) {
        return (float) (this.width / 100.0D * value);
    }

    public float height(float value) {
        return (float) (this.height / 100.0D * value);
    }

    public double width(double value) {
        return this.width / 100.0D * value;
    }

    public double height(double value) {
        return this.height / 100.0D * value;
    }

    public double smoothValue(double value, double target, double speed, double snapDiff, boolean snap) {
        int fps = 60;
        double diff = target - value;
        double absDiff = Math.abs(diff);
        double offset = speed / (((fps == 0) ? 1f : fps) / 60.0F) * absDiff / 3.0D;
        if (absDiff > snapDiff) {
            value += (diff > 0.0D) ? offset : -offset;
        } else if (snap) {
            value = target;
        }
        return value;
    }

    public abstract boolean onClick(int paramInt1, int paramInt2, int paramInt3);

    public abstract void onRelease(int paramInt1, int paramInt2, int paramInt3);

    public abstract void onKeyTyped(char paramChar, int paramInt);

    public abstract void onHover(int paramInt1, int paramInt2);

    public abstract void onHoverOut(int paramInt1, int paramInt2);

    public abstract void fixedUpdate();

    public String getTooltipsText() {
        return tooltipsText;
    }

    public void setTooltipsText(String tooltipsText) {
        this.tooltipsText = tooltipsText;
    }

    public class DownloadImage implements Runnable {
        private final AtomicReference<BufferedImage> instance;

        private final String url;

        public DownloadImage(AtomicReference<BufferedImage> instance, String url, TextureManager manager) {
            this.instance = instance;
            this.url = url;
        }

        public void run() {
            try {
                URL rul = new URL(this.url);
                HttpURLConnection connection = (HttpURLConnection) rul.openConnection();
                connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.88 Safari/537.36");
                this.instance.set(ImageIO.read(connection.getInputStream()));
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
