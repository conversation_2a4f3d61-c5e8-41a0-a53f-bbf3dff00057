package fr.minepiece.client.gui.component.button;

import fr.minepiece.client.gui.component.AbstractUI;
import fr.minepiece.client.gui.component.NodeUpdate;
import fr.minepiece.client.gui.component.button.scroll.GuiScrollButton;
import fr.minepiece.client.gui.component.button.scroll.ScrollableArea;
import fr.minepiece.client.gui.component.fontv3.IFont;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.FontRenderer;
import net.minecraft.client.gui.GuiScreen;
import net.minecraft.client.gui.ScaledResolution;
import net.minecraft.client.renderer.BufferBuilder;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.RenderHelper;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import org.lwjgl.input.Keyboard;
import org.lwjgl.input.Mouse;
import org.lwjgl.opengl.GL11;

import java.awt.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Queue;
import java.util.function.Consumer;
import java.util.stream.Stream;

public class GuiAPI extends AbstractUI {
    private ScrollableArea focusedScrollArea;

    private GuiScrollButton activeScroll;

    private List<ScrollableArea> areas;

    private Minecraft minecraft = Minecraft.getMinecraft();

    public GuiAPI() {
    }

    public GuiAPI(GuiScreen prev, String name) {
        super(prev, name);
    }

    public void initGui() {
        this.focusedScrollArea = null;
        this.activeScroll = null;
        this.areas = new ArrayList<>();
        super.initGui();
    }

    public void handleMouseInput() throws IOException {
        if (this.popup != null) {
            this.popup.handleMouseInput();
            return;
        }
        super.handleMouseInput();
        int k = Mouse.getEventDWheel();
        if (isFullScreen()) {
            int i = 2;
            double heightRatio = getHeight() / 1080.0D;
            this.pageScroll += (k < 0) ? (-50 / i) : ((k > 0) ? (50 / i) : 0);
            this.pageScroll = (int) Math.max(-(this.height * heightRatio - this.height - 1.0D), Math.min(0, this.pageScroll));
        }
        if (this.focusedScrollArea == null)
            return;
        if (!this.focusedScrollArea.isCanScroll())
            return;
        double scroll = this.focusedScrollArea.getScroll();
        int scale = new ScaledResolution(this.mc).getScaleFactor();
        double mult = Keyboard.isKeyDown(29) ? 3.0D : 1.0D;
        scroll += (k < 0) ? (-(this.focusedScrollArea.getScrollSpeed() * mult) / scale) : ((k > 0) ? (this.focusedScrollArea.getScrollSpeed() * mult / scale) : 0.0D);
        scroll = Math.min(0.0D, scroll);
        scroll = Math.max(scroll, -this.focusedScrollArea.getMaxScroll());
        this.focusedScrollArea.setScroll(scroll);
    /*    if (this.focusedScrollArea instanceof DynamicScrollableArea) {
            DynamicScrollableArea dynamicScrollableArea = (DynamicScrollableArea)this.focusedScrollArea;
            int size = ((List)getNodes().stream().filter(node -> (node.getArea() != null)).filter(node -> node.getArea().equals(this.focusedScrollArea)).filter(Component::isSolid).collect(Collectors.toList())).size() - 1;
            int current = Math.abs((int)(size * this.focusedScrollArea.getScroll() / this.focusedScrollArea.getMaxScroll()));
            if (current >= dynamicScrollableArea.getUpdateFrequency() + dynamicScrollableArea.getLastDynamicScrollIndex()) {
                dynamicScrollableArea.setLastDynamicScrollIndex(dynamicScrollableArea.getUpdateCount() - dynamicScrollableArea.getUpdateFrequency() + current);
                dynamicScrollableArea.update(size);
            }
        }*/
    }

    public void mouseClicked(int mouseX, int mouseY, int buttonID) throws IOException {
        super.mouseClicked(mouseX, mouseY, buttonID);
        if (this.popup != null)
            return;

        doNodeUpdates();

        this.activeScroll = getComponents().stream().filter(GuiScrollButton.class::isInstance).map(GuiScrollButton.class::cast).filter(GuiComponent::isHovered).findFirst().orElse(null);
        if (this.activeScroll != null && this.activeScroll.getArea().isCanScroll())
            this.activeScroll.getArea().setCursorScrolling(true);
    }

    private void doNodeUpdates() {
        Queue<GuiComponent> components = getComponents();
        for (GuiComponent component : components) {
            doNodeUpdate(component);
        }
    }

    private void doNodeUpdate(GuiComponent component) {
        if (component instanceof NodeUpdate<?>) {
            NodeUpdate<?> nodeUpdate = (NodeUpdate<?>) component;
            Consumer<NodeUpdate<?>> update = (Consumer<NodeUpdate<?>>) nodeUpdate.getUpdate();
            if (update != null) {
                update.accept(nodeUpdate);
            }
        }
        for (GuiComponent child : component.getChildren()) {
            doNodeUpdate(child);
        }
    }

    public void mouseReleased(int mouseX, int mouseY, int buttonID) {
        super.mouseReleased(mouseX, mouseY, buttonID);
        if (this.popup != null)
            return;
        if (this.activeScroll != null) {
            this.activeScroll.getArea().setCursorScrolling(false);
            this.activeScroll = null;
        }
    }

    protected void mouseClickMove(int mouseX, int mouseY, int buttonID, long ticks) {
        if (this.popup == null) {
            if (this.activeScroll != null && this.activeScroll.getArea().isCanScroll()) {
                GuiScrollButton node = this.activeScroll;
                double scrollPercent = (int) (mouseY - this.currentPageScroll) - node.getDefaultY();
                scrollPercent /= node.getArea().getScrollArea().getHeight();
                scrollPercent *= 100.0D;
                double scroll = -node.getArea().getMaxScroll() / 100.0D * scrollPercent;
                if (scroll > 0.1D)
                    scroll = 0.0D;
                if (scroll < -node.getArea().getMaxScroll())
                    scroll = -node.getArea().getMaxScroll();
                node.getArea().setScroll(scroll);
            }
            super.mouseClickMove(mouseX, (int) (mouseY - this.currentPageScroll), buttonID, ticks);
        } else {
            this.popup.mouseClickMove(mouseX, (int) (mouseY - this.currentPageScroll), buttonID, ticks);
        }
    }

    @Deprecated
    @Override
    public void drawScreen(final int scaledMouseX, final int scaledMouseY, final float ticks) {
        final int mouseX = Mouse.getX();
        final int mouseY = this.mc.displayHeight - Mouse.getY();
        if (this.isFullScreen()) {
            final int fps = Integer.parseInt(this.mc.debug.split(" fps")[0]);
            final double diff = this.pageScroll - this.currentPageScroll;
            final double absDiff = Math.abs(diff);
            final double offset = 0.2 / (((fps == 0) ? 1 : fps) / 60.0f) * absDiff / 3.0;
            if (absDiff > 0.2) {
                this.currentPageScroll += ((diff > 0.0) ? offset : (-offset));
            }
            else {
                this.currentPageScroll = this.pageScroll;
            }
        }
        GL11.glMatrixMode(5889);
        GL11.glLoadIdentity();
        GL11.glOrtho(0.0, (double)Minecraft.getMinecraft().displayWidth, (double)Minecraft.getMinecraft().displayHeight, 0.0, 1000.0, 3000.0);
        GL11.glMatrixMode(5888);
        GL11.glPushMatrix();
        GuiUtils.hovers.clear();
        GL11.glPushMatrix();
        if (this.isFullScreen()) {
            GL11.glTranslated(0.0, this.currentPageScroll, 0.0);
        }
        getComponents().stream().filter(node -> (node.getZindex() < 0)).sorted(Comparator.comparingInt(GuiComponent::getZindex)).forEach(node -> drawNode(node, this.mc, mouseX, (int)(mouseY - this.currentPageScroll)));
        this.drawBackground(mouseX, mouseY, ticks);
        if (this.isFullScreen()) {
            GL11.glTranslated(0.0, -this.currentPageScroll, 0.0);
        }
        GL11.glColor4f(1.0f, 1.0f, 1.0f, 1.0f);
        GL11.glPopMatrix();
        if (!this.isLoaded()) {
            this.focusedScrollArea = null;
            this.activeScroll = null;
            this.areas = new ArrayList<ScrollableArea>();
        }
        for (ScrollableArea area : this.areas) {
            double scroll = area.getScroll();
            double maxScroll = area.getMaxScroll();
            if (scroll > 0.1D) {
                double diff = Math.abs(0.0D - Math.abs(scroll));
                scroll -= (area.getScrollSpeed() / 50) * Math.max(10.0D, diff);
            }
            if (scroll < -maxScroll) {
                double diff = Math.abs(Math.abs(maxScroll) - Math.abs(scroll));
                scroll += (area.getScrollSpeed() / 50) * Math.max(10.0D, diff);
            }
            Stream<GuiComponent> hide = getComponents().parallelStream().filter(node -> (node.getArea() != null)).filter(GuiComponent::isSolid).filter(node -> node.getArea().equals(area)).filter(node -> !node.getArea().inCompleteArea(node.x, node.defaultY, node.width, node.height));
            long hiddenCount = getComponents().parallelStream().filter(node -> (node.getArea() != null)).filter(GuiComponent::isSolid).filter(node -> node.getArea().equals(area)).filter(node -> !node.getArea().inCompleteArea(node.x, node.defaultY, node.width, node.height)).count();
            long presentCount = getComponents().parallelStream().filter(node -> (node.getArea() != null)).filter(GuiComponent::isSolid).filter(node -> node.getArea().equals(area)).filter(node -> node.getArea().inCompleteArea(node.x, node.defaultY, node.width, node.height)).count();
            if (hiddenCount <= 0L) {
                area.setCanScroll(false);
                scroll = 0.0D;
            } else {
                GuiComponent lastHidden = hide.skip(hiddenCount - 1L).findFirst().orElse(null);
                assert lastHidden != null;
                maxScroll = lastHidden.getDefaultY() - lastHidden.getArea().getMinY() + lastHidden.height - presentCount * lastHidden.height;
                area.setCanScroll(true);
            }
            area.setScroll(scroll);
            area.setMaxScroll(maxScroll);
        }
        /*
        if (this.focusedScrollArea != null) {
            double scroll = this.focusedScrollArea.getScroll();
            double maxScroll = this.focusedScrollArea.getMaxScroll();
            if (scroll > 0.1D) {
                double diff = Math.abs(0.0D - Math.abs(scroll));
                scroll -= (this.focusedScrollArea.getScrollSpeed() / 50) * Math.max(10.0D, diff);
            }
            if (scroll < -maxScroll) {
                double diff = Math.abs(Math.abs(maxScroll) - Math.abs(scroll));
                scroll += (this.focusedScrollArea.getScrollSpeed() / 50) * Math.max(10.0D, diff);
            }
            Stream<GuiComponent> hide = getComponents().parallelStream().filter(node -> (node.getArea() != null)).filter(GuiComponent::isSolid).filter(node -> node.getArea().equals(this.focusedScrollArea)).filter(node -> !node.getArea().inCompleteArea(node.x, node.defaultY, node.width, node.height));
            long hiddenCount = getComponents().parallelStream().filter(node -> (node.getArea() != null)).filter(GuiComponent::isSolid).filter(node -> node.getArea().equals(this.focusedScrollArea)).filter(node -> !node.getArea().inCompleteArea(node.x, node.defaultY, node.width, node.height)).count();
            long presentCount = getComponents().parallelStream().filter(node -> (node.getArea() != null)).filter(GuiComponent::isSolid).filter(node -> node.getArea().equals(this.focusedScrollArea)).filter(node -> node.getArea().inCompleteArea(node.x, node.defaultY, node.width, node.height)).count();
            if (hiddenCount <= 0L) {
                this.focusedScrollArea.setCanScroll(false);
                scroll = 0.0D;

            } else {
                GuiComponent lastHidden = hide.skip(hiddenCount - 1L).findFirst().orElse(null);
                assert lastHidden != null;
                maxScroll = lastHidden.getDefaultY() - lastHidden.getArea().getMinY() + lastHidden.height - presentCount * lastHidden.height;
                this.focusedScrollArea.setCanScroll(true);
            }
            this.focusedScrollArea.setScroll(scroll);
            this.focusedScrollArea.setMaxScroll(maxScroll);
        }
        */
        boolean foundScrollArea = false;
        for (final GuiComponent node2 : this.getComponents()) {
            if (node2.getArea() != null) {
                final ScrollableArea area = node2.getArea();
                node2.setScroll(area.getScroll());
                if (!area.inArea(mouseX, mouseY - this.currentPageScroll)) {
                    continue;
                }
                this.focusedScrollArea = area;
                foundScrollArea = true;
            }
        }
        getComponents().stream().filter(GuiScrollButton.class::isInstance).map(GuiScrollButton.class::cast).forEach(wButton -> {
            double maxY = wButton.getDefaultY() + wButton.getArea().getScrollArea().getHeight() - wButton.height;
            double p = wButton.getArea().getScroll() / wButton.getArea().getMaxScroll() * 100.0D;
            double y = wButton.getDefaultY() - (wButton.getArea().getScrollArea().getHeight() - wButton.height) / 100.0D * p;
            y = Math.min(y, maxY);
            y = Math.max(y, wButton.getDefaultY());
            wButton.setY(y);
        });
        if (!foundScrollArea && this.areas.size() > 1) {
            this.focusedScrollArea = null;
        }
        if (this.areas.size() == 1) {
            this.focusedScrollArea = this.areas.get(0);
        }
        GL11.glPushMatrix();
        if (this.isFullScreen()) {
            GL11.glTranslated(0.0, this.currentPageScroll, 0.0);
        }
        getComponents().stream().filter(node -> (node.getZindex() >= 0 && node.getZindex() < 100)).sorted(Comparator.comparingInt(GuiComponent::getZindex)).forEach(node -> drawNode(node, this.mc, mouseX, (int)(mouseY - this.currentPageScroll)));
        postDraw(mouseX, mouseY, ticks);
        getComponents().stream().filter(node -> (node.getZindex() >= 100)).sorted(Comparator.comparingInt(GuiComponent::getZindex)).forEach(node -> drawNode(node, this.mc, mouseX, (int)(mouseY - this.currentPageScroll)));
        getComponents().stream()
                .filter(component -> component.hovered)
                .filter(component -> component.getTooltipsText() != null)
                .forEach(component -> drawHoveringText(Arrays.asList(component.getTooltipsText().split("\n")), mouseX, mouseY, width(1) / 6));
        if (this.isFullScreen()) {
            GL11.glTranslated(0.0, -this.currentPageScroll, 0.0);
        }
        GL11.glColor4f(1.0f, 1.0f, 1.0f, 1.0f);
        GL11.glPopMatrix();
        GL11.glPopMatrix();
        GL11.glColor4f(1.0f, 1.0f, 1.0f, 1.0f);
        super.drawScreen(mouseX, mouseY, ticks);
    }

    private void drawNode(GuiComponent node, Minecraft mc, int mouseX, int mouseY) {
        GL11.glPushMatrix();
        GL11.glColor4f(1.0F, 1.0F, 1.0F, 1.0F);
        if (node.getParent() != null)
            GL11.glTranslated(node.getParent().getX(), node.getParent().getY(), 0.0D);
        if (node.getArea() != null && !(node instanceof GuiScrollButton)) {
            ScrollableArea area = node.getArea();
            boolean drawing = !(node.getAbsoluteX() + node.getWidth() < area.getMinX()) && !(node.getAbsoluteY() + node.getHeight() < area.getMinY()) && !(node.getAbsoluteX() > area.getMaxX()) && !(node.getAbsoluteY() > area.getMaxY());
            GL11.glEnable(3089);
            GuiUtils.scissor(mc, area.getMinX(), area.getMinY(), area.getMaxX() - area.getMinX(), area.getMaxY() - area.getMinY());
            node.update(mc, mouseX, mouseY);
            if (node.isVisible() && drawing) {
                node.getChildren().stream().filter(child -> (child.getZindex() < 0)).sorted(Comparator.comparingInt(GuiComponent::getZindex)).forEach(child -> drawNode(child, mc, mouseX, mouseY));
                node.draw(mc, mouseX, mouseY);
                node.getChildren().stream().filter(child -> (child.getZindex() >= 0)).sorted(Comparator.comparingInt(GuiComponent::getZindex)).forEach(child -> drawNode(child, mc, mouseX, mouseY));
            }
            GL11.glDisable(3089);
        } else {
            node.update(mc, mouseX, mouseY);
            if (node.isVisible()) {
                node.getChildren().stream().filter(child -> (child.getZindex() < 0)).sorted(Comparator.comparingInt(GuiComponent::getZindex)).forEach(child -> drawNode(child, mc, mouseX, mouseY));
                node.draw(mc, mouseX, mouseY);
                node.getChildren().stream().filter(child -> (child.getZindex() >= 0)).sorted(Comparator.comparingInt(GuiComponent::getZindex)).forEach(child -> drawNode(child, mc, mouseX, mouseY));
            }
        }
        GL11.glColor4f(1.0F, 1.0F, 1.0F, 1.0F);
        GL11.glPopMatrix();
    }

    public void addScrollableArea(ScrollableArea area) {
        this.areas.add(area);
        addComponent(area.createScrollBar());
    }

    public void addScrollableText(String text, ScrollableArea area, double x, double y, double maxWidth, Color color, IFont font) {
        addScrollableArea(area);
        int fontHeight = GuiUtils.getFontHeight(this.mc, font, 80);
        List<String> texts = GuiUtils.getSplittedString(this.mc, text, font, 80, maxWidth);
        for (int i = 0; i < texts.size(); i++) {
            int width = GuiUtils.getStringWidth(this.mc, texts.get(i), font, 60);
            addComponent((new GuiTextLine(x, y + ((fontHeight + 1) * i), width, fontHeight, texts.get(i), color, font, 60)));
        }
    }

    public void drawDebug() {
        this.areas.forEach(area -> GuiUtils.drawBorder(area.getMinX(), area.getMinY(), area.getMaxX(), area.getMaxY(), new Color(47, 54, 64)));
        this.areas.forEach(area -> GuiUtils.drawBorder(area.getScrollArea().getX(), area.getScrollArea().getY(), area.getScrollArea().getX() + area.getScrollArea().getButtonWidth(), area.getScrollArea().getY() + area.getScrollArea().getHeight(), new Color(39, 60, 117)));
        super.drawDebug();
    }

    public boolean isFullScreen() {
        return false;
    }

    public double getHeight() {
        return 1080.0D;
    }

    protected void drawHoveringText(List<String> textLines, int mouseX, int mouseY, float scale) {
        if (!textLines.isEmpty()) {
            int screenWidth = mc.displayWidth;
            int screenHeight = mc.displayHeight;
            FontRenderer font = this.mc.fontRenderer;

            GlStateManager.disableRescaleNormal();
            RenderHelper.disableStandardItemLighting();
            GlStateManager.disableLighting();
            GlStateManager.disableDepth();

            int tooltipTextWidth = 0;

            for (String textLine : textLines) {
                int textLineWidth = font.getStringWidth(textLine);

                if (textLineWidth > tooltipTextWidth) {
                    tooltipTextWidth = textLineWidth;
                }
            }

            boolean needsWrap = false;

            int titleLinesCount = 1;
            int tooltipX = mouseX + 12;
            if (tooltipX + tooltipTextWidth + 4 > screenWidth) {
                tooltipX = mouseX - 16 - tooltipTextWidth;
                if (tooltipX < 4) {
                    if (mouseX > screenWidth / 2) {
                        tooltipTextWidth = mouseX - 12 - 8;
                    } else {
                        tooltipTextWidth = screenWidth - 16 - mouseX;
                    }
                    needsWrap = true;
                }
            }

            if (needsWrap) {
                int wrappedTooltipWidth = 0;
                List<String> wrappedTextLines = new ArrayList<>();
                for (int i = 0; i < textLines.size(); i++) {
                    String textLine = textLines.get(i);
                    List<String> wrappedLine = font.listFormattedStringToWidth(textLine, tooltipTextWidth);
                    if (i == 0) {
                        titleLinesCount = wrappedLine.size();
                    }

                    for (String line : wrappedLine) {
                        int lineWidth = font.getStringWidth(line);
                        if (lineWidth > wrappedTooltipWidth) {
                            wrappedTooltipWidth = lineWidth;
                        }
                        wrappedTextLines.add(line);
                    }
                }
                tooltipTextWidth = wrappedTooltipWidth;
                textLines = wrappedTextLines;

                if (mouseX > screenWidth / 2) {
                    tooltipX = mouseX - 16 - tooltipTextWidth;
                }
                else {
                    tooltipX = mouseX + 12;
                }
            }
            tooltipTextWidth = (int) (tooltipTextWidth * scale);

            int tooltipY = mouseY - 12;
            int tooltipHeight = 8;

            if (textLines.size() > 1) {
                tooltipHeight += (textLines.size() - 1) * 10;
                if (textLines.size() > titleLinesCount) {
                    tooltipHeight += 2; // gap between title lines and next lines
                }
            }
            tooltipHeight = (int) (tooltipHeight * scale);

            if (tooltipY < 4) {
                tooltipY = 4;
            } else if (tooltipY + tooltipHeight + 4 > screenHeight) {
                tooltipY = screenHeight - tooltipHeight - 4;
            }

            final int zLevel = 300;
            int backgroundColor = 0xF0100010;
            int borderColorStart = 0x505000FF;
            int borderColorEnd = (borderColorStart & 0xFEFEFE) >> 1 | borderColorStart & 0xFF000000;
            drawGradientRect(zLevel, tooltipX - 3, tooltipY - 4, tooltipX + tooltipTextWidth + 3, tooltipY - 3, backgroundColor, backgroundColor);
            drawGradientRect(zLevel, tooltipX - 3, tooltipY + tooltipHeight + 3, tooltipX + tooltipTextWidth + 3, tooltipY + tooltipHeight + 4, backgroundColor, backgroundColor);
            drawGradientRect(zLevel, tooltipX - 3, tooltipY - 3, tooltipX + tooltipTextWidth + 3, tooltipY + tooltipHeight + 3, backgroundColor, backgroundColor);
            drawGradientRect(zLevel, tooltipX - 4, tooltipY - 3, tooltipX - 3, tooltipY + tooltipHeight + 3, backgroundColor, backgroundColor);
            drawGradientRect(zLevel, tooltipX + tooltipTextWidth + 3, tooltipY - 3, tooltipX + tooltipTextWidth + 4, tooltipY + tooltipHeight + 3, backgroundColor, backgroundColor);
            drawGradientRect(zLevel, tooltipX - 3, tooltipY - 3 + 1, tooltipX - 3 + 1, tooltipY + tooltipHeight + 3 - 1, borderColorStart, borderColorEnd);
            drawGradientRect(zLevel, tooltipX + tooltipTextWidth + 2, tooltipY - 3 + 1, tooltipX + tooltipTextWidth + 3, tooltipY + tooltipHeight + 3 - 1, borderColorStart, borderColorEnd);
            drawGradientRect(zLevel, tooltipX - 3, tooltipY - 3, tooltipX + tooltipTextWidth + 3, tooltipY - 3 + 1, borderColorStart, borderColorStart);
            drawGradientRect(zLevel, tooltipX - 3, tooltipY + tooltipHeight + 2, tooltipX + tooltipTextWidth + 3, tooltipY + tooltipHeight + 3, borderColorEnd, borderColorEnd);

            int tooltipTop = tooltipY;

            for (int lineNumber = 0; lineNumber < textLines.size(); ++lineNumber) {
                String line = textLines.get(lineNumber);
                GuiUtils.renderTextScaled(line, tooltipX, tooltipY, -1, scale);

                if (lineNumber + 1 == titleLinesCount) {
                    tooltipY += 2;
                }

                tooltipY += 10;
            }

            GlStateManager.enableLighting();
            GlStateManager.enableDepth();
            RenderHelper.enableStandardItemLighting();
            GlStateManager.enableRescaleNormal();
        }
    }

    public static void drawGradientRect(int zLevel, int left, int top, int right, int bottom, int startColor, int endColor) {
        float startAlpha = (float)(startColor >> 24 & 255) / 255.0F;
        float startRed   = (float)(startColor >> 16 & 255) / 255.0F;
        float startGreen = (float)(startColor >>  8 & 255) / 255.0F;
        float startBlue  = (float)(startColor       & 255) / 255.0F;
        float endAlpha   = (float)(endColor   >> 24 & 255) / 255.0F;
        float endRed     = (float)(endColor   >> 16 & 255) / 255.0F;
        float endGreen   = (float)(endColor   >>  8 & 255) / 255.0F;
        float endBlue    = (float)(endColor         & 255) / 255.0F;

        GlStateManager.disableTexture2D();
        GlStateManager.enableBlend();
        GlStateManager.disableAlpha();
        GlStateManager.tryBlendFuncSeparate(GlStateManager.SourceFactor.SRC_ALPHA, GlStateManager.DestFactor.ONE_MINUS_SRC_ALPHA, GlStateManager.SourceFactor.ONE, GlStateManager.DestFactor.ZERO);
        GlStateManager.shadeModel(GL11.GL_SMOOTH);

        Tessellator tessellator = Tessellator.getInstance();
        BufferBuilder buffer = tessellator.getBuffer();
        buffer.begin(GL11.GL_QUADS, DefaultVertexFormats.POSITION_COLOR);
        buffer.pos(right,    top, zLevel).color(startRed, startGreen, startBlue, startAlpha).endVertex();
        buffer.pos( left,    top, zLevel).color(startRed, startGreen, startBlue, startAlpha).endVertex();
        buffer.pos( left, bottom, zLevel).color(  endRed,   endGreen,   endBlue,   endAlpha).endVertex();
        buffer.pos(right, bottom, zLevel).color(  endRed,   endGreen,   endBlue,   endAlpha).endVertex();
        tessellator.draw();

        GlStateManager.shadeModel(GL11.GL_FLAT);
        GlStateManager.disableBlend();
        GlStateManager.enableAlpha();
        GlStateManager.enableTexture2D();
    }
}
