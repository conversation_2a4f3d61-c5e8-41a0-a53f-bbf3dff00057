package fr.minepiece.client.gui.hud.components;

import fr.minepiece.client.gui.hud.IHudComponent;
import fr.minepiece.common.items.BarometerItem;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.FontRenderer;
import net.minecraft.client.gui.ScaledResolution;
import net.minecraft.entity.player.EntityPlayer;

public class BarometerHud implements IHudComponent {
    @Override
    public void render(Minecraft mc, ScaledResolution res, EntityPlayer player) {
        if (System.currentTimeMillis() < BarometerItem.displayEndTime) {
            FontRenderer fr = mc.fontRenderer;

            String pressureText = String.format("%.1f atm", BarometerItem.displayPressure);
            String label = "Pression : ";

            int x = (res.getScaledWidth() - fr.getStringWidth(label + pressureText)) / 2;
            int y = res.getScaledHeight() / 4;

            fr.drawStringWithShadow(label, x, y, 0xFFFFFF);
            fr.drawStringWithShadow(pressureText, x + fr.getStringWidth(label), y, 0xFFB6C1);
        }
    }
}
