package fr.minepiece.client.gui.practical;

import fr.minepiece.Reference;
import fr.minepiece.client.gui.component.PlayerRenderer;
import fr.minepiece.client.gui.component.TextAlign;
import fr.minepiece.client.gui.component.button.GuiAPI;
import fr.minepiece.client.gui.component.button.GuiTexturedButton;
import fr.minepiece.client.gui.component.button.GuiUtils;
import fr.minepiece.client.gui.component.textarea.GuiSimpleTextLabel;
import fr.minepiece.client.gui.component.textarea.TextInfo;
import fr.minepiece.common.api.utils.PlayerUtils;
import fr.minepiece.common.capability.mpdata.IMinePieceData;
import fr.minepiece.common.capability.mpdata.MinePieceDataStorage;
import fr.minepiece.common.events.PlayerXPEvents;
import net.minecraft.client.Minecraft;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.ResourceLocation;

import java.awt.*;

public class IdentityGui extends GuiAPI {

    private static final ResourceLocation background = new ResourceLocation(Reference.MOD_ID,"textures/gui/new/base/base_interface_inv.png");
    private static final ResourceLocation visualizer = new ResourceLocation(Reference.MOD_ID, "textures/gui/new/visualizers/modele_carte_identite.png");

    private static final ResourceLocation paperUltraLong = new ResourceLocation(Reference.MOD_ID, "textures/gui/new/composants/zone_de_texte_3.png");
    private static final ResourceLocation paperShort = new ResourceLocation(Reference.MOD_ID, "textures/gui/new/composants/zone_de_texte_5.png");
    private static final ResourceLocation mediumPaper = new ResourceLocation(Reference.MOD_ID, "textures/gui/new/composants/zone_de_texte_4.png");

    private static final ResourceLocation emptyBar = new ResourceLocation(Reference.MOD_ID, "textures/gui/new/composants/carte_identite_bar.png");
    private static final ResourceLocation jobBar = new ResourceLocation(Reference.MOD_ID, "textures/gui/new/composants/carte_identite_bar_metier.png");
    private static final ResourceLocation xpBar = new ResourceLocation(Reference.MOD_ID, "textures/gui/new/composants/carte_identite_bar_xp.png");

    private final String username;
    private static final Minecraft minecraft = Minecraft.getMinecraft();

    public IdentityGui(String username) {
        super(null, "identity_card");
        this.username = username;
    }


    public void initGui() {
        super.initGui();

        EntityPlayer player = PlayerUtils.getPlayerByUsername(username);
        assert player != null;
        IMinePieceData stats = player.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);
        String prenom = stats.getRPName();
        String nom = stats.getRPLastName();
        int age = stats.getRPAge();
        String race = stats.getRace().getName();
        int xp = stats.getXP();
        int level = stats.getLevel();
        long requiredXp = PlayerUtils.calculateRequiredXP(1 + level);

        PlayerRenderer playerRenderer = new PlayerRenderer(width(31), height(19), width(12.15F), height(30F), Minecraft.getMinecraft());
        playerRenderer.setScale(160);
        addComponent(playerRenderer);

        GuiTexturedButton prenomDisplay = new GuiTexturedButton(width(46.3F), height(19F), width(22.7F), height(7.5F), new TextInfo(prenom, 4334643, 4D, false));
        prenomDisplay.setTexture(paperUltraLong);
        addComponent(prenomDisplay);

        GuiTexturedButton nomDisplay = new GuiTexturedButton(width(46.3F), height(27.5F), width(22.7F), height(7.5F), new TextInfo(nom, 4334642, 4D, false));
        nomDisplay.setTexture(paperUltraLong);
        addComponent(nomDisplay);

        GuiTexturedButton ageDisplay = new GuiTexturedButton(width(46.3F), height(36.6F), width(5.8F), height(7.5F), new TextInfo(String.valueOf(age), 4334641, 4D, false));
        ageDisplay.setTexture(paperShort);
        ageDisplay.setTooltipsText("Age");
        addComponent(ageDisplay);

        GuiTexturedButton raceDisplay = new GuiTexturedButton(width(57.5F), height(36.6F), width(11.5F), height(7.5F), new TextInfo(race, 4334640, 2.7D, false));
        raceDisplay.setTexture(mediumPaper);
        addComponent(raceDisplay);

        // ##########
        // XP PROGRESSION - PLAYER LEVEL
        // ##########

        GuiTexturedButton playerLevelBackground = new GuiTexturedButton(width(33.05F), height(53.3F), width(33.85), height(2.95F));
        playerLevelBackground.setTexture(emptyBar);
        playerLevelBackground.setTooltipsText("§aNiveau "+level);
        addComponent(playerLevelBackground);

        GuiTexturedButton playerLevelProgression = new GuiTexturedButton(width(33.33F), height(53.8F), width(((float) xp / requiredXp)*33.37), height(1.95));
        playerLevelProgression.setTexture(xpBar);
        addComponent(playerLevelProgression);

        GuiSimpleTextLabel playerLevelDisplay = new GuiSimpleTextLabel(width(33.05F), height(53.7F), width(33.85), height(2.95F), new TextInfo(xp+" / "+requiredXp, Color.white.getRGB(), 2.7D, false));
        addComponent(playerLevelDisplay);

        // ##########
        // Job
        // ##########

        int jobID = stats.getJobID() ;
        String jobName = "" ;
        int jobXP = stats.getJobXP() ;
        int jobLevel = stats.getJobLevel() ;
        long jobRequiredXp = PlayerUtils.calculateRequiredXP(1 + jobLevel) ;

        /**
         * Il faut retrouver le métier selon l'ID de celui-ci.
         */
        if (jobID == 0) {
            jobName = "Médecin" ;
        }
        if (jobID == 1) {
            jobName = "Cuisinier" ;
        }
        if (jobID == 2) {
            jobName = "Forgeron" ;
        }

        GuiTexturedButton jobDisplay = new GuiTexturedButton(width(33.05F), height(57.5F), width(11.5F), height(7.5F), new TextInfo(jobName, 4334640, 2.7D, false));
        jobDisplay.setTexture(mediumPaper);
        addComponent(jobDisplay);

        GuiTexturedButton jobLevelDisplay = new GuiTexturedButton(width(46.3F), height(57.5F), width(5.8F), height(7.5F), new TextInfo(String.valueOf(jobLevel), 4334641, 4D, false));
        jobLevelDisplay.setTexture(paperShort);
        jobLevelDisplay.setTooltipsText("Job level");
        addComponent(jobLevelDisplay);

        GuiTexturedButton playerJobLevelBackground = new GuiTexturedButton(width(33.05F), height(68.3F), width(33.85), height(2.95F)) ;
        playerJobLevelBackground.setTexture(emptyBar);
        playerJobLevelBackground.setTooltipsText("§aNiveau " + jobLevel);
        addComponent(playerJobLevelBackground);

        GuiTexturedButton playerJobLevelProgression = new GuiTexturedButton(width(33.33F), height(68.8F), width(((float) jobXP / jobRequiredXp)*33.37), height(1.95)) ;
        playerJobLevelProgression.setTexture(xpBar);
        addComponent(playerJobLevelProgression);

        GuiSimpleTextLabel playerJobLevelDisplay = new GuiSimpleTextLabel(width(33.05F), height(68.7F), width(33.85), height(2.95F), new TextInfo(jobXP + " / "+ jobRequiredXp, Color.white.getRGB(), 2.7D, false));
        addComponent(playerJobLevelDisplay);

    }

    public void drawBackground(int mouseX, int mouseY, float ticks) {
        this.drawDefaultBackground();
        GuiUtils.drawImageTransparent(width(25F),height(9.3F), background, width(51.5F), height(81.7F));
        //GuiUtils.drawImageTransparent(width(0),height(0), visualizer, width(100), height(100));
    }

}
