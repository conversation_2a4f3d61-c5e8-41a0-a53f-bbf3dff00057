package fr.minepiece.client.gui.practical.jobs.cook;

import fr.minepiece.Reference;
import fr.minepiece.common.blocks.jobs.crafting.cook.plancheadecouper.container.ContainerPlancheADecouper;
import fr.minepiece.common.blocks.jobs.crafting.cook.plancheadecouper.tileentity.TileEntityPlancheADecouper;
import net.minecraft.client.gui.inventory.GuiContainer;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.resources.I18n;
import net.minecraft.entity.player.InventoryPlayer;
import net.minecraft.util.ResourceLocation;

public class GuiPlancheADecouper extends GuiContainer {
    private static final ResourceLocation TEXTURE = new ResourceLocation(Reference.MOD_ID, "textures/gui/new/visualizers/planche_a_decouper.png");

    private final InventoryPlayer playerInventory;
    private final TileEntityPlancheADecouper tileEntity;

    public GuiPlancheADecouper(InventoryPlayer playerInventory, TileEntityPlancheADecouper tileEntity) {
        super(new ContainerPlancheADecouper(playerInventory, tileEntity));
        this.playerInventory = playerInventory;
        this.tileEntity = tileEntity;
        this.xSize = 176;  // Width with borders
        this.ySize = 166; // Exact height including player inventory
    }

    @Override
    protected void drawGuiContainerForegroundLayer(int mouseX, int mouseY) {
        String tileName = I18n.format("PlancheADecouper") ;
        this.fontRenderer.drawString(tileName, (this.xSize/2 - this.fontRenderer.getStringWidth(tileName) / 2) + 3, 8, 4210752) ;
        this.fontRenderer.drawString(this.playerInventory.getDisplayName().getUnformattedText(), 13, this.ySize - 96 + 2, 4210752);
    }

    @Override
    protected void drawGuiContainerBackgroundLayer(float partialTicks, int mouseX, int mouseY) {
        GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);
        this.mc.getTextureManager().bindTexture(TEXTURE);
        this.drawModalRectWithCustomSizedTexture(this.guiLeft, this.guiTop, 0, 0, this.xSize, this.ySize, this.xSize, this.ySize) ;
    }

    @Override
    public void drawScreen(int mouseX, int mouseY, float partialTicks) {
        this.drawDefaultBackground();
        super.drawScreen(mouseX, mouseY, partialTicks);
        this.renderHoveredToolTip(mouseX, mouseY);
    }
}