package fr.minepiece.client.model.modifications.models.fishman;

import fr.minepiece.client.model.modifications.models.AbstractPlayerModificationModel;
import net.minecraft.client.model.ModelBase;
import net.minecraft.client.model.ModelBox;
import net.minecraft.client.model.ModelRenderer;
import net.minecraft.entity.Entity;

public class SeaBreamModel extends AbstractPlayerModificationModel {
    private final ModelRenderer bb_main;
    private final ModelRenderer cube_r1;

    public SeaBreamModel() {
        textureWidth = 32;
        textureHeight = 32;

        bb_main = new ModelRenderer(this);
        bb_main.setRotationPoint(0.0F, 24.0F, 0.0F);
        bb_main.cubeList.add(new ModelBox(bb_main, 0, 12, -0.5F, -19.0F, 1.0F, 1, 10, 2, 0.0F, false));

        cube_r1 = new ModelRenderer(this);
        cube_r1.setRotationPoint(0.0F, -14.5144F, 2.9105F);
        bb_main.addChild(cube_r1);
        setRotationAngle(cube_r1, -1.3963F, 0.0F, 0.0F);
        cube_r1.cubeList.add(new ModelBox(cube_r1, 0, 0, 0.0F, -2.25F, -3.5F, 0, 3, 9, 0.0F, false));

        bone.addChild(bb_main);
    }

    @Override
    public void render(Entity entity, float f, float f1, float f2, float f3, float f4, float f5) {
        bone.render(f5);
    }

    public void render(float scale, boolean sneaking) {
        if (sneaking) {
            bb_main.rotationPointX = 0.0F;
            bb_main.rotateAngleX = 0.5F;
            bb_main.offsetZ = 0.55F;
            bb_main.offsetY = -0.1F;

        } else {
            bb_main.rotationPointX = 0.0F;
            bb_main.rotateAngleX = 0F;
            bb_main.offsetZ = 0F;
            bb_main.offsetY = -0.1F;
        }
        bone.render(scale);
    }

    public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
        modelRenderer.rotateAngleX = x;
        modelRenderer.rotateAngleY = y;
        modelRenderer.rotateAngleZ = z;
    }
}
