package fr.minepiece.client.model.layers;

import fr.minepiece.client.model.CustomArmorModelBiped;
import fr.minepiece.client.model.modifications.AbstractPlayerModification;
import fr.minepiece.client.model.modifications.PlayerModifications;
import fr.minepiece.client.render.playerapi.poses.Poses;
import fr.minepiece.common.capability.mpdata.IMinePieceData;
import fr.minepiece.common.capability.mpdata.MinePieceDataStorage;
import fr.minepiece.common.capability.playermodifications.IPlayerModifications;
import fr.minepiece.common.capability.playermodifications.PlayerModificationsStorage;
import net.minecraft.client.model.ModelBiped;
import net.minecraft.client.model.ModelPlayer;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.entity.RenderLivingBase;
import net.minecraft.client.renderer.entity.layers.LayerArmorBase;
import net.minecraft.client.renderer.entity.layers.LayerBipedArmor;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.inventory.EntityEquipmentSlot;
import net.minecraft.item.ItemArmor;
import net.minecraft.item.ItemStack;
import net.minecraftforge.fml.common.ObfuscationReflectionHelper;
import net.minecraftforge.fml.relauncher.ReflectionHelper;

import java.lang.reflect.Field;

public class LayerCustomBipedArmor extends LayerBipedArmor {

    private final RenderLivingBase<?> renderer;
    public LayerCustomBipedArmor(RenderLivingBase<?> rendererIn) {
        super(rendererIn);
        this.renderer = rendererIn;
    }

    @Override
    protected void initArmor() {
        this.modelLeggings = new CustomArmorModelBiped(0.5F);
        this.modelArmor = new CustomArmorModelBiped(1.0F);
    }

    @Override
    public void doRenderLayer(EntityLivingBase entitylivingbaseIn, float limbSwing, float limbSwingAmount, float partialTicks, float ageInTicks, float netHeadYaw, float headPitch, float scale) {
        this.renderArmorLayer(entitylivingbaseIn, limbSwing, limbSwingAmount, partialTicks, ageInTicks, netHeadYaw, headPitch, scale, EntityEquipmentSlot.CHEST);
        this.renderArmorLayer(entitylivingbaseIn, limbSwing, limbSwingAmount, partialTicks, ageInTicks, netHeadYaw, headPitch, scale, EntityEquipmentSlot.LEGS);
        this.renderArmorLayer(entitylivingbaseIn, limbSwing, limbSwingAmount, partialTicks, ageInTicks, netHeadYaw, headPitch, scale, EntityEquipmentSlot.FEET);
        this.renderArmorLayer(entitylivingbaseIn, limbSwing, limbSwingAmount, partialTicks, ageInTicks, netHeadYaw, headPitch, scale, EntityEquipmentSlot.HEAD);
    }

    private void renderArmorLayer(EntityLivingBase entityLivingBaseIn, float limbSwing, float limbSwingAmount, float partialTicks, float ageInTicks, float netHeadYaw, float headPitch, float scale, EntityEquipmentSlot slotIn) {
        IPlayerModifications modifications = entityLivingBaseIn.getCapability(PlayerModificationsStorage.PLAYER_MODIFICATIONS_CAPABILITY, null);
        assert modifications != null;

        resetModelVisibilities(modelLeggings);
        resetModelVisibilities(modelArmor);

        if (!modifications.getModifications().isEmpty()) {
            for (String modification : modifications.getModifications()) {
                AbstractPlayerModification playerModification = PlayerModifications.getModificationByName(modification);
                playerModification.setModelVisibilities(modelLeggings);
                playerModification.setModelVisibilities(modelArmor);
            }
        }

        ItemStack itemstack = entityLivingBaseIn.getItemStackFromSlot(slotIn);

        if (itemstack.getItem() instanceof ItemArmor) {
            ItemArmor itemarmor = (ItemArmor)itemstack.getItem();

            if (itemarmor.getEquipmentSlot() == slotIn) {
                CustomArmorModelBiped modelBiped = this.getModelFromSlot(slotIn);
                modelBiped = getArmorModelHook(entityLivingBaseIn, itemstack, slotIn, modelBiped);
                modelBiped.setModelAttributes(this.renderer.getMainModel());
                modelBiped.setLivingAnimations(entityLivingBaseIn, limbSwing, limbSwingAmount, partialTicks);
                this.setModelSlotVisible(modelBiped, slotIn);
                this.renderer.bindTexture(this.getArmorResource(entityLivingBaseIn, itemstack, slotIn, null));

                float[] colors = getLayerArmorBaseColors(this);
                float colorR = colors[0];
                float colorG = colors[1];
                float colorB = colors[2];
                float alpha = colors[3];
                if (itemarmor.hasOverlay(itemstack)) {
                        int i = itemarmor.getColor(itemstack);
                        float redConstant = (float)(i >> 16 & 255) / 255.0F;
                        float greenConstant = (float)(i >> 8 & 255) / 255.0F;
                        float blueConstant = (float)(i & 255) / 255.0F;
                        GlStateManager.color(colorR * redConstant, colorG * greenConstant, colorB * blueConstant, alpha);
                        modelBiped.render(entityLivingBaseIn, limbSwing, limbSwingAmount, ageInTicks, netHeadYaw, headPitch, scale);
                        this.renderer.bindTexture(this.getArmorResource(entityLivingBaseIn, itemstack, slotIn, "overlay"));
                }

                GlStateManager.color(colorR, colorG, colorB, alpha);

                modelBiped.render(entityLivingBaseIn, limbSwing, limbSwingAmount, ageInTicks, netHeadYaw, headPitch, scale);

                if (!getLayerArmorBaseSkipRenderGlint(this) && itemstack.hasEffect()) {
                    renderEnchantedGlint(this.renderer, entityLivingBaseIn, modelBiped, limbSwing, limbSwingAmount, partialTicks, ageInTicks, netHeadYaw, headPitch, scale);
                }

            }
        }
    }

    private void resetModelVisibilities(ModelBiped modelPlayer) {
        modelPlayer.bipedLeftLeg.isHidden = false;
        modelPlayer.bipedRightLeg.isHidden = false;
        modelPlayer.bipedLeftArm.isHidden = false;
        modelPlayer.bipedRightArm.isHidden = false;
        modelPlayer.bipedBody.isHidden = false;
        modelPlayer.bipedHead.isHidden = false;
        modelPlayer.bipedHeadwear.isHidden = false;
    }

    @Override
    public CustomArmorModelBiped getModelFromSlot(EntityEquipmentSlot slotIn) {
        return (CustomArmorModelBiped) (this.isLegSlot(slotIn) ? this.modelLeggings : this.modelArmor);
    }

    @Override
    protected CustomArmorModelBiped getArmorModelHook(net.minecraft.entity.EntityLivingBase entity, net.minecraft.item.ItemStack itemStack, EntityEquipmentSlot slot, ModelBiped model) {
        return (CustomArmorModelBiped) net.minecraftforge.client.ForgeHooksClient.getArmorModel(entity, itemStack, slot, model);
    }

    private boolean isLegSlot(EntityEquipmentSlot slotIn) {
        return slotIn == EntityEquipmentSlot.LEGS;
    }

    private float[] getLayerArmorBaseColors(LayerArmorBase<?> layerArmorBase) {
        try {
            Field colorRField = ReflectionHelper.findField(LayerArmorBase.class, "colorR", "field_177184_f");
            Field colorGField = ReflectionHelper.findField(LayerArmorBase.class, "colorG", "field_177185_g");
            Field colorBField = ReflectionHelper.findField(LayerArmorBase.class, "colorB", "field_177192_h");
            Field alphaField = ReflectionHelper.findField(LayerArmorBase.class, "alpha", "field_177187_e");
            colorRField.setAccessible(true);
            colorGField.setAccessible(true);
            colorBField.setAccessible(true);
            alphaField.setAccessible(true);

            float colorR = colorRField.getFloat(layerArmorBase);
            float colorG = colorGField.getFloat(layerArmorBase);
            float colorB = colorBField.getFloat(layerArmorBase);
            float alpha = alphaField.getFloat(layerArmorBase);

            return new float[]{colorR, colorG, colorB, alpha};
        } catch (IllegalAccessException e) {
            e.printStackTrace(); // Handle the exception appropriately
            return null;
        }
    }

    private boolean getLayerArmorBaseSkipRenderGlint(LayerArmorBase<?> layerArmorBase) {
        try {
            Field skipRenderGlintField = ObfuscationReflectionHelper.findField(LayerArmorBase.class, "field_177193_i");
            //LayerArmorBase.class.getDeclaredField("skipRenderGlint");
            skipRenderGlintField.setAccessible(true);

            return skipRenderGlintField.getBoolean(layerArmorBase);
        } catch (IllegalAccessException e) {
            e.printStackTrace(); // Handle the exception appropriately
            return false; // Default value if unable to retrieve
        }
    }
}
