package fr.minepiece.client.events;

import fr.minepiece.client.gui.inventory.AbilityButton;
import fr.minepiece.client.gui.inventory.IdentityButton;
import fr.minepiece.client.gui.inventory.StatisticsButton;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiButton;
import net.minecraft.client.gui.inventory.GuiContainer;
import net.minecraft.client.gui.inventory.GuiInventory;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraftforge.client.event.GuiScreenEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

@Mod.EventBusSubscriber
public class InventoryEvents {
    @SubscribeEvent
    @SideOnly(Side.CLIENT)
    public static void initGui(GuiScreenEvent.InitGuiEvent.Post event) {
        if(event.getGui() instanceof GuiContainer) {
            GuiContainer guiInv = (GuiContainer) event.getGui();
            EntityPlayer player = Minecraft.getMinecraft().player;

            boolean isPlayerInv = guiInv instanceof GuiInventory;
            boolean creative = player.isCreative();
            if(creative || (!isPlayerInv))
                return;

            int guiWidth = guiInv.getXSize();
            int guiHeight = guiInv.getYSize();

            int startIndex = -73;
            int separation = 30;
            GuiButton statsButton = new StatisticsButton( 82530,  guiInv.getGuiLeft() + guiWidth/2 + (startIndex+separation), guiInv.getGuiTop() + guiHeight + 5);
            GuiButton identityButton = new IdentityButton( 82531,  guiInv.getGuiLeft() + guiWidth/2 + (startIndex+(separation*2)), guiInv.getGuiTop() + guiHeight + 5);
            GuiButton abilitiesButton = new AbilityButton( 82532,  guiInv.getGuiLeft() + guiWidth/2 + (startIndex+(separation*3)), guiInv.getGuiTop() + guiHeight + 5);
            event.getButtonList().add(statsButton);
            event.getButtonList().add(identityButton);
            event.getButtonList().add(abilitiesButton);
            //updateTrashPos(guiInv);
        }
    }
}
