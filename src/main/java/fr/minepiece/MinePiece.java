package fr.minepiece;

import fr.minepiece.common.api.jobs.dto.LevelUpReward;
import fr.minepiece.common.blocks.jobs.crafting.blacksmith.forge.recipes.ForgeRecipeManager;
import fr.minepiece.common.blocks.jobs.crafting.medic.bancdemedicament.recipes.BancDeMedicamentRecipeManager;
import fr.minepiece.common.capability.knockout.KOCapabilityAttachment;
import fr.minepiece.common.events.*;
import fr.minepiece.common.init.*;
import fr.minepiece.common.items.weapons.shooting.TieredFlintlockRifle;
import fr.minepiece.common.utils.ClusterConfigManager;
import fr.minepiece.common.utils.TemperatureConfigManager;
import fr.minepiece.common.utils.TemperatureZoneConfigManager;
import fr.minepiece.common.utils.XPConfigManager;
import fr.minepiece.common.utils.handlers.JobsGuiHandler;
import fr.minepiece.common.capability.CapabilityRegistration;
import fr.minepiece.common.events.abilities.FishmanEvents;
import fr.minepiece.server.commands.*;
import fr.minepiece.common.network.ModPackets;
import fr.minepiece.common.CommonProxy;
import net.minecraft.creativetab.CreativeTabs;
import net.minecraft.init.Blocks;
import net.minecraft.item.ItemStack;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.common.SidedProxy;
import net.minecraftforge.fml.common.event.FMLInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPostInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import net.minecraftforge.fml.common.event.FMLServerStartingEvent;
import net.minecraftforge.fml.common.network.NetworkRegistry;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import software.bernie.geckolib3.GeckoLib;

@Mod(modid = Reference.MOD_ID, name = Reference.MOD_NAME, version = Reference.MOD_VERSION)
public class MinePiece {

	///////////////////////////////////////////////////////////////////////////
	// Infos du Mod
	///////////////////////////////////////////////////////////////////////////

	public static final Logger LOGGER = LogManager.getLogger(Reference.MOD_ID);

	public static final CreativeTabs WEAPONS = new CreativeTabs("minepiece.weapons") {
		@Override
		public ItemStack getTabIconItem() {
			return new ItemStack(ModWeapons.ACE);
		}
	};
	public static final CreativeTabs ARMORS = new CreativeTabs("minepiece.armors") {
		@Override
		public ItemStack getTabIconItem() {
			return new ItemStack(ModTieredItems.TIERED_MARINE_HELMET);
		}
	};
	public static final CreativeTabs BLOCKS = new CreativeTabs("minepiece.blocks") {
		@Override
		public ItemStack getTabIconItem() {
			return new ItemStack(ModBlocks.POLLEN_PRISMATIQUE);
		}
	};
	public static final CreativeTabs MONEY = new CreativeTabs("minepiece.money") {
		@Override
		public ItemStack getTabIconItem() {
			return new ItemStack(ModItems.BERRY,1, 6);
		}
	};
	public static final CreativeTabs CROPS = new CreativeTabs("minepiece.crops") {
		@Override
		public ItemStack getTabIconItem() {return new ItemStack(ModSeeds.PIMENT_SEED); }
	};
	public static final CreativeTabs FOOD = new CreativeTabs("minepiece.food") {
		@Override
		public ItemStack getTabIconItem() {return new ItemStack(ModFoods.BURGER); }
	};
	public static final CreativeTabs BEVERAGE = new CreativeTabs("minepiece.beverage") {
		@Override
		public ItemStack getTabIconItem() {return new ItemStack(ModBeverages.COLA); }
	};
	public static final CreativeTabs ITEMS = new CreativeTabs("minepiece.items") {
		@Override
		public ItemStack getTabIconItem() {return new ItemStack(ModTools.THERMOMETER); }
	};
	public static final CreativeTabs JOBS = new CreativeTabs("minepiece.jobs") {
		@Override
		public ItemStack getTabIconItem() {return new ItemStack(ModBlocks.PILON); }
	};

	///////////////////////////////////////////////////////////////////////////
	// Creative Tabs
	///////////////////////////////////////////////////////////////////////////
	@Mod.Instance(Reference.MOD_ID)
	public static MinePiece INSTANCE;
	@SidedProxy(clientSide = Reference.PROXY_CLIENT, serverSide = Reference.PROXY_SERVER)
	public static CommonProxy proxy;


	///////////////////////////////////////////////////////////////////////////
	// Events
	///////////////////////////////////////////////////////////////////////////



	@Mod.EventHandler
	public void preInit(FMLPreInitializationEvent e) {

		ModPackets.init();

		ModConfiguration.registerConfig(e);
		GeckoLib.initialize();
		ModEntities.registerEntities();
		proxy.preInit(e);

		MinecraftForge.EVENT_BUS.register(new ScaleEvents());
		MinecraftForge.EVENT_BUS.register(new FishmanEvents());
		MinecraftForge.EVENT_BUS.register(KOCapabilityAttachment.class);
		MinecraftForge.EVENT_BUS.register(TieredFlintlockRifle.class);
		MinecraftForge.EVENT_BUS.register(new WeaponZoomEvents());

		CapabilityRegistration.registerCapabilities() ;


		TieredItemEvents.registerEvents();
		TieredItemHandler.registerEvents();
		SmokeEffectHandler.registerEvents();

		NetworkRegistry.INSTANCE.registerGuiHandler(INSTANCE, new JobsGuiHandler());

		LevelUpReward.initializeAllJobsRewards();
		TemperatureConfigManager.initialize(e);
		ClusterConfigManager.initialize(e);
		XPConfigManager.initialize(e);
		TemperatureZoneConfigManager.initialize(e);
		ForgeRecipeManager.initializeAdapters();
		BancDeMedicamentRecipeManager.initialize();
	}

	@Mod.EventHandler
	public void init(FMLInitializationEvent e) {
		proxy.init(e);
	}

	@Mod.EventHandler
	public void postInit(FMLPostInitializationEvent e) {
		proxy.postInit(e);
	}

	@Mod.EventHandler
	public void serverStarting(FMLServerStartingEvent event) {
		event.registerServerCommand(new ResetUserCommands());
		event.registerServerCommand(new ScaleIncreaseCommand());
		event.registerServerCommand(new EnergyGiveCommand());
		event.registerServerCommand(new StatsTestCommand());
		event.registerServerCommand(new ParticleTestCommand());
		event.registerServerCommand(new SetLevelCommand());
		event.registerServerCommand(new GiveUpCommand());
		event.registerServerCommand(new JobXpGiveCommand());
		event.registerServerCommand(new CapabilityTestCommand());
		event.registerServerCommand(new LimitCommand());
		event.registerServerCommand(new ReviveCommand());
		event.registerServerCommand(new TemperatureConfigCommand());
		event.registerServerCommand(new TemperatureConfigSaveCommand());
		event.registerServerCommand(new TemperatureDebugCommand());
		event.registerServerCommand(new ClusterConfigCommand());
		event.registerServerCommand(new ClusterConfigSaveCommand());
		event.registerServerCommand(new ClusterDebugCommand());
		event.registerServerCommand(new XPConfigCommand());
		event.registerServerCommand(new XPInfoCommand());
		event.registerServerCommand(new TemperatureZoneCommand());
		event.registerServerCommand(new TemperatureZoneSaveCommand());
	}


}
