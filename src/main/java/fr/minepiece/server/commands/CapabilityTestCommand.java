package fr.minepiece.server.commands;

import fr.minepiece.common.capability.CapabilityHelper;
import fr.minepiece.common.capability.mpdata.IMinePieceData;
import fr.minepiece.common.capability.mpstats.IMinePieceStats;
import fr.minepiece.common.capability.playermodifications.IPlayerModifications;
import fr.minepiece.common.capability.playermodifications.PlayerModificationsStorage;
import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.TextComponentTranslation;

/**
 * Server command that sends a given player's capabilities to the client who executed the command.
 * */
public class CapabilityTestCommand extends CommandBase {
    @Override
    public String getName() {
        return "capagetserver";
    }

    @Override
    public String getUsage(ICommandSender sender) {
        return getName() + " <player> <modifications|data|stats>";
    }

    @Override
    public void execute(MinecraftServer server, ICommandSender sender, String[] args) throws CommandException {
        if (!sender.canUseCommand(2, getName())) {
            sender.sendMessage(new TextComponentString(new TextComponentTranslation("minepiece.message.no_permission.command").getFormattedText()));
            return;
        }
        if (args.length < 2) {
            throw new CommandException("Invalid arguments. Usage: " + getUsage(sender));
        }

        EntityPlayerMP targetPlayer = server.getPlayerList().getPlayerByUsername(args[0]);
        if (targetPlayer == null) {
            throw new CommandException("Player not found: " + args[0]);
        }

        String capabilityType = args[1];
        switch (capabilityType) {
            case "modifications":
                IPlayerModifications modifications = targetPlayer.getCapability(PlayerModificationsStorage.PLAYER_MODIFICATIONS_CAPABILITY, null);
                if (modifications != null) {
                    String output = "Modifications: " + targetPlayer.getName() + "\n";
                    output += "Pose: " + modifications.getPose() + "\n";
                    output += "Skin: " + modifications.getSkin() + "\n";
                    output += "Modifications: " + modifications.getModifications() + "\n";
                    output += "Skin Modifications: " + modifications.getSkinModifications() + "\n";
                    output += "Skin Color: " + modifications.getSkinColor() + "\n";
                    output += "Modification Color: " + modifications.getModificationColor() + "\n";
                    sender.sendMessage(new TextComponentString(output));
                } else {
                    throw new CommandException("Player modifications capability not found.");
                }
                break;
            case "data":
                IMinePieceData data = CapabilityHelper.getPlayerData(targetPlayer);
                if (data != null) {
                    String output = "Data for: " + targetPlayer.getName() + "\n";
                    output += "Level: " + data.getLevel() + "\n";
                    output += "Experience: " + data.getXP() + "\n";
                    output += "RP Name: " + data.getRPName() + "\n";
                    output += "RP Last Name: " + data.getRPLastName() + "\n";
                    output += "Race: " + data.getRace() + "\n";
                    output += "Skills: " + data.getSkills() + "\n";
                    output += "Daily Craft Limit: " + data.getDailyCraftLimit() + "\n";
                    output += "Job: " + data.getJob() + "\n";
                    output += "Job Level: " + data.getJobLevel() + "\n";
                    output += "Job XP: " + data.getJobXP() + "\n";
                    output += "RP age: " + data.getRPAge() + "\n";
                    sender.sendMessage(new TextComponentString(output));
                } else {
                    throw new CommandException("Player data capability not found.");
                }
                break;
            case "stats":
                IMinePieceStats stats = CapabilityHelper.getPlayerStats(targetPlayer);
                if (stats != null) {
                    String output = "Stats for: " + targetPlayer.getName() + "\n";
                    output += "Energy: " + stats.getEnergy() + "\n";
                    output += "Max Energy: " + stats.getMaxEnergy() + "\n";
                    output += "Actual Max Energy: " + stats.getActualMaxEnergy() + "\n";
                    output += "Strength: " + stats.getStrength() + "\n";
                    output += "HP: " + stats.getHP() + "\n";
                    output += "Speed: " + stats.getSpeed() + "\n";
                    output += "Defense: " + stats.getToughness() + "\n";
                    output += "StatPoints: " + stats.getStatPoints() + "\n";
                    sender.sendMessage(new TextComponentString(output));
                } else {
                    throw new CommandException("Player stats capability not found.");
                }
                break;
            default:
                throw new CommandException("Invalid capability type. Usage: " + getUsage(sender));
        }
    }
}
