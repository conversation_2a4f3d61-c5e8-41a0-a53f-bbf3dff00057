package fr.minepiece.server.commands;

import fr.minepiece.common.utils.XPConfigManager;
import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.TextFormatting;

import java.util.ArrayList;
import java.util.List;

/**
 * Commande pour configurer les paramètres XP
 */
public class XPConfigCommand extends CommandBase {

    @Override
    public String getName() {
        return "Minepiece_xp_config";
    }

    @Override
    public String getUsage(ICommandSender sender) {
        return "/Minepiece_xp_config <interval|amount|jobtoggle|save|load> [value]";
    }

    @Override
    public int getRequiredPermissionLevel() {
        return 2; // Niveau opérateur requis
    }

    @Override
    public void execute(MinecraftServer server, ICommandSender sender, String[] args) throws CommandException {
        if (args.length < 1) {
            displayAllInfo(sender);
            return;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "interval":
                if (args.length < 2) {
                    sender.sendMessage(new TextComponentString(TextFormatting.YELLOW +
                            "Intervalle actuel: " + XPConfigManager.getSecondsBetweenPassiveXP() + " secondes"));
                } else {
                    handleIntervalConfig(sender, parseInt(args[1]));
                }
                break;

            case "amount":
                if (args.length < 2) {
                    sender.sendMessage(new TextComponentString(TextFormatting.YELLOW +
                            "Quantité actuelle: " + XPConfigManager.getPassiveXPAmount() + " XP"));
                } else {
                    handleAmountConfig(sender, parseInt(args[1]));
                }
                break;

            case "jobtoggle":
                handleJobToggle(sender);
                break;

            case "save":
                XPConfigManager.saveConfig();
                sender.sendMessage(new TextComponentString(TextFormatting.GREEN +
                        "Configuration XP sauvegardée dans le fichier"));
                break;

            case "load":
                XPConfigManager.loadConfig();
                sender.sendMessage(new TextComponentString(TextFormatting.GREEN +
                        "Configuration XP chargée depuis le fichier"));
                break;

            default:
                displayAllInfo(sender);
                break;
        }
    }

    /**
     * Affiche toutes les informations sur la configuration XP
     */
    private void displayAllInfo(ICommandSender sender) {
        sender.sendMessage(new TextComponentString(TextFormatting.GOLD + "=== Configuration XP ==="));
        sender.sendMessage(new TextComponentString(TextFormatting.YELLOW +
                "Intervalle entre gains: " + XPConfigManager.getSecondsBetweenPassiveXP() + " secondes"));
        sender.sendMessage(new TextComponentString(TextFormatting.YELLOW +
                "Quantité d'XP passive: " + XPConfigManager.getPassiveXPAmount() + " XP"));
        sender.sendMessage(new TextComponentString(TextFormatting.YELLOW +
                "XP de job activée: " + (XPConfigManager.isGiveJobXP() ?
                TextFormatting.GREEN + "OUI" :
                TextFormatting.RED + "NON")));
    }

    /**
     * Gère la configuration de l'intervalle
     */
    private void handleIntervalConfig(ICommandSender sender, int seconds) throws CommandException {
        if (seconds < 1) {
            throw new CommandException("L'intervalle doit être d'au moins 1 seconde");
        }

        XPConfigManager.setSecondsBetweenPassiveXP(seconds);
        sender.sendMessage(new TextComponentString(TextFormatting.GREEN +
                "Intervalle entre gains d'XP passive réglé à " + seconds + " secondes"));
    }

    /**
     * Gère la configuration de la quantité d'XP
     */
    private void handleAmountConfig(ICommandSender sender, int amount) throws CommandException {
        if (amount < 0) {
            throw new CommandException("La quantité d'XP doit être positive ou nulle");
        }

        XPConfigManager.setPassiveXPAmount(amount);
        sender.sendMessage(new TextComponentString(TextFormatting.GREEN +
                "Quantité d'XP passive réglée à " + amount + " XP"));
    }

    /**
     * Gère l'activation/désactivation de l'XP de métier
     */
    private void handleJobToggle(ICommandSender sender) {
        boolean newState = XPConfigManager.toggleGiveJobXP();
        sender.sendMessage(new TextComponentString(TextFormatting.GREEN +
                "Gain d'XP de métier " + (newState ?
                TextFormatting.GREEN + "ACTIVÉ" :
                TextFormatting.RED + "DÉSACTIVÉ")));
    }

    @Override
    public List<String> getTabCompletions(MinecraftServer server, ICommandSender sender, String[] args, BlockPos targetPos) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            completions.add("interval");
            completions.add("amount");
            completions.add("jobtoggle");
            completions.add("save");
            completions.add("load");
        }

        return getListOfStringsMatchingLastWord(args, completions);
    }
}