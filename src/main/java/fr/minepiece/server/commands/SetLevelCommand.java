package fr.minepiece.server.commands;

import fr.minepiece.common.capability.mpdata.IMinePieceData;
import fr.minepiece.common.capability.mpdata.MinePieceDataStorage;
import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.text.TextComponentString;

public class SetLevelCommand extends CommandBase {
    @Override
    public String getName() {
        return "setlevel";
    }

    @Override
    public String getUsage(ICommandSender sender) {
        return getName() + " <username> <level>";
    }

    @Override
    public void execute(MinecraftServer server, ICommandSender sender, String[] args) throws CommandException {
        if (args.length != 2) {
            sendUsage(sender);
            return;
        }
        int level;
        try {
            level = Integer.parseInt(args[1]);
        } catch (NumberFormatException e) {
            sender.sendMessage(new TextComponentString("You must send a number!"));
            sendUsage(sender);
            return;
        }

        EntityPlayerMP player = server.getPlayerList().getPlayerByUsername(args[0]);
        if (player == null) {
            sender.sendMessage(new TextComponentString("Could not find the target player"));
            sendUsage(sender);
        }

        IMinePieceData data1 = player.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);
        assert data1 != null;
        data1.setLevel(level);
        data1.clientSync(player);
    }

    private void sendUsage(ICommandSender sender) {
        sender.sendMessage(new TextComponentString(getUsage(sender)));
    }
}
