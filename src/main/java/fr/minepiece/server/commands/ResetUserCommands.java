package fr.minepiece.server.commands;

import fr.minepiece.common.api.stats.StatsUtils;
import fr.minepiece.common.api.utils.PlayerUtils;
import fr.minepiece.common.capability.CapabilityHelper;
import fr.minepiece.common.capability.mpdata.IMinePieceData;
import fr.minepiece.common.capability.mpdata.MinePieceDataStorage;
import fr.minepiece.common.capability.mpstats.IMinePieceStats;
import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.TextComponentTranslation;
import net.minecraft.util.text.TextFormatting;

import java.util.ArrayList;
import java.util.Objects;

public class ResetUserCommands extends CommandBase {

    @Override
    public String getName() {
        return "resetuser";
    }

    @Override
    public String getUsage(ICommandSender sender) {
        return "/" + getName() + " <user>";
    }

    @Override
    public void execute(MinecraftServer server, ICommandSender sender, String[] args) throws CommandException {
        if (!sender.canUseCommand(4, getName())) {
            sender.sendMessage(new TextComponentString(new TextComponentTranslation("minepiece.message.no_permission.command").getFormattedText()));
            return;
        }
        if (args.length == 0) {
            sender.sendMessage(new TextComponentString("Please specify a user!"));
            return;
        }
        String username = args[0];
        EntityPlayer player = server.getPlayerList().getPlayerByUsername(username);

        assert player != null;
        IMinePieceData data = player.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);
        assert data != null;

        data.setLevel(0);
        data.setJobLevel(1);
        data.setRace(-1);
        data.setXP(0);
        data.setDailyCraftLimit(10);
        data.setJobXP(0);
        data.setRPAge(-1);
        data.setRPName("");
        data.setRPLastName("");

        data.setSkills(new ArrayList<>());

        IMinePieceStats stats = CapabilityHelper.getPlayerStats(player);
        stats.setStatPoints(0);
        stats.setEnergy(0);
        stats.setMaxEnergy(0);
        stats.setHP(0);
        stats.setSpeed(0);
        stats.setStrength(0);
        stats.setToughness(0);

        StatsUtils.resetModifiers(player); // commenting this doesnt fix the crash

        data.clientSync((EntityPlayerMP) player);
        stats.clientSync((EntityPlayerMP) player); // commenting this doesnt fix the crash

        //MinePiece.NETWORK.sendToServer(new RaceIDPacket(-1));

        TextComponentString kickMessage = new TextComponentString("Un admin a reset votre compte");
        kickMessage.getStyle().setColor(TextFormatting.DARK_RED);
        Objects.requireNonNull(server.getPlayerList().getPlayerByUsername(username)).connection.disconnect(kickMessage);
        //PlayerUtils.kickPlayer(username, kickMessage);
    }
}
