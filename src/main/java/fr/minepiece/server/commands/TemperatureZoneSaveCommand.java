package fr.minepiece.server.commands;

import fr.minepiece.common.utils.TemperatureZoneConfigManager;
import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.TextFormatting;

import java.util.Arrays;
import java.util.List;

public class TemperatureZoneSaveCommand extends CommandBase {

    @Override
    public String getName() {
        return "Minepiece_temp_zone_config";
    }

    @Override
    public String getUsage(ICommandSender sender) {
        return "/Minepiece_temp_zone_config <save|load>";
    }

    @Override
    public int getRequiredPermissionLevel() {
        return 2; // Niveau opérateur requis
    }

    @Override
    public void execute(MinecraftServer server, ICommandSender sender, String[] args) throws CommandException {
        if (args.length != 1) {
            throw new CommandException("Utilisation : " + getUsage(sender));
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "save":
                boolean saveSuccess = TemperatureZoneConfigManager.saveConfig();
                if (saveSuccess) {
                    sender.sendMessage(new TextComponentString(TextFormatting.GREEN +
                            "Zones de température sauvegardées dans le fichier de configuration"));
                } else {
                    sender.sendMessage(new TextComponentString(TextFormatting.RED +
                            "Erreur lors de la sauvegarde des zones de température"));
                }
                break;

            case "load":
                boolean loadSuccess = TemperatureZoneConfigManager.loadConfig();
                if (loadSuccess) {
                    sender.sendMessage(new TextComponentString(TextFormatting.GREEN +
                            "Zones de température chargées depuis le fichier de configuration"));
                } else {
                    sender.sendMessage(new TextComponentString(TextFormatting.RED +
                            "Erreur lors du chargement des zones de température ou fichier inexistant"));
                }
                break;

            default:
                throw new CommandException("Sous-commande inconnue : " + subCommand + ". Utilisez 'save' ou 'load'");
        }
    }

    @Override
    public List<String> getTabCompletions(MinecraftServer server, ICommandSender sender, String[] args, net.minecraft.util.math.BlockPos targetPos) {
        if (args.length == 1) {
            return getListOfStringsMatchingLastWord(args, Arrays.asList("save", "load"));
        }
        return super.getTabCompletions(server, sender, args, targetPos);
    }
}