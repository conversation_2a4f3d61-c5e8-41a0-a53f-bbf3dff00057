package fr.minepiece.server.commands;

import fr.minepiece.client.render.Skins;
import fr.minepiece.common.capability.CapabilityHelper;
import fr.minepiece.common.capability.mpdata.IMinePieceData;
import fr.minepiece.common.capability.mpstats.IMinePieceStats;
import fr.minepiece.common.capability.playermodifications.IPlayerModifications;
import fr.minepiece.common.capability.playermodifications.PlayerModificationsStorage;
import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.text.TextComponentString;

import java.util.ArrayList;

public class StatsTestCommand extends CommandBase {

    @Override
    public String getName() {
        return "statstest";
    }

    @Override
    public String getUsage(ICommandSender sender) {
        return getName() + " <value>";
    }

    @Override
    public void execute(MinecraftServer server, ICommandSender sender, String[] args) throws CommandException {
        if (!(sender instanceof EntityPlayer)) {
            sender.sendMessage(new TextComponentString("You must be a player to do that"));
            return;
        }
        EntityPlayer player = (EntityPlayer) sender;

        IPlayerModifications modifications = player.getCapability(PlayerModificationsStorage.PLAYER_MODIFICATIONS_CAPABILITY, null);
        assert modifications != null;
        switch (args[0]) {
            case "0":
                modifications.setModifications(new ArrayList<>());
                break;
            case "1":
                modifications.setModifications(new ArrayList<String>() {{
                    add("mermaid_tail");
                }});
                break;
            case "2":
                modifications.setModifications(new ArrayList<String>() {{
                    add("shark_tail");
                    add("back_shark_fin");
                }});
                break;
            case "3":
                modifications.setModifications(new ArrayList<String>() {{
                    add("fighter_fishman");
                }});
                break;
            case "4":
                modifications.setModifications(new ArrayList<String>() {{
                    add("fishman_nose");
                    add("back_shark_fin");
                }});
                break;
            case "5":
                modifications.setModifications(new ArrayList<String>() {{
                    add("sea_horse_tail");
                    add("back_sea_horse_fin");
                }});
                break;
            case "6":
                modifications.setModifications(new ArrayList<String>() {{
                    add("sea_bream");
                }});
                break;
            case "7":
                modifications.setModifications(new ArrayList<String>() {{
                    add("cow_fish");
                }});
                break;
            case "8":
                modifications.setModifications(new ArrayList<String>() {{
                    add("right_ray_fin");
                    add("left_ray_fin");
                }});
                break;
            case "9":
                modifications.setModifications(new ArrayList<String>() {{
                    add("whale_shark_tail");
                    add("back_shark_fin");
                }});
                break;
            case "10":
                modifications.setModifications(new ArrayList<String>() {{
                    add("rodent_tail");
                    add("rodent_head");
                }});
                break;
            case "11":
                modifications.setModifications(new ArrayList<String>() {{
                    add("canine_head");
                    add("canine_tail");
                }});
                break;
            case "12":
                modifications.setModifications(new ArrayList<String>() {{
                    add("feline_tail");
                    add("feline_head");
                }});
                break;
            case "13":
                modifications.setModifications(new ArrayList<String>() {{
                    add("bear_tail");
                    add("bear_head");
                }});
                break;
            case "14":
                modifications.setModifications(new ArrayList<String>() {{
                    add("cervid_antlers");
                    add("cervid_head");
                    add("cervid_tail");
                }});
                break;
            case "15":
                modifications.setModifications(new ArrayList<String>() {{
                    add("gold_fish_tail");
                }});
                break;
            case "16":
                modifications.setModifications(new ArrayList<String>() {{
                    add("tontatta_tail");
                }});
                break;
            case "17":
                modifications.setModifications(new ArrayList<String>() {{
                    add("sulong");
                }});
                modifications.setSkinColor(0xFFFFFF);
                modifications.setModificationColor(0xFFFFFF);
                break;
            case "101":
                modifications.setSkin(Skins.HUMAN_MALE_FAT.getName());
                break;
            case "102":
                modifications.setSkin(Skins.HUMAN_MALE_NORMAL.getName());
                break;
            case "201":
                IMinePieceData data = CapabilityHelper.getPlayerData(player);
                data.setSkills(new ArrayList<>());
                data.clientSync((EntityPlayerMP) player);
                break;
            case "250":
                IMinePieceStats stats = CapabilityHelper.getPlayerStats(player);
                stats.setStatPoints(0);
                stats.clientSync((EntityPlayerMP) player);
                break;
        }

        modifications.clientSync((EntityPlayerMP) player);
        System.out.println("MODIFICATIONS: " + modifications.getModifications());
        /*
        IMinePieceStats data = player.getCapability(MinePieceStatsStorage.MP_STATS_CAPABILITY, null);
        //data.setStrength(Integer.parseInt(args[0]));
        //data.setSpeed(Integer.parseInt(args[0]));
        //data.setHP(Integer.parseInt(args[0]));
        data.setMaxEnergy(Integer.parseInt(args[0]));
        //data.setStatPoints(Integer.parseInt(args[0]));
        data.clientSync((EntityPlayerMP) player);
        StatsUtils.updateStats(player);
         */

        //IMinePieceData data1 = player.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);
        //data1.setLevel(Integer.parseInt(args[0]));
        //data1.clientSync((EntityPlayerMP) player);
        //data1.getRace().getInstance().init(player, player.world);

        System.out.println("EXECTUED");
    }
}
