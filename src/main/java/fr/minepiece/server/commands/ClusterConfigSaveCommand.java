package fr.minepiece.server.commands;

import fr.minepiece.common.utils.ClusterConfigManager;
import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.TextFormatting;

/**
 * Commande simplifiée pour sauvegarder/charger la configuration des clusters
 */
public class ClusterConfigSaveCommand extends CommandBase {

    @Override
    public String getName() {
        return "Minepiece_cluster_save";
    }

    @Override
    public String getUsage(ICommandSender sender) {
        return "/Minepiece_cluster_save <save|load>";
    }

    @Override
    public int getRequiredPermissionLevel() {
        return 2; // Niveau opérateur requis
    }

    @Override
    public void execute(MinecraftServer server, ICommandSender sender, String[] args) throws CommandException {
        if (args.length != 1) {
            throw new CommandException("Utilisation : " + getUsage(sender));
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "save":
                ClusterConfigManager.saveConfig();
                sender.sendMessage(new TextComponentString(TextFormatting.GREEN +
                        "Configuration des clusters sauvegardée dans le fichier"));
                break;

            case "load":
                ClusterConfigManager.loadConfig();
                sender.sendMessage(new TextComponentString(TextFormatting.GREEN +
                        "Configuration des clusters chargée depuis le fichier"));
                break;

            default:
                throw new CommandException("Sous-commande inconnue : " + subCommand + ". Utilisez 'save' ou 'load'.");
        }
    }
}