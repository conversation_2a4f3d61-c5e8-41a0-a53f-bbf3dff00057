package fr.minepiece.server.commands;

import fr.minepiece.common.init.ModConfiguration;
import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.TextComponentTranslation;

public class LimitCommand extends CommandBase {
    @Override
    public String getName() {
        return "limit";
    }

    @Override
    public String getUsage(ICommandSender sender) {
        return "/" + getName() + " <player|job> <limit|-1>";
    }

    @Override
    public void execute(MinecraftServer server, ICommandSender sender, String[] args) throws CommandException {
        if (!sender.canUseCommand(4, getName())) {
            sender.sendMessage(new TextComponentString(new TextComponentTranslation("minepiece.message.no_permission.command").getFormattedText()));
            return;
        }
        if (args.length < 2) {
            throw new CommandException("Invalid arguments. Usage: " + getUsage(sender));
        }

        String type = args[0];
        int limit = Integer.parseInt(args[1]);

        switch (type) {
            case "job":
                ModConfiguration.maxJobLevel = limit;
                break;
            case "player":
                ModConfiguration.maxPlayerLevel = limit;
                break;
            default:
                throw new CommandException("Invalid arguments. Usage: " + getUsage(sender));
        }

        ModConfiguration.config.getCategory("level limits").get("Max Player Level").set(ModConfiguration.maxPlayerLevel);
        ModConfiguration.config.getCategory("level limits").get("Max Job Level").set(ModConfiguration.maxJobLevel);
        ModConfiguration.config.save();
        sender.sendMessage(new TextComponentString("Max " + type + " level set to " + limit));
    }
}
