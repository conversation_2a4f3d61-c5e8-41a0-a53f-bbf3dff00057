package fr.minepiece.server.commands;

import fr.minepiece.client.particles.ModParticles;
import fr.minepiece.common.network.ModPackets;
import fr.minepiece.common.network.packets.races.SpawnParticlePacket;
import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.server.MinecraftServer;

public class ParticleTestCommand extends CommandBase {
    @Override
    public String getName() {
        return "spawnparticle";
    }

    @Override
    public String getUsage(ICommandSender sender) {
        return null;
    }

    @Override
    public void execute(MinecraftServer server, ICommandSender sender, String[] args) throws CommandException {
        if (sender instanceof EntityPlayerMP) {
            //((EntityPlayerMP) sender).world.spawnParticle(ModParticles.MINKS_ERING, ((EntityPlayerMP) sender).posX, ((EntityPlayerMP) sender).posY, ((EntityPlayerMP) sender).posZ, 0, 0, 0);
            //ModPackets.NETWORK.sendTo(new TestParticlePacket(), (EntityPlayerMP) sender);

            //ModPackets.NETWORK.sendTo(new SpawnParticlePacket(ModParticles.LIGHTNING_FX, sender.getPosition().getX(), sender.getPosition().getY(), sender.getPosition().getZ()), (EntityPlayerMP) sender);
            ModPackets.NETWORK.sendTo(new SpawnParticlePacket(ModParticles.SKYWALK, sender.getPosition().getX() + 2, sender.getPosition().getY() + 1, sender.getPosition().getZ() + 2), (EntityPlayerMP) sender);

            /*
            EntityPlayerMP playerMP = (EntityPlayerMP) sender;
            //ModPackets.NETWORK.sendTo(new ElectroPacket(playerMP.getName()), playerMP); // need to change this to everyone so everyone see particle
            EntityMinksRing ring = new EntityMinksRing(playerMP.world);
            ring.setPosition(playerMP.posX, playerMP.posY + 2, playerMP.posZ);
            ring.setNoGravity(true);
            playerMP.world.spawnEntity(ring);

             */
        }
    }

}
