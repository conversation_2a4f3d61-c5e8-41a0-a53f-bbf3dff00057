package fr.minepiece.server.events;

import fr.minepiece.Reference;
import fr.minepiece.common.capability.mpcooldowns.DefaultMinePieceCooldown;
import fr.minepiece.common.capability.mpcooldowns.IMinePieceCooldown;
import fr.minepiece.common.capability.mpcooldowns.MinePieceCooldownStorage;
import fr.minepiece.common.capability.mpdata.DefaultMinePieceData;
import fr.minepiece.common.capability.mpdata.IMinePieceData;
import fr.minepiece.common.capability.mpdata.MinePieceDataStorage;
import fr.minepiece.common.capability.mpstats.DefaultMinePieceStats;
import fr.minepiece.common.capability.mpstats.IMinePieceStats;
import fr.minepiece.common.capability.mpstats.MinePieceStatsStorage;
import fr.minepiece.common.capability.playermodifications.DefaultPlayerModifications;
import fr.minepiece.common.capability.playermodifications.IPlayerModifications;
import fr.minepiece.common.capability.playermodifications.PlayerModificationsStorage;
import fr.minepiece.common.capability.thirst.DefaultThirst;
import fr.minepiece.common.capability.thirst.IThirst;
import fr.minepiece.common.capability.thirst.ThirstProvider;
import fr.minepiece.common.capability.thirst.ThirstStorage;
import fr.minepiece.common.network.ModPackets;
import fr.minepiece.common.network.packets.FullModificationsPacket;
import fr.minepiece.common.network.packets.TextureRecomputePacket;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraftforge.fml.common.FMLCommonHandler;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.PlayerEvent;

@Mod.EventBusSubscriber(modid = Reference.MOD_ID)
public class MinePieceServerEvents {

    /**
     * Synchronises the player's capabilities when they die
     * */
    @SubscribeEvent
    public static void onPlayerDeath(final net.minecraftforge.event.entity.player.PlayerEvent.Clone e) {
        if (e.getEntityPlayer().world.isRemote) return;

        syncPlayerData(e.getOriginal(), e.getEntityPlayer());

        if (e.getEntityPlayer() instanceof EntityPlayerMP) {
            syncToAllPlayers((EntityPlayerMP) e.getEntityPlayer());
            initPlayerRace(e.getEntityPlayer());
        }
    }

    private static void syncPlayerData(EntityPlayer original, EntityPlayer newPlayer) {
        // MinePiece Data
        IMinePieceData oldData = original.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);
        IMinePieceData data = newPlayer.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);
        if (oldData != null && data != null) {
            DefaultMinePieceData.syncStats(oldData, data);
        }

        // MinePiece Stats
        IMinePieceStats oldStats = original.getCapability(MinePieceStatsStorage.MP_STATS_CAPABILITY, null);
        IMinePieceStats stats = newPlayer.getCapability(MinePieceStatsStorage.MP_STATS_CAPABILITY, null);
        if (oldStats != null && stats != null) {
            DefaultMinePieceStats.syncStats(oldStats, stats);
        }

        // MinePiece Cooldowns
        IMinePieceCooldown oldCooldown = original.getCapability(MinePieceCooldownStorage.MP_COOLDOWN_CAPABILITY, null);
        IMinePieceCooldown cooldown = newPlayer.getCapability(MinePieceCooldownStorage.MP_COOLDOWN_CAPABILITY, null);
        if (oldCooldown != null && cooldown != null) {
            DefaultMinePieceCooldown.syncStats(oldCooldown, cooldown);
        }

        // Player Modifications
        IPlayerModifications oldModifications = original.getCapability(PlayerModificationsStorage.PLAYER_MODIFICATIONS_CAPABILITY, null);
        IPlayerModifications modifications = newPlayer.getCapability(PlayerModificationsStorage.PLAYER_MODIFICATIONS_CAPABILITY, null);
        if (oldModifications != null && modifications != null) {
            DefaultPlayerModifications.syncStats(oldModifications, modifications);
        }

        IThirst oldThirst = original.getCapability(ThirstProvider.THIRST_CAPABILITY, null);
        IThirst thirst = newPlayer.getCapability(ThirstProvider.THIRST_CAPABILITY, null);
        if (oldThirst != null && thirst != null) {
            DefaultThirst.syncStats(oldThirst, thirst);
        }
    }

    @SubscribeEvent
    public static void onDimensionChange(PlayerEvent.PlayerChangedDimensionEvent event) {
        if (event.player instanceof EntityPlayerMP) { // Server-side
            EntityPlayerMP player = (EntityPlayerMP) event.player;
            // Sync to others
            syncToAllPlayers(player);
            //syncExistingPlayersToNewPlayer(player);
            ModPackets.NETWORK.sendTo(new TextureRecomputePacket(), player);
        }
    }

    @SubscribeEvent
    public static void onPlayerRespawn(PlayerEvent.PlayerRespawnEvent event) {
        if (!event.player.world.isRemote) { // Server-side
            syncToAllPlayers((EntityPlayerMP) event.player);
        }
    }

    @SubscribeEvent
    public static void onEntityTrack(net.minecraftforge.event.entity.player.PlayerEvent.StartTracking event) {
        if (event.getTarget() instanceof EntityPlayerMP) {
            EntityPlayerMP trackedPlayer = (EntityPlayerMP) event.getTarget();
            EntityPlayerMP trackingPlayer = (EntityPlayerMP) event.getEntityPlayer();
            syncToClient(trackedPlayer, trackingPlayer);
            ModPackets.NETWORK.sendTo(new TextureRecomputePacket(), trackingPlayer);
        }
    }

    @SubscribeEvent
    public static void onPlayerJoinWorld(net.minecraftforge.event.entity.EntityJoinWorldEvent event) {
        if (event.getEntity() instanceof EntityPlayer) {
            EntityPlayer player = (EntityPlayer) event.getEntity();
            if (!player.world.isRemote) { // Server-side
                syncToAllPlayers((EntityPlayerMP) player);
                syncExistingPlayersToNewPlayer((EntityPlayerMP) player);
            }
        }
    }

    /**
     * When a player logs in, we need to sync their data to the client and to all other players
     * */
    @SubscribeEvent
    public static void onPlayerLogin(PlayerEvent.PlayerLoggedInEvent event){
        if (event.player.world.isRemote) return;
        EntityPlayerMP joinedPlayer = (EntityPlayerMP) event.player;

        // 1. Initialize the player
        initPlayerRace(joinedPlayer);

        // 3. Sync the new player's data TO EVERYONE
        syncToAllPlayers(joinedPlayer);

        // 4. Sync existing players' data TO THE NEW PLAYER
        syncExistingPlayersToNewPlayer(joinedPlayer);
    }

    /**
     * S2C sync the player's data to all players
     * */
    private static void syncToAllPlayers(EntityPlayerMP sourcePlayer) {
        IMinePieceData data = sourcePlayer.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);
        IMinePieceStats stats = sourcePlayer.getCapability(MinePieceStatsStorage.MP_STATS_CAPABILITY, null);
        IPlayerModifications modifications = sourcePlayer.getCapability(PlayerModificationsStorage.PLAYER_MODIFICATIONS_CAPABILITY, null);
        IThirst thirst = sourcePlayer.getCapability(ThirstProvider.THIRST_CAPABILITY, null);

        if (data != null) data.clientSync(sourcePlayer);
        if (stats != null) stats.clientSync(sourcePlayer);
        if (modifications != null) modifications.clientSync(sourcePlayer);
        if (thirst != null) thirst.clientSync(sourcePlayer);
    }

    /**
     * S2C sync the existing players' data to the new player
     * */
    private static void syncExistingPlayersToNewPlayer(EntityPlayerMP newPlayer) {
        for (EntityPlayerMP existingPlayer : FMLCommonHandler.instance()
                .getMinecraftServerInstance().getPlayerList().getPlayers()) {

            if (existingPlayer != newPlayer) {
                IPlayerModifications existingData = existingPlayer.getCapability(PlayerModificationsStorage.PLAYER_MODIFICATIONS_CAPABILITY, null);

                if (existingData != null) {
                    ModPackets.NETWORK.sendTo(
                            new FullModificationsPacket(
                                    existingData.getModifications(),
                                    existingData.getSkinModifications(),
                                    existingData.getPose(),
                                    existingData.getSkin(),
                                    existingData.getSkinColor(),
                                    existingData.getModificationColor(),
                                    existingPlayer.getUniqueID()
                            ),
                            newPlayer
                    );
                }
            }
        }
    }

    private static void syncToClient(EntityPlayerMP trackedPlayer, EntityPlayerMP trackingPlayer) {
        IPlayerModifications modifications = trackedPlayer.getCapability(PlayerModificationsStorage.PLAYER_MODIFICATIONS_CAPABILITY, null);
        if (modifications != null) {
            ModPackets.NETWORK.sendTo(
                    new FullModificationsPacket(
                            modifications.getModifications(),
                            modifications.getSkinModifications(),
                            modifications.getPose(),
                            modifications.getSkin(),
                            modifications.getSkinColor(),
                            modifications.getModificationColor(),
                            trackedPlayer.getUniqueID()
                    ),
                    trackingPlayer
            );
        }
    }

    private static void initPlayerRace(EntityPlayer player) {
        IMinePieceData data = player.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);
        if (data != null) {
            data.getRace().getInstance().init(player, player.world);
        }
    }
}
