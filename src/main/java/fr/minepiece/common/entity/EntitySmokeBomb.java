package fr.minepiece.common.entity;

import fr.minepiece.client.particles.ParticleSmoke;
import fr.minepiece.common.init.ModWeapons;
import fr.minepiece.common.network.ModPackets;
import fr.minepiece.common.network.packets.PacketSpawnSmokeCloud;
import net.minecraft.client.Minecraft;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.entity.projectile.EntityThrowable;
import net.minecraft.init.MobEffects;
import net.minecraft.item.ItemStack;
import net.minecraft.nbt.NBTTagCompound;
import net.minecraft.network.datasync.DataParameter;
import net.minecraft.network.datasync.DataSerializers;
import net.minecraft.network.datasync.EntityDataManager;
import net.minecraft.potion.PotionEffect;
import net.minecraft.util.math.RayTraceResult;
import net.minecraft.world.World;
import net.minecraftforge.fml.common.FMLCommonHandler;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class EntitySmokeBomb extends EntityThrowable {

    private static final DataParameter<Integer> SMOKE_COLOR = EntityDataManager.createKey(EntitySmokeBomb.class, DataSerializers.VARINT);
    private static final int DEFAULT_SMOKE_COLOR = 0xAA3939; // Rouge brique 42f557 0xAA3939
    private static final int SMOKE_DURATION = 760; // 30 secondes
    private static final float SMOKE_RADIUS = 5.0F;
    private static final int PARTICLE_DENSITY = 250;

    // Nouvelles constantes pour contrôler le comportement de la fumée
    private static final float TURBULENCE_FACTOR = 0.012f;
    private static final float VORTEX_STRENGTH = 0.006f;
    private static final float RISE_SPEED_VARIANCE = 0.004f;

    private static final ConcurrentHashMap<UUID, SmokeCloudData> activeSmokeEffects = new ConcurrentHashMap<>();
    public EntitySmokeBomb(World worldIn) {
        super(worldIn);
    }

    public EntitySmokeBomb(World worldIn, EntityLivingBase throwerIn) {
        super(worldIn, throwerIn);
    }

    public EntitySmokeBomb(World worldIn, double x, double y, double z) {
        super(worldIn, x, y, z);
    }

    @Override
    protected void entityInit() {
        super.entityInit();
        this.dataManager.register(SMOKE_COLOR, DEFAULT_SMOKE_COLOR);
    }

    public void setSmokeColor(int color) {
        this.dataManager.set(SMOKE_COLOR, color);
    }

    public int getSmokeColor() {
        return this.dataManager.get(SMOKE_COLOR);
    }

    @Override
    protected void onImpact(RayTraceResult result) {
        if (!this.world.isRemote) {
            NBTTagCompound smokeData = new NBTTagCompound();
            smokeData.setInteger("Color", getSmokeColor());
            smokeData.setInteger("Duration", SMOKE_DURATION);
            smokeData.setFloat("Radius", SMOKE_RADIUS);
            smokeData.setInteger("Density", PARTICLE_DENSITY);

            UUID smokeId = UUID.randomUUID();
            smokeData.setUniqueId("SmokeId", smokeId);

            registerSmokeCloud(smokeId, this.posX, this.posY, this.posZ, SMOKE_RADIUS, SMOKE_DURATION);

            spawnSmokeEffect(smokeData);
            PacketSpawnSmokeCloud packet = new PacketSpawnSmokeCloud(
                    smokeId,
                    this.posX, this.posY, this.posZ,
                    SMOKE_RADIUS,
                    getSmokeColor(),
                    0.7f,
                    SMOKE_DURATION
            );
            // Send to all players in dimension near the impact
            ModPackets.NETWORK.sendToDimension(packet, this.world.provider.getDimension());
            this.setDead();
        }
    }

    private void registerSmokeCloud(UUID id, double x, double y, double z, float radius, int duration) {
        SmokeCloudData cloudData = new SmokeCloudData(x, y, z, radius, this.world.provider.getDimension(), this.world.getTotalWorldTime() + duration);
        activeSmokeEffects.put(id, cloudData);
    }

    private void spawnSmokeEffect(NBTTagCompound smokeData) {
        this.world.setEntityState(this, (byte)1);
        this.getEntityData().setTag("SmokeData", smokeData);
    }

    @Override
    public void writeEntityToNBT(NBTTagCompound compound) {
        super.writeEntityToNBT(compound);
        compound.setInteger("SmokeColor", this.getSmokeColor());
    }

    @Override
    public void readEntityFromNBT(NBTTagCompound compound) {
        super.readEntityFromNBT(compound);
        if (compound.hasKey("SmokeColor")) {
            this.setSmokeColor(compound.getInteger("SmokeColor"));
        }
    }

    @Override
    @SideOnly(Side.CLIENT)
    public void handleStatusUpdate(byte id) {
        if (id == 1) {
            NBTTagCompound smokeData = this.getEntityData().getCompoundTag("SmokeData");
            int color = smokeData.hasKey("Color") ? smokeData.getInteger("Color") : DEFAULT_SMOKE_COLOR;
            int duration = smokeData.hasKey("Duration") ? smokeData.getInteger("Duration") : SMOKE_DURATION;
            float radius = smokeData.hasKey("Radius") ? smokeData.getFloat("Radius") : SMOKE_RADIUS;
            int density = smokeData.hasKey("Density") ? smokeData.getInteger("Density") : PARTICLE_DENSITY;
            createSmokeCloud(this.world, this.posX, this.posY, this.posZ, color, radius, duration, density);
        } else {
            super.handleStatusUpdate(id);
        }
    }

    /**
     * Méthode statique qui applique les effets de cécité aux joueurs.
     * Cette méthode est appelée par un événement serveur à chaque tick.
     */
    public static void applyBlindingEffects() {
        if (activeSmokeEffects.isEmpty()) {
            return;
        }
        long currentTime = FMLCommonHandler.instance().getMinecraftServerInstance().worlds[0].getTotalWorldTime();
        activeSmokeEffects.forEach((id, data) -> {
            if (data.expirationTime < currentTime) {
                activeSmokeEffects.remove(id);
            }
        });
        if (activeSmokeEffects.isEmpty()) {
            return;
        }
        for (World world : FMLCommonHandler.instance().getMinecraftServerInstance().worlds) {
            boolean hasActiveCloudsInDimension = activeSmokeEffects.values().stream()
                    .anyMatch(data -> data.dimension == world.provider.getDimension());
            if (!hasActiveCloudsInDimension) {
                continue;
            }
            for (EntityPlayer player : world.playerEntities) {
                for (SmokeCloudData cloud : activeSmokeEffects.values()) {
                    if (cloud.dimension != world.provider.getDimension()) {
                        continue;
                    }
                    double dx = player.posX - cloud.x;
                    double dy = player.posY - cloud.y;
                    double dz = player.posZ - cloud.z;
                    double distanceSquared = dx * dx + dy * dy + dz * dz;
                    if (distanceSquared <= cloud.radius * cloud.radius) {
                        int duration = 40; // 2 secondes (40 ticks)
                        int amplifier = (distanceSquared < (cloud.radius * cloud.radius) / 4) ? 1 : 0; // Plus fort au centre

                        if (player instanceof EntityPlayerMP) {
                            (player).addPotionEffect(new PotionEffect(MobEffects.BLINDNESS, duration, amplifier, false, true));
                        }
                        break;
                    }
                }
            }
        }
    }

    /**
     * Crée un nuage de fumée hyper-dense avec stratégie de superposition en couches
     * Amélioration: animation plus organique avec mouvement turbulent
     */
    @SideOnly(Side.CLIENT)
    private void createSmokeCloud(World world, double x, double y, double z, int color, float radius, int duration, int density) {
        float r = ((color >> 16) & 0xFF) / 255.0F;
        float g = ((color >> 8) & 0xFF) / 255.0F;
        float b = (color & 0xFF) / 255.0F;

        final Random rand = new Random();

        // Couche centrale dense (inchangée)
        spawnDenseCoreSmokeLayer(world, x, y, z, r, g, b, radius * 0.8f, density/4, rand);

        // Couche externe avec turbulence (pour un effet plus organique)
        spawnTurbulentSmokeLayer(world, x, y, z, r, g, b, radius, density/4, rand);

        // Couches animées pour les deux derniers stades avec mouvement plus réaliste
        final int numWaves = 10;
        for (int wave = 1; wave < numWaves; wave++) {
            // Les dernières vagues (stades) avec plus de mouvement
            if (wave >= numWaves - 2) {
                spawnAdvancedSmokeLayer(
                        world, x, y + (wave * 0.05), z,
                        r, g, b,
                        radius * (0.4f + wave * 0.06f),
                        density / 3,
                        rand,
                        wave >= numWaves - 1 // Dernière couche avec mouvement maximal
                );
            } else {
                // Les autres couches utilisent le comportement existant
                spawnDenseCoreSmokeLayer(
                        world, x, y + (wave * 0.05), z,
                        r, g, b,
                        radius * (0.4f + wave * 0.06f),
                        density / 3,
                        rand
                );
            }
        }
    }

    /**
     * Génère une couche de fumée très dense avec superposition stratégique
     * Méthode originale conservée pour la compatibilité
     */
    @SideOnly(Side.CLIENT)
    private void spawnDenseCoreSmokeLayer(World world, double x, double y, double z,
                                          float r, float g, float b,
                                          float layerRadius, int particleCount, Random rand) {
        for (int layer = 0; layer < 3; layer++) {
            double layerOffset = layer * 0.2 - 0.2;
            for (int i = 0; i < particleCount/3; i++) {
                double phi = rand.nextDouble() * 2 * Math.PI;
                double theta = Math.acos(2 * rand.nextDouble() - 1);
                double rho = layerRadius * Math.pow(rand.nextDouble(), 1/2.5);
                double offsetX = rho * Math.sin(theta) * Math.cos(phi);
                double offsetY = rho * Math.cos(theta) + layerOffset;
                double offsetZ = rho * Math.sin(theta) * Math.sin(phi);
                double speedX = offsetX * 0.0002;
                double speedY = 0.002 + rand.nextDouble() * 0.003;
                double speedZ = offsetZ * 0.0002;
                ParticleSmoke smokeParticle = new ParticleSmoke(world,
                        x + offsetX, y + offsetY, z + offsetZ,
                        speedX, speedY, speedZ);
                smokeParticle.setRBGColorF(
                        Math.min(1.0f, r * 1.2f),
                        g,
                        b
                );
                float scale = 0.9f + rand.nextFloat() * 0.2f;
                smokeParticle.multipleParticleScaleBy(scale);
                Minecraft.getMinecraft().effectRenderer.addEffect(smokeParticle);
            }
        }
    }

    /**
     * Nouvelle méthode pour générer une couche de fumée avec turbulence
     * Ajoute des mouvements plus aléatoires pour un effet plus organique
     */
    @SideOnly(Side.CLIENT)
    private void spawnTurbulentSmokeLayer(World world, double x, double y, double z,
                                          float r, float g, float b,
                                          float layerRadius, int particleCount, Random rand) {
        for (int layer = 0; layer < 3; layer++) {
            double layerOffset = layer * 0.2 - 0.2;
            for (int i = 0; i < particleCount/3; i++) {
                double phi = rand.nextDouble() * 2 * Math.PI;
                double theta = Math.acos(2 * rand.nextDouble() - 1);
                double rho = layerRadius * Math.pow(rand.nextDouble(), 1/2.5);
                double offsetX = rho * Math.sin(theta) * Math.cos(phi);
                double offsetY = rho * Math.cos(theta) + layerOffset;
                double offsetZ = rho * Math.sin(theta) * Math.sin(phi);

                // Vitesse avec légère turbulence pour un mouvement plus naturel
                double speedX = offsetX * 0.0002 + (rand.nextDouble() - 0.5) * TURBULENCE_FACTOR;
                double speedY = 0.002 + rand.nextDouble() * 0.003 + (rand.nextDouble() - 0.3) * RISE_SPEED_VARIANCE;
                double speedZ = offsetZ * 0.0002 + (rand.nextDouble() - 0.5) * TURBULENCE_FACTOR;

                ParticleSmoke smokeParticle = new ParticleSmoke(world,
                        x + offsetX, y + offsetY, z + offsetZ,
                        speedX, speedY, speedZ);
                smokeParticle.setRBGColorF(
                        Math.min(1.0f, r * 1.2f),
                        g * (0.9f + rand.nextFloat() * 0.2f), // Légère variation de couleur
                        b * (0.9f + rand.nextFloat() * 0.2f)  // pour plus de réalisme
                );

                // Variation de taille pour plus de diversité
                float scale = 0.85f + rand.nextFloat() * 0.3f;
                smokeParticle.multipleParticleScaleBy(scale);
                Minecraft.getMinecraft().effectRenderer.addEffect(smokeParticle);
            }
        }
    }

    /**
     * Méthode avancée pour les derniers stades de la fumée
     * Ajoute un mouvement tourbillonnant et plus organique
     */
    @SideOnly(Side.CLIENT)
    private void spawnAdvancedSmokeLayer(World world, double x, double y, double z,
                                         float r, float g, float b,
                                         float layerRadius, int particleCount, Random rand,
                                         boolean isLastLayer) {
        // Angle de base pour créer un léger effet de rotation
        double baseRotationAngle = rand.nextDouble() * Math.PI;

        for (int layer = 0; layer < 3; layer++) {
            double layerOffset = layer * 0.25 - 0.2; // Espacement vertical légèrement plus grand
            for (int i = 0; i < particleCount/3; i++) {
                // Distribution sphérique standard
                double phi = rand.nextDouble() * 2 * Math.PI;
                double theta = Math.acos(2 * rand.nextDouble() - 1);
                double rho = layerRadius * Math.pow(rand.nextDouble(), 1/2.2); // Distribution légèrement modifiée

                // Positions de base
                double offsetX = rho * Math.sin(theta) * Math.cos(phi);
                double offsetY = rho * Math.cos(theta) + layerOffset;
                double offsetZ = rho * Math.sin(theta) * Math.sin(phi);

                // Distance depuis le centre (horizontalement)
                double distFromCenter = Math.sqrt(offsetX * offsetX + offsetZ * offsetZ);

                // Angles pour calculer des mouvements circulaires
                double angle = Math.atan2(offsetZ, offsetX) + baseRotationAngle;

                // Calcul de vitesse avec composante tourbillonnante
                // Plus la particule est loin du centre, plus le mouvement de rotation est prononcé
                double vortexFactor = isLastLayer ? VORTEX_STRENGTH * 1.5 : VORTEX_STRENGTH;
                double speedX = offsetX * 0.0002
                        + (rand.nextDouble() - 0.5) * TURBULENCE_FACTOR * 1.5
                        - Math.sin(angle) * distFromCenter * vortexFactor;

                double speedY = 0.002 + rand.nextDouble() * 0.004
                        + (rand.nextDouble() - 0.3) * RISE_SPEED_VARIANCE * 2.0;

                double speedZ = offsetZ * 0.0002
                        + (rand.nextDouble() - 0.5) * TURBULENCE_FACTOR * 1.5
                        + Math.cos(angle) * distFromCenter * vortexFactor;

                // Pour le dernier stade, on ajoute plus de variabilité et des mouvements plus complexes
                if (isLastLayer) {
                    // Variation sinusoïdale en fonction de la position pour les particules externes
                    if (distFromCenter > layerRadius * 0.6) {
                        double waveEffect = Math.sin(angle * 3) * 0.005;
                        speedX += waveEffect;
                        speedZ += waveEffect;

                        // Mouvement ascendant plus prononcé pour les particules externes
                        if (rand.nextDouble() > 0.5) {
                            speedY += rand.nextDouble() * 0.003;
                        }
                    }
                }

                ParticleSmoke smokeParticle = new ParticleSmoke(world,
                        x + offsetX, y + offsetY, z + offsetZ,
                        speedX, speedY, speedZ);

                // Variation de couleur subtile pour plus de réalisme
                float colorVariation = 0.9f + rand.nextFloat() * 0.2f;
                smokeParticle.setRBGColorF(
                        Math.min(1.0f, r * 1.2f * colorVariation),
                        g * colorVariation,
                        b * colorVariation
                );

                // Taille variable selon distance et position
                float scale = 0.8f + rand.nextFloat() * 0.4f;

                // Les particules plus hautes et périphériques sont plus grandes
                if (offsetY > 0 && distFromCenter > layerRadius * 0.6) {
                    scale *= 1.15f;
                }

                smokeParticle.multipleParticleScaleBy(scale);
                Minecraft.getMinecraft().effectRenderer.addEffect(smokeParticle);
            }
        }
    }

    protected ItemStack getArrowStack() {
        return new ItemStack(ModWeapons.SMOKE_BOMB);
    }

    /**
     * Classe pour stocker les données d'un nuage de fumée actif
     */
    private static class SmokeCloudData {
        final double x, y, z;
        final float radius;
        final int dimension;
        final long expirationTime;

        SmokeCloudData(double x, double y, double z, float radius, int dimension, long expirationTime) {
            this.x = x;
            this.y = y;
            this.z = z;
            this.radius = radius;
            this.dimension = dimension;
            this.expirationTime = expirationTime;
        }
    }
}