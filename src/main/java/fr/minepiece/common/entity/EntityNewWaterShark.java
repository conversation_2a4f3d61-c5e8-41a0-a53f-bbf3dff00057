package fr.minepiece.common.entity;

import fr.minepiece.common.init.ModConfiguration;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.DamageSource;
import net.minecraft.util.EnumParticleTypes;
import net.minecraft.util.math.RayTraceResult;
import net.minecraft.world.World;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;

public class EntityNewWaterShark extends CustomFireBall implements IAnimatable {

    protected static final AnimationBuilder FLY_ANIM = new AnimationBuilder().addAnimation("Attaque", true);

    private AnimationFactory factory = new AnimationFactory(this);

    private double originalYaw;
    private double originalPitch;


    protected float damage = ModConfiguration.fishman_watershark_damage;

    public EntityNewWaterShark(World worldIn) {
        super(worldIn);
        this.setSize(4f, 2f);
    }

    public EntityNewWaterShark(World worldIn, EntityLivingBase shooter, double accelX, double accelY, double accelZ, double outGoingYaw) {
        super(worldIn, shooter, accelX, accelY, accelZ);
        this.setLocationAndAngles(shooter.posX, shooter.posY, shooter.posZ, (float) (shooter.rotationYaw - outGoingYaw), shooter.rotationPitch);
        this.setPosition(this.posX, this.posY, this.posZ);
        this.originalPitch = shooter.rotationPitch;
        this.originalYaw = shooter.rotationYaw - outGoingYaw;
        this.setSize(3.25f, 2f);
    }

    public EntityNewWaterShark(World worldIn, double x, double y, double z, double accelX, double accelY, double accelZ) {
        super(worldIn, x, y, z, accelX, accelY, accelZ);
        this.setSize(4f, 2f);
    }

    public void onUpdate() {
        if (ticksExisted == 1) {
            this.originalPitch = this.rotationPitch;
            this.originalYaw = this.rotationYaw;
        }
        if (ticksExisted > 60) this.setDead();
        super.onUpdate();
        this.rotationYaw = (float) originalYaw;
        this.rotationPitch = (float) originalPitch;
    }

    @Override
    protected void onImpact(RayTraceResult result) {
        if (!this.world.isRemote) {
            if (result.entityHit != null) {
                if (result.entityHit instanceof EntityWaterDrop) return;
                if (!(shootingEntity instanceof EntityPlayer))
                    throw new IllegalStateException("Water sharks must be shot by players !");
                boolean successfulHit = result.entityHit.attackEntityFrom(DamageSource.causePlayerDamage((EntityPlayer) shootingEntity), this.damage);

                if (successfulHit) {
                    this.applyEnchantments(this.shootingEntity, result.entityHit);
                    this.setDead();
                }

            } else if (result.typeOfHit == RayTraceResult.Type.BLOCK) {
                this.setDead();
            }

        }
    }

    @Override
    protected boolean isFireballFiery() {
        return false;
    }

    protected EnumParticleTypes getParticleType() {
        return EnumParticleTypes.WATER_DROP;
    }


    @Override
    public AnimationFactory getFactory() {
        return this.factory;
    }

    @Override
    protected ParticleFunction getParticleFunction() {
        return ParticleFunction.MIDDLE;
    }


    protected float getMotionFactor()
    {
        return 0.85F;
    }

    private <E extends IAnimatable> PlayState flyAnimController(AnimationEvent<E> event) {
        event.getController().setAnimation(FLY_ANIM);
        return PlayState.CONTINUE;
    }

    @Override
    public void registerControllers(AnimationData data) {
        data.addAnimationController(
                new AnimationController<EntityNewWaterShark>(this, "controller", 1, this::flyAnimController));
    }
}
