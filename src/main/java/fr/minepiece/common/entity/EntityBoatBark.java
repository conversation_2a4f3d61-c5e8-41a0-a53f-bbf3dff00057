package fr.minepiece.common.entity;

import fr.minepiece.common.init.ModItems;
import net.minecraft.item.Item;
import net.minecraft.world.World;

public class EntityBoatBark extends AbstractBoat {

    public EntityBoatBark(World worldIn) {
        super(worldIn);
        setSize(3F, 1.2F);
    }

    public EntityBoatBark(World worldIn, double x, double y, double z) {
        super(worldIn, x, y, z);
        setSize(3F, 1.2F);
    }

    @Override
    public Item getItemBoat() {
        return ModItems.BARQUE;
    }

    @Override
    protected void initSeatPositions() {
        SEAT_POSITIONS = new double[][]{
                {-2.3D, 0.0D, 0.5D},  // Arrière droite gauche - conducteur principal
                {-2.3D, 0.0D, -0.5D}, // Arrière gauche - conducteur alternatif
                {0.5D, 0.0D, 0.5D}, // Avant droite
                {0.5D, 0.0D, -0.5D} // Avant gauche
        };
    }

    @Override
    public float getSpeedMultiplier() {
        return 1.3f;
    }
}
