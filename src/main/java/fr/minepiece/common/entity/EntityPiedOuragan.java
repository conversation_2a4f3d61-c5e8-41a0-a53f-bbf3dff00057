package fr.minepiece.common.entity;

import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.DamageSource;
import net.minecraft.util.EnumParticleTypes;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.RayTraceResult;
import net.minecraft.world.World;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;

public class EntityPiedOuragan extends CustomFireBall implements IAnimatable {

    protected static final AnimationBuilder FLY_ANIM = new AnimationBuilder().addAnimation("animation.model.piedouragan", true);

    private AnimationFactory factory = new AnimationFactory(this);

    protected float damage = 6f;

    private double originalYaw;
    private double originalPitch;

    public EntityPiedOuragan(World worldIn) {
        super(worldIn);
    }

    public EntityPiedOuragan(World worldIn, EntityLivingBase shooter, double accelX, double accelY, double accelZ, double outGoingYaw) {
        super(worldIn, shooter, accelX, accelY, accelZ);
        this.setLocationAndAngles(shooter.posX, shooter.posY, shooter.posZ, (float) (shooter.rotationYaw - outGoingYaw), shooter.rotationPitch);
        this.setPosition(this.posX, this.posY, this.posZ);
        this.originalPitch = shooter.rotationPitch;
        this.originalYaw = shooter.rotationYaw - outGoingYaw;
        this.setSize(10f, 2f);
    }

    public EntityPiedOuragan(World worldIn, double x, double y, double z, double accelX, double accelY, double accelZ) {
        super(worldIn, x, y, z, accelX, accelY, accelZ);
        this.setSize(10f, 2f);
    }

    public void onUpdate() {
        if (ticksExisted == 1) {
            this.originalPitch = this.rotationPitch;
            this.originalYaw = this.rotationYaw;
        }
        if (ticksExisted > 60) this.setDead();
        super.onUpdate();
        this.rotationYaw = (float) originalYaw;
        this.rotationPitch = (float) originalPitch;
        this.setSize(4f, 0.5f);
    }

    @Override
    protected ParticleFunction getParticleFunction() {
        return ParticleFunction.LEFT_MIDDLE_RIGHT;
    }

    @Override
    protected void applyEnchantments(EntityLivingBase entityLivingBaseIn, Entity entityIn) {
        super.applyEnchantments(entityLivingBaseIn, entityIn);
        if (entityIn instanceof EntityLivingBase) {

            ((EntityLivingBase) entityIn).knockBack(this, 1f, MathHelper.sin(this.rotationYaw * 0.017453292F), -MathHelper.cos(this.rotationYaw * 0.017453292F));
//            else
//            {
//                EntityLivingBase ent = (EntityLivingBase) entityIn;
//                if(ent.getActivePotionEffect(Potion.getPotionFromResourceLocation("slowness")) != null && ent.getActivePotionEffect(Potion.getPotionFromResourceLocation("slowness")).getAmplifier() < 2)
//                {
//                    ent.addPotionEffect(new PotionEffect(Potion.getPotionFromResourceLocation("slowness"), 60, 2));
//                }
//            }
        }
    }


    @Override
    protected boolean isFireballFiery() {
        return false;
    }

    protected float getMotionFactor() {
        return 0.85f;
    }

    protected EnumParticleTypes getParticleType() {
        return EnumParticleTypes.CLOUD;
    }

    @Override
    protected void onImpact(RayTraceResult result) {
        if (!this.world.isRemote) {
            if (result.entityHit != null) {
                if (result.entityHit instanceof EntityPiedOuragan) return;
                if (!(shootingEntity instanceof EntityPlayer))
                    throw new IllegalStateException("Pied ouragans must be shot by players !");
                boolean successfulHit = result.entityHit.attackEntityFrom(DamageSource.causePlayerDamage((EntityPlayer) shootingEntity), this.damage);

                if (successfulHit) {
                    this.applyEnchantments(this.shootingEntity, result.entityHit);
                    //this.setDead();
                }

            } else if (result.typeOfHit == RayTraceResult.Type.BLOCK) {
                this.setDead();
            }

        }
    }

    private <E extends IAnimatable> PlayState flyAnimController(AnimationEvent<E> event) {
        event.getController().setAnimation(FLY_ANIM);
        return PlayState.CONTINUE;
    }

    @Override
    public void registerControllers(AnimationData data) {
        data.addAnimationController(
                new AnimationController<>(this, "controller", 1, this::flyAnimController));
    }

    @Override
    public AnimationFactory getFactory() {
        return this.factory;
    }
}
