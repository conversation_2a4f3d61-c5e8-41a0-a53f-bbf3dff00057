package fr.minepiece.common.entity;

import net.minecraft.entity.Entity;
import net.minecraft.entity.item.EntityBoat;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.EnumHand;
import net.minecraft.util.math.AxisAlignedBB;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;
import net.minecraftforge.fml.common.ObfuscationReflectionHelper;

import java.lang.reflect.Field;
import java.util.Collections;
import java.util.List;

public abstract class AbstractBoat extends EntityBoat {

    private static Field riddenByEntitiesField;
    private static Field ridingEntityField;

    protected static double[][] SEAT_POSITIONS;

    static {
        try {
            // The field name might be obfuscated in some environments. In a development environment, "riddenByEntities" works.
            riddenByEntitiesField = ObfuscationReflectionHelper.findField(Entity.class, "field_184244_h");
            riddenByEntitiesField.setAccessible(true);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            ridingEntityField = ObfuscationReflectionHelper.findField(Entity.class,"field_184239_as");
            ridingEntityField.setAccessible(true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public AbstractBoat(World worldIn) {
        super(worldIn);
        initSeatPositions();
    }

    public AbstractBoat(World worldIn, double x, double y, double z) {
        super(worldIn, x, y, z);
        initSeatPositions();
    }

    protected abstract void initSeatPositions();

    @Override
    public void updatePassenger(Entity passenger) {
        super.updatePassenger(passenger);
        if (this.isPassenger(passenger)) {
            int index = this.getPassengers().indexOf(passenger);
            if (index >= 0 && index < SEAT_POSITIONS.length) {
                double[] pos = SEAT_POSITIONS[index];

                float f = 0.0F;
                float f1 = (float)((this.isDead ? 0.01D : this.getMountedYOffset()) + passenger.getYOffset());

                Vec3d vec3d = new Vec3d(pos[0], pos[1] + f1, pos[2]).rotateYaw(-this.rotationYaw * 0.017453292F - ((float)Math.PI / 2F));
                passenger.setPosition(this.posX + vec3d.x, this.posY + vec3d.y, this.posZ + vec3d.z);
            }
        }
    }

    @Override
    public boolean processInitialInteract(EntityPlayer player, EnumHand hand) {
        if (getActualPassengers().size() == 0) {
            return super.processInitialInteract(player, hand);
        } else {
            if (player.isSneaking()) {
                return false;
            } else {
                if (!this.world.isRemote) {
                    List<Entity> passengers = this.getPassengers();
                    // Vérifier si le joueur est déjà un passager
                    if (passengers.contains(player)) {
                        return true;
                    }

                    // Vérifier la disponibilité des places
                    if (passengers.size() < SEAT_POSITIONS.length) {
                        this.addPassenger(player);
                        //player.startRiding(this);
                        return true;
                    }
                }

                return true;
            }
        }


    }

//    @Override
//    public boolean canPassengerSteer() {
//        Entity entity = this.getControllingPassenger();
//        return entity instanceof EntityPlayer;
//    }

//    @Nullable
//    @Override
//    public Entity getControllingPassenger() {
//        List<Entity> list = this.getPassengers();
//
//        // Si la place avant gauche (conducteur principal) est occupée
//        if (!list.isEmpty() && list.get(0) instanceof EntityLivingBase) {
//            return list.get(0);
//        }
//        // Si la place avant droite (conducteur alternatif) est occupée
//        else if (list.size() > 1 && list.get(1) instanceof EntityLivingBase) {
//            return list.get(1);
//        }
//
//        return null;
//    }

    @Override
    public void addPassenger(Entity passenger) {
        List<Entity> riders = getActualPassengers();
        if (riders.size() < SEAT_POSITIONS.length && !riders.contains(passenger)) {
            riders.add(passenger);
            setRidingEntity(passenger, this);
            updatePassenger(passenger);
        }
    }

    @Override
    public void onUpdate() {
//        if (this.world.getTotalWorldTime() % 2 == 0)
//            updateAABB();
        super.onUpdate();
    }

    /**
     * Gets the actual underlying list of passengers using reflection.
     */
    @SuppressWarnings("unchecked")
    private List<Entity> getActualPassengers() {
        try {
            return (List<Entity>) riddenByEntitiesField.get(this);
        } catch (Exception e) {
            e.printStackTrace();
        }
        // Fallback to an empty list if reflection fails.
        return Collections.emptyList();
    }

    /**
     * Sets the private ridingEntity field for a given passenger.
     */
    private void setRidingEntity(Entity passenger, Entity riding) {
        try {
            ridingEntityField.set(passenger, riding);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public AxisAlignedBB getCollisionBox(Entity entityIn) {
        return super.getCollisionBox(entityIn);
    }

    @Override
    public AxisAlignedBB getCollisionBoundingBox() {
        return super.getCollisionBoundingBox();
    }

    private void updateAABB() {
        double halfLength = 5 / 2.0; // 3.25
        double halfWidth = 2 / 2.0;  // 1.25
        double halfHeight = 0.6 / 2.0; // Changed from 5.0 - too tall

        // Convert yaw to radians (Minecraft yaw is in degrees)
        float yawRad = this.rotationYaw * (float)Math.PI / 180.0F;

        // Calculate the horizontal extents based on the yaw.
        double xExtent = Math.abs(halfLength * Math.sin(yawRad)) + Math.abs(halfWidth * Math.cos(yawRad));
        double zExtent = Math.abs(halfLength * Math.cos(yawRad)) + Math.abs(halfWidth * Math.sin(yawRad));
        this.setEntityBoundingBox(new AxisAlignedBB(
                this.posX -xExtent, // -0.75
                this.posY, //0
                this.posZ-zExtent, // -0.75
                this.posX + xExtent, // 0.75
                this.posY + 1.2, // +0.6
                this.posZ + zExtent // 0.75
        ));
    }

    @Override
    protected void applyYawToEntity(Entity entityToUpdate) {
        super.applyYawToEntity(entityToUpdate);
        updateAABB();
    }

    @Override
    public double getMountedYOffset() {
        return 0.4D;
    }

    public abstract float getSpeedMultiplier();
}
