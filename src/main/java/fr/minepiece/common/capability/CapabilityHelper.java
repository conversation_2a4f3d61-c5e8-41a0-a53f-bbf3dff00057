package fr.minepiece.common.capability;

import fr.minepiece.common.capability.fruits.FruitUserProvider;
import fr.minepiece.common.capability.fruits.IFruitUser;
import fr.minepiece.common.capability.knockout.IKOState;
import fr.minepiece.common.capability.knockout.KOStateStorage;
import fr.minepiece.common.capability.mpdata.IMinePieceData;
import fr.minepiece.common.capability.mpdata.MinePieceDataStorage;
import fr.minepiece.common.capability.mpstats.IMinePieceStats;
import fr.minepiece.common.capability.mpstats.MinePieceStatsStorage;
import fr.minepiece.common.capability.playermodifications.IPlayerModifications;
import fr.minepiece.common.capability.playermodifications.PlayerModificationsStorage;
import net.minecraft.entity.player.EntityPlayer;

public final class CapabilityHelper {

    public static IPlayerModifications getPlayerModifications(EntityPlayer player) {
        IPlayerModifications playerModifications = player.getCapability(PlayerModificationsStorage.PLAYER_MODIFICATIONS_CAPABILITY, null);
        assert playerModifications != null;
        return playerModifications;
    }

    public static IMinePieceData getPlayerData(EntityPlayer player) {
        IMinePieceData playerData = player.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);
        assert playerData != null;
        return playerData;
    }

    public static IMinePieceStats getPlayerStats(EntityPlayer player) {
        IMinePieceStats playerStats = player.getCapability(MinePieceStatsStorage.MP_STATS_CAPABILITY, null);
        assert playerStats != null;
        return playerStats;
    }

    public static IKOState getPlayerKOState(EntityPlayer player) {
        IKOState koState = player.getCapability(KOStateStorage.KO_STATE_CAPABILITY, null);
        assert koState != null;
        return koState;
    }

    public static IFruitUser getPlayerFruitUser(EntityPlayer player) {
        IFruitUser fruitUser = player.getCapability(FruitUserProvider.FRUIT_USER_CAPABILITY, null);
        assert fruitUser != null;
        return fruitUser;
    }
}
