package fr.minepiece.common.capability.temperature;

import net.minecraft.nbt.NBTTagCompound;
import net.minecraft.util.EnumFacing;
import net.minecraftforge.common.capabilities.Capability;
import net.minecraftforge.common.capabilities.CapabilityInject;
import net.minecraftforge.common.capabilities.ICapabilitySerializable;

import javax.annotation.Nullable;

public class TemperatureProvider implements ICapabilitySerializable<NBTTagCompound> {

    @CapabilityInject(ITemperature.class)
    public static final Capability<ITemperature> TEMPERATURE_CAPABILITY = null;

    private ITemperature instance = null;

    public TemperatureProvider() {
        this.instance = new DefaultTemperature();
    }

    @Override
    public boolean hasCapability(Capability<?> capability, @Nullable EnumFacing facing) {
        return capability == TEMPERATURE_CAPABILITY;
    }

    @Override
    @Nullable
    public <T> T getCapability(Capability<T> capability, @Nullable EnumFacing facing) {
        return capability == TEMPERATURE_CAPABILITY ? TEMPERATURE_CAPABILITY.cast(this.instance) : null;
    }

    @Override
    public NBTTagCompound serializeNBT() {
        NBTTagCompound compound = new NBTTagCompound();
        if (TEMPERATURE_CAPABILITY != null) {
            compound = (NBTTagCompound) TEMPERATURE_CAPABILITY.getStorage()
                    .writeNBT(TEMPERATURE_CAPABILITY, this.instance, null);
        }
        return compound;
    }

    @Override
    public void deserializeNBT(NBTTagCompound nbt) {
        if (TEMPERATURE_CAPABILITY != null) {
            TEMPERATURE_CAPABILITY.getStorage()
                    .readNBT(TEMPERATURE_CAPABILITY, this.instance, null, nbt);
        }
    }
}