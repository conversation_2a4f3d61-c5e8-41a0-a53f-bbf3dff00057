package fr.minepiece.common.capability.temperature;

import fr.minepiece.common.init.ModPotions;
import fr.minepiece.common.utils.BiomeTemperatures;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.nbt.NBTTagCompound;
import net.minecraft.potion.PotionEffect;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.TextFormatting;

public class DefaultTemperature implements ITemperature {
    private static final String NBT_TEMPERATURE = "Temperature";
    private static final String NBT_BASE_TEMPERATURE = "BaseTemperature";

    // Constantes pour le système de température
    private static final double MIN_TEMPERATURE = -10.0;
    private static final double MAX_TEMPERATURE = 45.0;

    // PARAMÈTRES MODIFIABLES
    private static int UPDATE_INTERVAL = 40 * 20; // 40 secondes
    private static double DEFAULT_TEMP_CHANGE = 0.1; // Changement de température par défaut

    private static final double COLD_THRESHOLD = 8.0; // En dessous de cette température environnementale, c'est "froid"
    private static final double HEAT_THRESHOLD = 32.0; // Au-dessus de cette température environnementale, c'est "chaud"

    private double temperature;
    private double baseTemperature;
    private long lastUpdate;

    public DefaultTemperature() {
        // La température sera initialisée correctement lors de l'attachement au joueur
        this.baseTemperature = 37.0;
        this.temperature = this.baseTemperature;
        this.lastUpdate = 0;
    }

    // Méthodes pour modifier les paramètres
    public static void setUpdateInterval(int ticks) {
        UPDATE_INTERVAL = ticks;
    }

    public static int getUpdateInterval() {
        return UPDATE_INTERVAL;
    }

    public static void setDefaultTempChange(double change) {
        DEFAULT_TEMP_CHANGE = change;
    }

    public static double getDefaultTempChange() {
        return DEFAULT_TEMP_CHANGE;
    }

    @Override
    public double getTemperature() {
        return this.temperature;
    }

    @Override
    public void setTemperature(double temperature) {
        this.temperature = temperature;
    }

    @Override
    public double getBaseTemperature() {
        return this.baseTemperature;
    }

    @Override
    public void setBaseTemperature(double baseTemperature) {
        this.baseTemperature = baseTemperature;
    }

    @Override
    public TemperatureState getCurrentState() {
        return TemperatureState.getStateFromTemperature(this.temperature);
    }

    @Override
    public void applyEffects(EntityPlayer player) {
        StringBuilder message = new StringBuilder();
        TemperatureState state = getCurrentState();
        Double temp = getTemperature();

        // Clear previous effects
        player.removePotionEffect(ModPotions.TEMPERATURE_EFFECT);

        // Apply new effects based on state
        switch (state) {
            case HYPOTHERMIA_MILD:
            case HYPERTHERMIA_MILD:
                player.addPotionEffect(new PotionEffect(ModPotions.TEMPERATURE_EFFECT, 220, 0));
                message.append(TextFormatting.GREEN).append("Vous ressentez les effets de la température, ").append(temp).append("°C");
                break;
            case HYPOTHERMIA_MODERATE:
            case HYPERTHERMIA_MODERATE:
                player.addPotionEffect(new PotionEffect(ModPotions.TEMPERATURE_EFFECT, 220, 1));
                message.append(TextFormatting.GREEN).append("Vous ressentez encore plus les effets de la température, attention ! ").append(temp).append("°C");
                break;
            case HYPOTHERMIA_SEVERE:
            case HYPERTHERMIA_SEVERE:
                player.addPotionEffect(new PotionEffect(ModPotions.TEMPERATURE_EFFECT, 220, 2));
                message.append(TextFormatting.GREEN).append("La température commence à devenir critique, vous êtes en danger, ").append(temp).append("°C");
                break;
            case HYPOTHERMIA_DEATH:
            case HYPERTHERMIA_DEATH:
                player.addPotionEffect(new PotionEffect(ModPotions.TEMPERATURE_EFFECT, 220, 3));
                message.append(TextFormatting.GREEN).append("C'est la fin des haricots, ").append(temp).append("°C");
                break;
        }
    }

    @Override
    public void updateTemperature(EntityPlayer player) {
        if (player.isCreative()) {
            this.temperature = this.baseTemperature;
            return;
        }

        long currentTime = player.world.getTotalWorldTime();

        if (currentTime - lastUpdate < UPDATE_INTERVAL) {
            return;
        }
        lastUpdate = currentTime;

        BlockPos playerPos = player.getPosition();
        double environmentTemp = BiomeTemperatures.getFinalEnvironmentTemperature(
                player.world,
                playerPos,
                player.world.getWorldTime()
        );

        // Base temperature change
        double tempChange = 0.0;

        // Vérification si le joueur tient une torche
        boolean holdingTorch = BiomeTemperatures.isHoldingTorch(player);

        if (environmentTemp < COLD_THRESHOLD) {
            // Environnement froid
            tempChange = -DEFAULT_TEMP_CHANGE;

            // Si le joueur tient une torche et est en hypothermie, on aide à remonter la température
            if (holdingTorch && temperature < baseTemperature) {
                tempChange = DEFAULT_TEMP_CHANGE;
            } else {
                // Applique la résistance raciale au froid
                double raceModifier = RaceTemperatureModifiers.getTemperatureChangeRate(
                        player, environmentTemp, temperature);
                tempChange *= raceModifier;

                // Applique l'isolation de l'armure pour le froid
                tempChange *= BiomeTemperatures.getArmorInsulation(player);
            }

        } else if (environmentTemp > HEAT_THRESHOLD) {
            // Environnement chaud
            tempChange = DEFAULT_TEMP_CHANGE;

            // Applique la résistance raciale à la chaleur
            double raceModifier = RaceTemperatureModifiers.getTemperatureChangeRate(
                    player, environmentTemp, temperature);
            tempChange *= raceModifier;

            // L'armure augmente l'effet de la chaleur
            tempChange *= (2.0 - BiomeTemperatures.getArmorInsulation(player));

        } else if (Math.abs(temperature - baseTemperature) > 0.1) {
            // Retour progressif à la température de base
            tempChange = (temperature < baseTemperature) ? DEFAULT_TEMP_CHANGE : -DEFAULT_TEMP_CHANGE;

            // Si le joueur tient une torche et que sa température va descendre, on empêche cela
            if (holdingTorch && tempChange < 0) {
                tempChange = 0;
            }
        }

        // Applique le multiplicateur d'eau si nécessaire
        double waterMultiplier = BiomeTemperatures.getWaterEffect(player.world, playerPos);
        if (tempChange < 0) {
            tempChange *= waterMultiplier;
        }

        temperature += tempChange;

        // Ensure temperature stays within reasonable bounds
        temperature = Math.max(Math.min(temperature, MAX_TEMPERATURE), MIN_TEMPERATURE);
    }

    @Override
    public void saveNBTData(NBTTagCompound compound) {
        compound.setDouble(NBT_TEMPERATURE, temperature);
        compound.setDouble(NBT_BASE_TEMPERATURE, baseTemperature);
    }

    @Override
    public void loadNBTData(NBTTagCompound compound) {
        temperature = compound.getDouble(NBT_TEMPERATURE);
        baseTemperature = compound.getDouble(NBT_BASE_TEMPERATURE);
    }

    @Override
    public void copy(ITemperature temperature) {
        this.temperature = temperature.getTemperature();
        this.baseTemperature = temperature.getBaseTemperature();
    }
}