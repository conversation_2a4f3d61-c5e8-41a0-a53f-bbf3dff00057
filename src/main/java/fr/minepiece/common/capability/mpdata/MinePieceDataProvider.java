/*
 * Copyright (c) 2021.  Zom'
 * https://twitter.com/ZOm__YT
 */

package fr.minepiece.common.capability.mpdata;

import net.minecraft.nbt.NBTBase;
import net.minecraft.util.EnumFacing;
import net.minecraftforge.common.capabilities.Capability;
import net.minecraftforge.common.capabilities.ICapabilitySerializable;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

public class MinePieceDataProvider implements ICapabilitySerializable<NBTBase> {

	protected IMinePieceData mpData;

	public MinePieceDataProvider() {
		this.mpData = new DefaultMinePieceData();
	}

	@Override
	public boolean hasCapability(@Nonnull Capability<?> capability, @Nullable EnumFacing facing) {
		return capability == MinePieceDataStorage.MP_DATA_CAPABILITY;
	}

	@Nullable
	@Override
	public <T> T getCapability(@Nonnull Capability<T> capability, @Nullable EnumFacing facing) {
		return this.hasCapability(capability, facing) ? MinePieceDataStorage.MP_DATA_CAPABILITY.cast(this.mpData) : null;
	}

	@Override
	public NBTBase serializeNBT() {
		return MinePieceDataStorage.MP_DATA_CAPABILITY.writeNBT(this.mpData, null);
	}

	@Override
	public void deserializeNBT(NBTBase nbt) {
		MinePieceDataStorage.MP_DATA_CAPABILITY.readNBT(this.mpData, null, nbt);
	}
}
