/*
 * Copyright (c) 2021.  Zom'
 * https://twitter.com/ZOm__YT
 */

package fr.minepiece.common.capability.mpdata;

import fr.minepiece.client.render.playerapi.poses.IPose;
import fr.minepiece.client.render.playerapi.poses.Poses;
import fr.minepiece.common.api.jobs.MinePieceJobs;
import fr.minepiece.common.api.races.MinePieceRaces;
import fr.minepiece.common.events.PlayerXPEvents;
import net.minecraft.entity.player.EntityPlayerMP;

import java.util.ArrayList;

public interface IMinePieceData {

	String getRPName();

	void setRPName(String name);

	String getRPLastName();

	void setRPLastName(String name);

	int getRPAge();

	void setRPAge(int age);

	int getLevel();

	void setLevel(int level);

	default void addLevel(int amount) {setLevel(getLevel() + amount);}

	int getXP();

	void setXP(int xp);

	default void addXP(int xp) {
		setXP(getXP() + xp);
	}

	// Race
	MinePieceRaces getRace();

	void setRace(MinePieceRaces race);

	void setRace(int raceID);

	int getRaceID();

	// Job

	MinePieceJobs getJob();

	void setJob(MinePieceJobs job);

	void setJob(int jobId);

	int getJobID();

	int getJobXP();

	void setJobXP(int xp);

	default void addJobXP(int xp) {
		setJobXP(getJobXP() + xp) ;
	}

	int getJobLevel();

	void setJobLevel(int jobLevel);

	default void addJobLevel(int amount) {setJobLevel(getJobLevel() + amount);}

	int getDailyCraftLimit() ;

	void setDailyCraftLimit(int dailyCraftLimit);

	// Skills

	ArrayList<String> getSkills();

	void addSkill(String skill);

	void setSkills(ArrayList<String> skills);

	// Other

	boolean isCyborgSkill2Activated();

	void toggleCyborgSkill2();

	void setCyborgSkill(boolean activated);

	boolean isGiantSkill1Activated();

	void toggleGiantSkill1();

	void setGiantSkill1(boolean activated);

	// sync

	void clientSync(EntityPlayerMP player);

}
