/*
 * Copyright (c) 2021.  Zom'
 * https://twitter.com/ZOm__YT
 */

package fr.minepiece.common.capability.mpdata;

import fr.minepiece.common.api.jobs.MinePieceJobs;
import fr.minepiece.common.api.races.MinePieceRaces;
import fr.minepiece.common.api.fruits.MinePieceFruits;
import fr.minepiece.common.network.ModPackets;
import fr.minepiece.common.network.packets.FullMPDataPacket;
import net.minecraft.entity.player.EntityPlayerMP;

import java.util.ArrayList;

public class DefaultMinePieceData implements IMinePieceData {

	protected String rpName, rpLastName;
	protected int level, rpAge, raceID, jobID, fruitID, dailyCraftLimit;
	protected MinePieceRaces race;
	protected MinePieceJobs job;
	protected MinePieceFruits fruit;
	private int jobXp, jobLevel, fruitXp, fruitLevel;
	private ArrayList<String> skills;

	// Powers Cooldowns
	protected long lastPower1Use, lastPower2Use;
	protected boolean cyborgSkill;
	protected boolean giantSkill1;
	private int xp;

	public DefaultMinePieceData() {
		this.rpName = "";
		this.rpLastName = "";
		this.level = this.rpAge = 1;
		this.raceID = -1;
		this.race = MinePieceRaces.HUMAN;
		this.job = MinePieceJobs.MEDIC;
		this.fruit = null;
		this.fruitID = -1;
		this.fruitLevel = 1;
		this.fruitXp = 0;
		this.dailyCraftLimit = 10;
		this.skills = new ArrayList<>();
		this.jobID = -1;
		this.jobLevel = 1;
		this.lastPower1Use = this.lastPower2Use = 0L;
		this.xp = 0;
	}

	@Override
	public String getRPName() {
		return rpName;
	}

	@Override
	public void setRPName(String name) {
		this.rpName = name;
	}

	@Override
	public String getRPLastName() {
		return rpLastName;
	}

	@Override
	public void setRPLastName(String name) {
		this.rpLastName = name;
	}

	@Override
	public int getRPAge() {
		return rpAge;
	}

	@Override
	public void setRPAge(int age) {
		this.rpAge = age;
	}

	@Override
	public int getLevel() {
		return level;
	}

	@Override
	public void setLevel(int level) {
		this.level = level;
	}

	@Override
	public int getXP() {
		return this.xp;
	}

	@Override
	public void setXP(int xp) {
		this.xp = xp;
	}

	@Override
	public MinePieceRaces getRace() {
		return race;
	}

	@Override
	public void setRace(MinePieceRaces race) {
		this.race = race;
		this.raceID = race.ordinal();
	}

	@Override
	public void setRace(int raceID) {
		this.raceID = raceID;
		this.race = raceID == -1 ? MinePieceRaces.HUMAN : MinePieceRaces.values()[raceID];
	}

	@Override
	public int getRaceID() {
		return raceID;
	}

	@Override
	public MinePieceJobs getJob() {
		return job;
	}

	@Override
	public void setJob(MinePieceJobs job) {
		this.job = job;
		this.jobID = job.ordinal();
		if (job != null) {
			this.dailyCraftLimit = job.getDailyCraftLimit();
		}
	}

	@Override
	public void setJob(int jobId) {
		this.jobID = jobId;
		this.job = jobId == -1 ? MinePieceJobs.MEDIC : MinePieceJobs.values()[jobId];
		if (this.job != null) {
			this.dailyCraftLimit = this.job.getDailyCraftLimit();
		}
	}

	@Override
	public int getJobID() {
		return jobID;
	}

	@Override
	public int getJobXP() {
		return jobXp;
	}

	@Override
	public void setJobXP(int jobXP) {
		this.jobXp = jobXP;
	}

	@Override
	public int getJobLevel() {
		return jobLevel;
	}

	@Override
	public void setJobLevel(int jobLevel) {
		this.jobLevel = jobLevel;
	}

	@Override
	public MinePieceFruits getFruit() {
		return fruit;
	}

	@Override
	public void setFruit(MinePieceFruits fruit) {
		this.fruit = fruit;
		this.fruitID = fruit == null ? -1 : fruit.ordinal();
	}

	@Override
	public void setFruit(int fruitId) {
		this.fruitID = fruitId;
		this.fruit = fruitId == -1 ? null : MinePieceFruits.values()[fruitId];
	}

	@Override
	public int getFruitID() {
		return fruitID;
	}

	@Override
	public int getFruitXP() {
		return fruitXp;
	}

	@Override
	public void setFruitXP(int fruitXP) {
		this.fruitXp = fruitXP;
	}

	@Override
	public int getFruitLevel() {
		return fruitLevel;
	}

	@Override
	public void setFruitLevel(int fruitLevel) {
		this.fruitLevel = fruitLevel;
	}

	public int getDailyCraftLimit() {
		return this.dailyCraftLimit;
	}

	public void setDailyCraftLimit(int dailyCraftLimit) {
		this.dailyCraftLimit = dailyCraftLimit;
	}

	@Override
	public ArrayList<String> getSkills() {
		return this.skills;
	}

	@Override
	public void addSkill(String skill) {
		this.skills.add(skill);
	}

	@Override
	public void setSkills(ArrayList<String> skills) {
		this.skills = skills;
	}

	@Override
	public boolean isCyborgSkill2Activated() {
		return this.cyborgSkill;
	}

	@Override
	public void toggleCyborgSkill2() {
		this.cyborgSkill = !this.cyborgSkill;
	}

	@Override
	public void setCyborgSkill(boolean activated) {
		this.cyborgSkill = activated;
	}

	@Override
	public boolean isGiantSkill1Activated() {
		return this.giantSkill1;
	}

	@Override
	public void toggleGiantSkill1() {
		this.giantSkill1 = !this.giantSkill1;
	}

	@Override
	public void setGiantSkill1(boolean activated) {
		this.giantSkill1 = activated;
	}

	@Override
	public void clientSync(EntityPlayerMP player) {
		ModPackets.NETWORK.sendToAll(new FullMPDataPacket(rpName, rpLastName, level, rpAge, raceID, xp, jobID, jobLevel, jobXp, fruitID, fruitLevel, fruitXp, skills, player.getUniqueID()));
	}

	public static void syncStats(IMinePieceData statsOrig, IMinePieceData statsNew) {
		statsNew.setRPName(statsOrig.getRPName());
		statsNew.setRPLastName(statsOrig.getRPLastName());
		statsNew.setRPAge(statsOrig.getRPAge());
		statsNew.setLevel(statsOrig.getLevel());
		statsNew.setRace(statsOrig.getRaceID());
		statsNew.setXP(statsOrig.getXP());
		statsNew.setJob(statsOrig.getJob());
		statsNew.setJobXP(statsOrig.getJobXP());
		statsNew.setJobLevel(statsOrig.getJobLevel());
		statsNew.setFruit(statsOrig.getFruitID());
		statsNew.setFruitXP(statsOrig.getFruitXP());
		statsNew.setFruitLevel(statsOrig.getFruitLevel());
		statsNew.setDailyCraftLimit(statsOrig.getDailyCraftLimit());
		statsNew.setSkills(statsOrig.getSkills());
	}
}