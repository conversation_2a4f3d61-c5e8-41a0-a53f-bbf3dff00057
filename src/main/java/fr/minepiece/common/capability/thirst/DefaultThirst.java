package fr.minepiece.common.capability.thirst;

import fr.minepiece.common.api.races.MinePieceRaces;
import fr.minepiece.common.capability.mpdata.IMinePieceData;
import fr.minepiece.common.capability.mpdata.MinePieceDataStorage;
import fr.minepiece.common.capability.temperature.ITemperature;
import fr.minepiece.common.capability.temperature.TemperatureProvider;
import fr.minepiece.common.network.ModPackets;
import fr.minepiece.common.network.packets.ThirstSyncPacket;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.init.MobEffects;
import net.minecraft.potion.PotionEffect;
import net.minecraft.util.DamageSource;
import net.minecraft.world.biome.Biome;

public class DefaultThirst implements IThirst {
    private float thirstLevel = 20.0F;
    private float thirstSaturation = 5.0F;
    private float exhaustion = 0.0F;
    private static final float MAX_EXHAUSTION = 4.0F;
    public static final DamageSource THIRST = new DamageSource("thirst").setDamageBypassesArmor();

    @Override
    public float getThirstLevel() {
        return thirstLevel;
    }

    @Override
    public void setThirstLevel(float level) {
        this.thirstLevel = Math.min(20.0F, Math.max(0.0F, level));
    }

    @Override
    public float getThirstSaturation() {
        return thirstSaturation;
    }

    @Override
    public void setThirstSaturation(float saturation) {
        this.thirstSaturation = Math.max(0.0F, saturation);
    }

    @Override
    public void drink(float water, float saturation) {
        this.thirstLevel = Math.min(this.thirstLevel + water, 20.0F);
        this.thirstSaturation = Math.min(this.thirstSaturation + saturation, this.thirstLevel);
    }

    @Override
    public void addExhaustion(EntityPlayerMP player, float amount) {
        // Don't add exhaustion if in creative mode
        if (amount <= 0) return;

        this.exhaustion += amount * 0.6F;
        while (this.exhaustion >= MAX_EXHAUSTION) {
            this.exhaustion -= MAX_EXHAUSTION;
            if (this.thirstSaturation > 0.0F) {
                this.thirstSaturation = Math.max(0.0F, this.thirstSaturation - 1.0F);
            } else {
                this.thirstLevel = Math.max(0.0F, this.thirstLevel - 1.0F);
            }
        }
    }

    @Override
    public float getExhaustion() {
        return exhaustion;
    }

    @Override
    public void setExhaustion(float exhaustion) {
        this.exhaustion = exhaustion;
    }

    @Override
    public void clientSync(EntityPlayerMP player) {
        ModPackets.NETWORK.sendTo(new ThirstSyncPacket(this), player);
    }

    public static void syncStats(IThirst thirstOrig, IThirst thirstNew) {
        thirstNew.setThirstLevel(thirstOrig.getThirstLevel());
        thirstNew.setThirstSaturation(thirstOrig.getThirstSaturation());
        thirstNew.setExhaustion(thirstOrig.getExhaustion());
    }

    private float getRacialResistance(EntityPlayer player) {
        float resistance = 1.0F;

        IMinePieceData data = player.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);
        if (data != null) {
            MinePieceRaces race = data.getRace();
            int level = data.getLevel();

            // Bonus pour les hommes-poissons
            if (race == MinePieceRaces.FISHMAN) {
                resistance = 0.5F;  // 50% moins de déshydratation
            }
            // Bonus pour les cyborgs
            else if (race == MinePieceRaces.CYBORG) {
                resistance = 0.8F;  // 20% moins de déshydratation
            }

            // Bonus de niveau pour toutes les races (diminue avec le niveau)
            resistance *= (1.0F - (level * 0.005F));  // 0.5% de résistance par niveau (max 25% à niveau 50)
        }

        return Math.max(0.25F, resistance);  // Au moins 25% de l'effet normal
    }

    public void update(EntityPlayer player) {
        if (player.world.isRemote || player.isCreative()) {
            this.thirstLevel = 20.0F;  // Keep thirst full in creative mode
            this.thirstSaturation = 5.0F;  // Keep saturation at base level in creative mode
            return;
        }

        // Récupérer la résistance raciale
        float racialResistance = getRacialResistance(player);

        // Modificateurs environnementaux
        float biomeModifier = 1.0F;
        Biome biome = player.world.getBiome(player.getPosition());

        // Augmentation de l'exhaustion dans les biomes chauds
        if (biome.getTemperature(player.getPosition()) > 0.9F) {
            biomeModifier = 1.5F;  // 50% plus d'exhaustion dans les biomes chauds
        } else if (biome.getTemperature(player.getPosition()) > 0.6F) {
            biomeModifier = 1.2F;  // 20% plus d'exhaustion dans les biomes tempérés chauds
        }

        // Intégration avec le système de température
        if (player.getCapability(TemperatureProvider.TEMPERATURE_CAPABILITY, null) != null) {
            ITemperature temp = player.getCapability(TemperatureProvider.TEMPERATURE_CAPABILITY, null);
            if (temp.getTemperature() > 38.0) {
                biomeModifier *= 1.0F + ((float)temp.getTemperature() - 38.0F) / 10.0F;
            }
        }

        // Appliquer à la fois le modificateur de biome et la résistance raciale
        float finalModifier = biomeModifier * racialResistance;

        // Only add exhaustion if not in creative mode
        // Different exhaustion values for different activities
        if (player.isSprinting()) {
            addExhaustion((EntityPlayerMP) player,0.008F * finalModifier);  // 4x higher than before
        } else if (player.isInWater() && player.motionY < 0) {
            addExhaustion((EntityPlayerMP) player,0.005F * finalModifier);  // Swimming underwater
        } else if (player.fallDistance > 0) {
            addExhaustion((EntityPlayerMP) player,0.004F * finalModifier);  // Jumping/falling
        } else if (player.isSwingInProgress) {
            addExhaustion((EntityPlayerMP) player,0.004F * finalModifier);  // Fighting
        } else {
            addExhaustion((EntityPlayerMP) player,0.002F * finalModifier);  // Base exhaustion unchanged
        }

        // Only apply bad effects if not in creative mode
        if (thirstLevel <= 0) {
            // Très déshydraté - Dégâts et effets sévères
            if (player.world.getWorldTime() % 60 == 0) {  // Plus fréquent
                player.attackEntityFrom(DamageSource.STARVE, 1.5F);  // Plus de dégâts
                player.addPotionEffect(new PotionEffect(MobEffects.MINING_FATIGUE, 240, 2));  // Niveau plus élevé
                player.addPotionEffect(new PotionEffect(MobEffects.WEAKNESS, 240, 2));
                player.addPotionEffect(new PotionEffect(MobEffects.SLOWNESS, 240, 1));  // Nouveau effet
            }
        } else if (thirstLevel <= 3) {
            // Fortement déshydraté
            if (player.world.getWorldTime() % 100 == 0) {
                player.attackEntityFrom(DamageSource.STARVE, 1.0F);
                player.addPotionEffect(new PotionEffect(MobEffects.MINING_FATIGUE, 220, 1));
                player.addPotionEffect(new PotionEffect(MobEffects.WEAKNESS, 220, 1));
            }
        } else if (thirstLevel <= 6) {
            // Moyennement déshydraté
            if (player.world.getWorldTime() % 200 == 0) {
                player.addPotionEffect(new PotionEffect(MobEffects.MINING_FATIGUE, 200, 0));
                player.addPotionEffect(new PotionEffect(MobEffects.WEAKNESS, 200, 0));
            }
        } else if (thirstLevel <= 10) {
            // Légèrement déshydraté - Juste une petite fatigue
            if (player.world.getWorldTime() % 300 == 0) {
                player.addPotionEffect(new PotionEffect(MobEffects.MINING_FATIGUE, 160, 0));
            }
        }
    }
}