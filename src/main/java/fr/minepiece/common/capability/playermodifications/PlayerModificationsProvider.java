package fr.minepiece.common.capability.playermodifications;

import net.minecraft.nbt.NBTBase;
import net.minecraft.util.EnumFacing;
import net.minecraftforge.common.capabilities.Capability;
import net.minecraftforge.common.capabilities.ICapabilitySerializable;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

public class PlayerModificationsProvider implements ICapabilitySerializable<NBTBase> {
    protected IPlayerModifications playerModifications;

    public PlayerModificationsProvider() {
        this.playerModifications = new DefaultPlayerModifications();
    }

    @Override
    public boolean hasCapability(@Nonnull Capability<?> capability, @Nullable EnumFacing facing) {
        return capability == PlayerModificationsStorage.PLAYER_MODIFICATIONS_CAPABILITY;
    }

    @Nullable
    @Override
    public <T> T getCapability(@Nonnull Capability<T> capability, @Nullable EnumFacing facing) {
        return this.hasCapability(capability, facing) ? PlayerModificationsStorage.PLAYER_MODIFICATIONS_CAPABILITY.cast(this.playerModifications) : null;
    }

    @Override
    public NBTBase serializeNBT() {
        return PlayerModificationsStorage.PLAYER_MODIFICATIONS_CAPABILITY.writeNBT(this.playerModifications, null);
    }

    @Override
    public void deserializeNBT(NBTBase nbt) {
        PlayerModificationsStorage.PLAYER_MODIFICATIONS_CAPABILITY.readNBT(this.playerModifications, null, nbt);
    }
}
