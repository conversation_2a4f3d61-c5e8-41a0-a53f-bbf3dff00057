package fr.minepiece.common.craft;

import fr.minepiece.common.api.jobs.MinePieceJobs;
import net.minecraft.item.ItemStack;
import net.minecraft.item.crafting.Ingredient;
import net.minecraft.util.NonNullList;
import net.minecraft.util.ResourceLocation;
import net.minecraft.world.World;
import net.minecraftforge.common.crafting.CraftingHelper;
import net.minecraftforge.oredict.ShapedOreRecipe;

import java.util.ArrayList;
import java.util.List;

/**
 * Implémentation pour les recettes simples (1 ingrédient comme le pilon)
 */
public abstract class AbstractSimpleMinePieceRecipe extends ShapedOreRecipe implements IMinePieceCraftingRecipe {
    protected final ItemStack result;
    protected final int levelRequired;
    protected final int xpReward;
    protected final MinePieceJobs requiredJob;
    protected final Ingredient ingredient;

    public AbstractSimpleMinePieceRecipe(ResourceLocation group, ItemStack result, Ingredient ingredient,
                                         int levelRequired, int xpReward,
                                         MinePieceJobs requiredJob) {

        super(group, result, createPrimer(ingredient));
        this.result = result;
        this.ingredient = ingredient;
        this.levelRequired = levelRequired;
        this.xpReward = xpReward;
        this.requiredJob = requiredJob;
    }

    private static CraftingHelper.ShapedPrimer createPrimer(Ingredient ingredient) {
        CraftingHelper.ShapedPrimer primer = new CraftingHelper.ShapedPrimer();
        primer.width = 1;
        primer.height = 1;
        primer.mirrored = false;
        primer.input = NonNullList.withSize(1, ingredient);
        return primer;
    }

    @Override
    public boolean matches(IMinePieceCraftingInventory inventory, World world) {
        ItemStack input = inventory.getStackInSlot(0);
        return !input.isEmpty() && ingredient.apply(input);
    }

    @Override
    public CraftingResult getCraftingResult(IMinePieceCraftingInventory inventory) {
        ItemStack input = inventory.getStackInSlot(0);
        if (input.isEmpty() || !ingredient.apply(input)) {
            return new CraftingResult(ItemStack.EMPTY, 0, new ArrayList<>());
        }

        int multiplier = Math.min(input.getCount(),
                result.getMaxStackSize() / result.getCount());

        // Création du résultat
        ItemStack craftedResult = result.copy();
        craftedResult.setCount(craftedResult.getCount() * multiplier);

        // Item à consommer
        List<CraftingResult.ItemPosition> consumedItems = new ArrayList<>();
        consumedItems.add(new CraftingResult.ItemPosition(0, multiplier));

        return new CraftingResult(craftedResult, multiplier, consumedItems);
    }

    @Override
    public int getLevelRequired() {
        return levelRequired;
    }

    @Override
    public int getBaseXpReward() {
        return xpReward;
    }

    @Override
    public MinePieceJobs getRequiredJob() {
        return requiredJob;
    }

    @Override
    public ItemStack getRecipeOutput() {
        return result.copy();
    }
}