package fr.minepiece.common.utils;

import fr.minepiece.Reference;
import fr.minepiece.common.network.ModPackets;
import fr.minepiece.common.network.packets.VisualizeZonePacket;
import net.minecraft.client.Minecraft;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.World;
import net.minecraftforge.common.DimensionManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nullable;
import java.util.*;

/**
 * Système permettant de définir des zones dans le monde avec des températures personnalisées
 */
public class TemperatureZones {

    private static final Logger LOGGER = LogManager.getLogger(Reference.MOD_ID);
    private static final Map<Integer, List<TemperatureZone>> DIMENSION_ZONES = new HashMap<>();

    /**
     * Représente une zone 3D avec une température personnalisée
     */
    public static class TemperatureZone {
        private final String name;
        private final BlockPos minPos;
        private final BlockPos maxPos;
        private final double temperature;
        private final double blendRadius;
        private final double priority;

        /**
         * Constructeur de zone de température
         * @param name Nom unique de la zone
         * @param minPos Première coordonnée (minimum) de la zone
         * @param maxPos Seconde coordonnée (maximum) de la zone
         * @param temperature Température en °C dans cette zone
         * @param blendRadius Rayon de fusion en blocs (transition progressive)
         * @param priority Priorité de la zone (valeurs plus élevées ont priorité)
         */
        public TemperatureZone(String name, BlockPos minPos, BlockPos maxPos, double temperature, double blendRadius, double priority) {
            this.name = name;

            // S'assurer que minPos est bien le minimum et maxPos le maximum
            this.minPos = new BlockPos(
                    Math.min(minPos.getX(), maxPos.getX()),
                    Math.min(minPos.getY(), maxPos.getY()),
                    Math.min(minPos.getZ(), maxPos.getZ())
            );

            this.maxPos = new BlockPos(
                    Math.max(minPos.getX(), maxPos.getX()),
                    Math.max(minPos.getY(), maxPos.getY()),
                    Math.max(minPos.getZ(), maxPos.getZ())
            );

            this.temperature = temperature;
            this.blendRadius = blendRadius;
            this.priority = priority;
        }

        public String getName() {
            return name;
        }

        public BlockPos getMinPos() {
            return minPos;
        }

        public BlockPos getMaxPos() {
            return maxPos;
        }

        public double getTemperature() {
            return temperature;
        }

        public double getBlendRadius() {
            return blendRadius;
        }

        public double getPriority() {
            return priority;
        }

        /**
         * Vérifie si la position donnée est dans cette zone
         * @param pos Position à vérifier
         * @return true si la position est dans la zone, false sinon
         */
        public boolean contains(BlockPos pos) {
            return pos.getX() >= minPos.getX() && pos.getX() <= maxPos.getX() &&
                    pos.getY() >= minPos.getY() && pos.getY() <= maxPos.getY() &&
                    pos.getZ() >= minPos.getZ() && pos.getZ() <= maxPos.getZ();
        }

        /**
         * Calcule la distance minimale à la bordure de la zone
         * @param pos Position depuis laquelle calculer
         * @return Distance à la bordure (0 si à l'intérieur, valeur positive si à l'extérieur)
         */
        public double distanceToBorder(BlockPos pos) {
            if (contains(pos)) {
                // Si à l'intérieur, calcule la distance minimale aux bords
                double distX = Math.min(pos.getX() - minPos.getX(), maxPos.getX() - pos.getX());
                double distY = Math.min(pos.getY() - minPos.getY(), maxPos.getY() - pos.getY());
                double distZ = Math.min(pos.getZ() - minPos.getZ(), maxPos.getZ() - pos.getZ());
                return -Math.min(Math.min(distX, distY), distZ);
            } else {
                // Si à l'extérieur, calcule la distance minimale à un point de la zone
                double dx = Math.max(0, Math.max(minPos.getX() - pos.getX(), pos.getX() - maxPos.getX()));
                double dy = Math.max(0, Math.max(minPos.getY() - pos.getY(), pos.getY() - maxPos.getY()));
                double dz = Math.max(0, Math.max(minPos.getZ() - pos.getZ(), pos.getZ() - maxPos.getZ()));
                return Math.sqrt(dx * dx + dy * dy + dz * dz);
            }
        }

        /**
         * Calcule le facteur d'influence de la zone à la position donnée
         * @param pos Position à vérifier
         * @return Valeur entre 0.0 (aucune influence) et 1.0 (influence totale)
         */
        public double getInfluenceFactor(BlockPos pos) {
            double distance = distanceToBorder(pos);

            if (distance <= -blendRadius) {
                // Au cœur de la zone, influence totale
                return 1.0;
            } else if (distance >= blendRadius) {
                // Trop loin de la zone, aucune influence
                return 0.0;
            } else {
                // Dans la zone de transition, influence progressive
                return 0.5 * (1.0 - distance / blendRadius) + 0.5;
            }
        }
    }

    /**
     * Ajoute une zone de température à une dimension
     * @param dimension ID de la dimension
     * @param zone Zone de température à ajouter
     * @return true si ajouté avec succès, false si une zone avec le même nom existe déjà
     */
    public static boolean addZone(int dimension, TemperatureZone zone) {
        List<TemperatureZone> zones = DIMENSION_ZONES.computeIfAbsent(dimension, k -> new ArrayList<>());

        // Vérifier si une zone avec ce nom existe déjà
        for (TemperatureZone existingZone : zones) {
            if (existingZone.getName().equals(zone.getName())) {
                return false;
            }
        }

        zones.add(zone);
        // Trier les zones par priorité (priorité élevée en premier)
        zones.sort(Comparator.comparingDouble(TemperatureZone::getPriority).reversed());
        return true;
    }

    /**
     * Supprime une zone par son nom dans la dimension spécifiée
     * @param dimension ID de la dimension
     * @param zoneName Nom de la zone à supprimer
     * @return true si supprimé avec succès, false si la zone n'existe pas
     */
    public static boolean removeZone(int dimension, String zoneName) {
        List<TemperatureZone> zones = DIMENSION_ZONES.get(dimension);
        if (zones == null) {
            return false;
        }

        for (Iterator<TemperatureZone> iterator = zones.iterator(); iterator.hasNext();) {
            TemperatureZone zone = iterator.next();
            if (zone.getName().equals(zoneName)) {
                iterator.remove();
                return true;
            }
        }

        return false;
    }

    /**
     * Trouve la première zone à la position spécifiée dans la dimension
     * @param world Le monde
     * @param pos Position à vérifier
     * @return La zone trouvée, ou null si aucune
     */
    @Nullable
    public static TemperatureZone getZoneAt(World world, BlockPos pos) {
        int dimension = world.provider.getDimension();
        List<TemperatureZone> zones = DIMENSION_ZONES.get(dimension);

        if (zones == null || zones.isEmpty()) {
            return null;
        }

        // Parcourir les zones par ordre de priorité
        for (TemperatureZone zone : zones) {
            if (zone.contains(pos)) {
                return zone;
            }
        }

        return null;
    }

    /**
     * Obtient toutes les zones influençant une position
     * @param world Le monde
     * @param pos Position à vérifier
     * @return Une liste de zones avec leur influence (entre 0.0 et 1.0)
     */
    public static List<Map.Entry<TemperatureZone, Double>> getInfluencingZones(World world, BlockPos pos) {
        int dimension = world.provider.getDimension();
        List<TemperatureZone> zones = DIMENSION_ZONES.get(dimension);

        if (zones == null || zones.isEmpty()) {
            return Collections.emptyList();
        }

        List<Map.Entry<TemperatureZone, Double>> influencingZones = new ArrayList<>();

        for (TemperatureZone zone : zones) {
            double influence = zone.getInfluenceFactor(pos);
            if (influence > 0.0) {
                influencingZones.add(new AbstractMap.SimpleEntry<>(zone, influence));
            }
        }

        return influencingZones;
    }

    /**
     * Calcule la température de zone à la position donnée
     * @param world Le monde
     * @param pos Position à vérifier
     * @param defaultTemp Température par défaut si aucune zone ne s'applique
     * @return La température modifiée par les zones
     */
    public static double getZoneTemperature(World world, BlockPos pos, double defaultTemp) {
        List<Map.Entry<TemperatureZone, Double>> influencingZones = getInfluencingZones(world, pos);

        if (influencingZones.isEmpty()) {
            return defaultTemp;
        }

        // Gestion des priorités et des influences
        double highestPriority = influencingZones.get(0).getKey().getPriority();
        double tempSum = 0.0;
        double influenceSum = 0.0;

        // Garder uniquement les zones de priorité maximale
        List<Map.Entry<TemperatureZone, Double>> highPriorityZones = new ArrayList<>();
        for (Map.Entry<TemperatureZone, Double> entry : influencingZones) {
            TemperatureZone zone = entry.getKey();
            if (Math.abs(zone.getPriority() - highestPriority) < 0.0001) {
                highPriorityZones.add(entry);
            }
        }

        // Calculer la température moyenne pondérée par l'influence
        for (Map.Entry<TemperatureZone, Double> entry : highPriorityZones) {
            double influence = entry.getValue();
            tempSum += entry.getKey().getTemperature() * influence;
            influenceSum += influence;
        }

        if (influenceSum > 0.0) {
            double avgTemp = tempSum / influenceSum;
            // Influence globale combinée sur la température par défaut
            double totalInfluence = Math.min(1.0, influenceSum);
            return avgTemp * totalInfluence + defaultTemp * (1.0 - totalInfluence);
        }

        return defaultTemp;
    }

    /**
     * Obtient la liste de toutes les zones d'une dimension
     * @param dimension ID de la dimension
     * @return Liste des zones de température
     */
    public static List<TemperatureZone> getZonesForDimension(int dimension) {
        return DIMENSION_ZONES.getOrDefault(dimension, Collections.emptyList());
    }

    /**
     * Supprime toutes les zones dans toutes les dimensions
     */
    public static void clearAllZones() {
        DIMENSION_ZONES.clear();
    }

    /**
     * Obtient toutes les zones de température de toutes les dimensions
     * @return Une map des zones par dimension
     */
    public static Map<Integer, List<TemperatureZone>> getAllDimensionZones() {
        Map<Integer, List<TemperatureZone>> result = new HashMap<>();

        for (Map.Entry<Integer, List<TemperatureZone>> entry : DIMENSION_ZONES.entrySet()) {
            result.put(entry.getKey(), new ArrayList<>(entry.getValue()));
        }

        return result;
    }

    /**
     * Visualise une zone de température en envoyant un paquet au client
     * pour afficher les contours de la zone
     * @param player Le joueur qui verra la visualisation
     * @param zone La zone à visualiser
     * @param durationTicks Durée d'affichage en ticks (20 ticks = 1 seconde)
     */
    public static void visualizeZone(EntityPlayerMP player, TemperatureZone zone, int durationTicks) {
        BlockPos min = zone.getMinPos();
        BlockPos max = zone.getMaxPos();

        // Envoyer le paquet réseau pour afficher la zone
        ModPackets.NETWORK.sendTo(new VisualizeZonePacket(
                min.getX(), min.getY(), min.getZ(),
                max.getX(), max.getY(), max.getZ(),
                durationTicks, zone.getTemperature()), player);
    }

    /**
     * Supprime toutes les zones dans une dimension spécifique
     * @param dimension ID de la dimension
     */
    public static void clearZonesForDimension(int dimension) {
        DIMENSION_ZONES.remove(dimension);
    }
}