package fr.minepiece.common.utils.handlers;

import fr.minepiece.client.gui.practical.jobs.blacksmith.GuiAtelier;
import fr.minepiece.client.gui.practical.jobs.blacksmith.GuiFonderie;
import fr.minepiece.client.gui.practical.jobs.blacksmith.GuiForge;
import fr.minepiece.client.gui.practical.jobs.cook.*;
import fr.minepiece.client.gui.practical.jobs.medic.GuiBancDeMedicament;
import fr.minepiece.client.gui.practical.jobs.medic.GuiExtracteur;
import fr.minepiece.client.gui.practical.jobs.medic.GuiPaillasse;
import fr.minepiece.client.gui.practical.jobs.medic.GuiPilon;
import fr.minepiece.common.blocks.jobs.crafting.blacksmith.atelier.container.ContainerAtelier;
import fr.minepiece.common.blocks.jobs.crafting.blacksmith.fonderie.container.ContainerFonderie;
import fr.minepiece.common.blocks.jobs.crafting.blacksmith.fonderie.tileentity.TileEntityFonderie;
import fr.minepiece.common.blocks.jobs.crafting.blacksmith.forge.container.ContainerForge;
import fr.minepiece.common.blocks.jobs.crafting.blacksmith.gui.BlacksmithGuiIDs;
import fr.minepiece.common.blocks.jobs.crafting.cook.chaudron.container.ContainerChaudron;
import fr.minepiece.common.blocks.jobs.crafting.cook.fut.container.ContainerFut;
import fr.minepiece.common.blocks.jobs.crafting.cook.fut.tileentity.TileEntityFut;
import fr.minepiece.common.blocks.jobs.crafting.cook.gui.CookGuiIDs;
import fr.minepiece.common.blocks.jobs.crafting.cook.plancheadecouper.container.ContainerPlancheADecouper;
import fr.minepiece.common.blocks.jobs.crafting.cook.plancheadecouper.tileentity.TileEntityPlancheADecouper;
import fr.minepiece.common.blocks.jobs.crafting.cook.plandetravail.container.ContainerPlanDeTravail;
import fr.minepiece.common.blocks.jobs.crafting.cook.tablededressage.container.ContainerTableDeDressage;
import fr.minepiece.common.blocks.jobs.crafting.medic.bancdemedicament.container.ContainerBancDeMedicament;
import fr.minepiece.common.blocks.jobs.crafting.medic.extracteur.container.ContainerExtracteur;
import fr.minepiece.common.blocks.jobs.crafting.medic.extracteur.tileentity.TileEntityExtracteur;
import fr.minepiece.common.blocks.jobs.crafting.medic.gui.MedicGuiIDs;
import fr.minepiece.common.blocks.jobs.crafting.medic.paillasse.container.ContainerPaillasse;
import fr.minepiece.common.blocks.jobs.crafting.medic.pilon.container.ContainerPilon;
import fr.minepiece.common.blocks.jobs.crafting.medic.pilon.tileentity.TileEntityPilon;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.World;
import net.minecraftforge.fml.common.network.IGuiHandler;

public class JobsGuiHandler implements IGuiHandler {
    @Override
    public Object getServerGuiElement(int ID, EntityPlayer player, World world, int x, int y, int z) {
        BlockPos pos = new BlockPos(x, y, z);
        switch(ID) {

            // Médecin
            case MedicGuiIDs.PAILLASSE:
                return new ContainerPaillasse(player.inventory, world, pos);
            case MedicGuiIDs.PILON:
                return new ContainerPilon(player.inventory, (TileEntityPilon) world.getTileEntity(pos));
            case MedicGuiIDs.EXTRACTEUR:
                return new ContainerExtracteur(player.inventory, (TileEntityExtracteur) world.getTileEntity(pos));
            case MedicGuiIDs.BANC_DE_MEDICAMENT:
                return new ContainerBancDeMedicament(player.inventory, world, pos);

            // Cuisinier
            case CookGuiIDs.PLAN_DE_TRAVAIL:
                return new ContainerPlanDeTravail(player.inventory, world, pos);
            case CookGuiIDs.PLANCHE_A_DECOUPER:
                return new ContainerPlancheADecouper(player.inventory, (TileEntityPlancheADecouper) world.getTileEntity(pos));
            case CookGuiIDs.CHAUDRON:
                return new ContainerChaudron(player.inventory, world, pos);
            case CookGuiIDs.FUT:
                return new ContainerFut(player.inventory, (TileEntityFut) world.getTileEntity(pos));
            case CookGuiIDs.TABLE_DE_DRESSAGE:
                return new ContainerTableDeDressage(player.inventory, world, pos);

            // Forgeron
            case BlacksmithGuiIDs.ATELIER:
                return new ContainerAtelier(player.inventory, world, pos);
            case BlacksmithGuiIDs.FONDERIE:
                return new ContainerFonderie(player.inventory, (TileEntityFonderie) world.getTileEntity(pos));
            case BlacksmithGuiIDs.FORGE:
                return new ContainerForge(player.inventory, world, pos);

            default:
                return null;
        }
    }

    @Override
    public Object getClientGuiElement(int ID, EntityPlayer player, World world, int x, int y, int z) {
        BlockPos pos = new BlockPos(x, y, z);
        switch(ID) {

            // Médecin
            case MedicGuiIDs.PAILLASSE:
                return new GuiPaillasse(player.inventory, world, pos);
            case MedicGuiIDs.PILON:
                return new GuiPilon(player.inventory, (TileEntityPilon) world.getTileEntity(pos));
            case MedicGuiIDs.EXTRACTEUR:
                return new GuiExtracteur(player.inventory, (TileEntityExtracteur) world.getTileEntity(pos));
            case MedicGuiIDs.BANC_DE_MEDICAMENT:
                return new GuiBancDeMedicament(player.inventory, world, pos);

            // Cuisinier
            case CookGuiIDs.PLAN_DE_TRAVAIL:
                return new GuiPlanDeTravail(player.inventory, world, pos);
            case CookGuiIDs.PLANCHE_A_DECOUPER:
                return new GuiPlancheADecouper(player.inventory, (TileEntityPlancheADecouper) world.getTileEntity(pos));
            case CookGuiIDs.CHAUDRON:
                return new GuiChaudron(player.inventory, world, pos);
            case CookGuiIDs.FUT:
                return new GuiFut(player.inventory, (TileEntityFut) world.getTileEntity(pos));
            case CookGuiIDs.TABLE_DE_DRESSAGE:
                return new GuiTableDeDressage(player.inventory, world, pos);

            // Forgeron
            case BlacksmithGuiIDs.ATELIER:
                return new GuiAtelier(player.inventory, world, pos);
            case BlacksmithGuiIDs.FONDERIE:
                return new GuiFonderie(player.inventory, (TileEntityFonderie) world.getTileEntity(pos));
            case BlacksmithGuiIDs.FORGE:
                return new GuiForge(player.inventory, world, pos);

            default:
                return null;
        }
    }
}