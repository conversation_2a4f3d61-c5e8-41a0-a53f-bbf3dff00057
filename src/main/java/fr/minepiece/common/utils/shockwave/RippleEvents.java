package fr.minepiece.common.utils.shockwave;

import fr.minepiece.Reference;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

@Mod.EventBusSubscriber(modid = Reference.MOD_ID)
public class RippleEvents {

    public static boolean continueRipple = false;
    public static int delayTicks = 0;
    private static Ripple ripple = null;

    @SubscribeEvent
    public static void onTick (TickEvent event) {
        if (!continueRipple) return;
        if (delayTicks > 0) {
            delayTicks--;
            return;
        }
        System.out.println("RIPPLEEVENTS: RAN RIPPLE");
        ripple.run();
    }

    public static void setRipple(Ripple ripple1) {
        if (ripple1 == null) {
            ripple = null;
            return;
        }
        if (ripple != null) return;
        ripple = ripple1;
    }

    public static void continueRippleWithDelay(int delayTick) {
        continueRipple = true;
        delayTicks = delayTick;
    }

}
