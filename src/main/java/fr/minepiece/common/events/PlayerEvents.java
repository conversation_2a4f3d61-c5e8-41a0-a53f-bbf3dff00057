package fr.minepiece.common.events;

import com.artemis.artemislib.util.attributes.ArtemisLibAttributes;
import fr.minepiece.Reference;
import fr.minepiece.common.api.races.IMinePieceRace;
import fr.minepiece.common.api.races.MinePieceRaces;
import fr.minepiece.common.api.stats.MinePieceAttributeModifier;
import fr.minepiece.common.api.stats.StatsUtils;
import fr.minepiece.common.capability.mpcooldowns.*;
import fr.minepiece.common.capability.mpdata.*;
import fr.minepiece.common.capability.mpstats.*;
import fr.minepiece.common.capability.playermodifications.*;
import fr.minepiece.common.init.ModConfiguration;
import fr.minepiece.common.init.ModPotions;
import fr.minepiece.common.network.ModPackets;
import fr.minepiece.common.network.packets.OpenNameSelectionPacket;
import fr.minepiece.common.api.utils.PlayerUtils;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.ai.attributes.IAttributeInstance;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.potion.PotionEffect;
import net.minecraft.util.ResourceLocation;
import net.minecraftforge.event.AttachCapabilitiesEvent;
import net.minecraftforge.event.entity.living.LivingEvent;
import net.minecraftforge.event.entity.living.LivingHurtEvent;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;

@Mod.EventBusSubscriber(modid = Reference.MOD_ID)
public class PlayerEvents {

	public static final ResourceLocation MPDATA_LOCATION = new ResourceLocation(Reference.MOD_ID, "mpdata");
	public static final ResourceLocation MPSTATS_LOCATION = new ResourceLocation(Reference.MOD_ID, "mpstats");
	public static final ResourceLocation MPCOOLDOWN_LOCATION = new ResourceLocation(Reference.MOD_ID, "mpcooldown");
	public static final ResourceLocation PLAYER_MODIFICATIONS_LOCATION = new ResourceLocation(Reference.MOD_ID, "playermodifications");


	/**
	 * Fix for aquaacrobatics
	 * */
	@SubscribeEvent
	public static void onLivingUpdateEvent(LivingEvent.LivingUpdateEvent event) {
		if (!(event.getEntityLiving() instanceof EntityPlayer)) return;

		EntityPlayer player = (EntityPlayer) event.getEntityLiving();
		IMinePieceData data = player.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);

		// Only proceed if player is a Giant race
		if (data != null && data.getRace() == MinePieceRaces.GIANT) {
			// Check if the player has both modifiers
			IAttributeInstance heightAttribute = player.getEntityAttribute(ArtemisLibAttributes.ENTITY_HEIGHT);
			IAttributeInstance widthAttribute = player.getEntityAttribute(ArtemisLibAttributes.ENTITY_WIDTH);

			// If height modifier is missing but width is present, something's wrong
			if (heightAttribute.getModifier(MinePieceAttributeModifier.GIANT_HEIGHT.getUuid()) == null &&
					widthAttribute.getModifier(MinePieceAttributeModifier.GIANT_WIDTH.getUuid()) != null) {

				// Re-apply the height modifier
				data.getRace().getInstance().init(player, player.world);
			}
		} else if (data != null && data.getRace() == MinePieceRaces.TONTATTA) {
			// Check if the player has both modifiers
			IAttributeInstance heightAttribute = player.getEntityAttribute(ArtemisLibAttributes.ENTITY_HEIGHT);
			IAttributeInstance widthAttribute = player.getEntityAttribute(ArtemisLibAttributes.ENTITY_WIDTH);

			// If height modifier is missing but width is present, something's wrong
			if (heightAttribute.getModifier(MinePieceAttributeModifier.TONTATTA_HEIGHT.getUuid()) == null &&
					widthAttribute.getModifier(MinePieceAttributeModifier.TONTATTA_WIDTH.getUuid()) != null) {

				// Re-apply the height modifier
				data.getRace().getInstance().init(player, player.world);
			}
		}
	}

	@SubscribeEvent
	public static void attachCapabilities(final AttachCapabilitiesEvent<Entity> e) {
		if (e.getObject() instanceof EntityPlayer) {
			e.addCapability(MPDATA_LOCATION, new MinePieceDataProvider());
			e.addCapability(MPSTATS_LOCATION, new MinePieceStatsProvider());
			e.addCapability(MPCOOLDOWN_LOCATION, new MinePieceCooldownProvider());
			e.addCapability(PLAYER_MODIFICATIONS_LOCATION, new PlayerModificationsProvider());
		}
	}

	@SubscribeEvent
	public static void playerUpdate(LivingEvent.LivingUpdateEvent e) {
		if (e.getEntityLiving().world.isRemote) return;
		if (!(e.getEntityLiving() instanceof EntityPlayerMP)) return;

		EntityPlayerMP player = (EntityPlayerMP) e.getEntityLiving();
		handlePlayerUpdate(player);
	}

	private static void handlePlayerUpdate(EntityPlayerMP player) {
		IMinePieceData data = player.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);
		if (data == null) return;

		// Check RP Name
		if ((data.getRPName() == null || data.getRPName().isEmpty()) &&
				PlayerUtils.isPlayerOnline(player) &&
				player.connection.netManager.isChannelOpen()) {
			ModPackets.NETWORK.sendTo(new OpenNameSelectionPacket(), player);
		}
		// Check RP Last Name
		if ((data.getRPLastName() == null || data.getRPLastName().isEmpty()) &&
				PlayerUtils.isPlayerOnline(player) &&
				player.connection.netManager.isChannelOpen()) {
			ModPackets.NETWORK.sendTo(new OpenNameSelectionPacket(), player);
		}

		// Race handling
		int raceID = data.getRaceID();
		if (raceID == -1) return;

		IMinePieceRace race = data.getRace().getInstance();
		if (data.getLevel() >= 20) {
			player.getServer().addScheduledTask(() -> race.powerUp(player, player.world));
		} else {
			player.getServer().addScheduledTask(() -> race.passive(player, player.world));
		}

		// Energy regen
		handleEnergyRegen(player);
	}

	private static void handleEnergyRegen(EntityPlayerMP player) {
		IMinePieceCooldown cooldown = player.getCapability(MinePieceCooldownStorage.MP_COOLDOWN_CAPABILITY, null);
		if (cooldown == null) return;

		if (cooldown.getEnergyRegen() < System.currentTimeMillis()) {
			player.getServer().addScheduledTask(() -> {
				IMinePieceStats stats = player.getCapability(MinePieceStatsStorage.MP_STATS_CAPABILITY, null);
				if (stats != null) {
					int additionnal = 0;
					if (player.isPotionActive(ModPotions.ENERGY_REGEN_EFFECT))
						additionnal = player.getActivePotionEffect(ModPotions.ENERGY_REGEN_EFFECT).getAmplifier() + 1;
					stats.addEnergy(1 +additionnal);
					stats.clientSync(player);
				}
			});
			if (player.isPotionActive(ModPotions.ENERGY_REGEN_EFFECT)) {
				PotionEffect effect = player.getActivePotionEffect(ModPotions.ENERGY_REGEN_EFFECT);
				cooldown.setEnergyRegen(System.currentTimeMillis() + ModConfiguration.energy_regen_delay - ((effect.getAmplifier()+1) * 1000));
			} else {
				cooldown.setEnergyRegen(System.currentTimeMillis() + ModConfiguration.energy_regen_delay);
			}
		}
	}

	@SubscribeEvent
	public static void onEntityDamaged(LivingHurtEvent event) {
		if (event.getEntityLiving().world.isRemote) return;

		EntityLivingBase entity = event.getEntityLiving();
		if (entity.isPotionActive(ModPotions.COLA_EFFECT)) {
			PotionEffect colaEffect = entity.getActivePotionEffect(ModPotions.COLA_EFFECT);
			int amplifier = colaEffect.getAmplifier() + 1;
			double reductionPercentage = amplifier * 0.10;
			event.setAmount((float)(event.getAmount() * (1.0 - reductionPercentage)));
		}
	}
}