package fr.minepiece.common.events;

import fr.minepiece.Reference;
import fr.minepiece.common.entity.EntitySmokeBomb;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;

/**
 * Gestionnaire d'événements pour appliquer les effets des bombes fumigènes
 */
@Mod.EventBusSubscriber(modid = Reference.MOD_ID)
public class SmokeEffectHandler {

    /**
     * Enregistre le gestionnaire d'événements
     */
    public static void registerEvents() {
        MinecraftForge.EVENT_BUS.register(SmokeEffectHandler.class);
    }

    private static int tickCounter = 0;
    private static final int CHECK_INTERVAL = 5;

    /**
     * Événement appelé à chaque tick serveur
     * Applique les effets de cécité aux joueurs dans les nuages de fumée
     */
    @SubscribeEvent
    public static void onServerTick(TickEvent.ServerTickEvent event) {
        if (event.phase != TickEvent.Phase.END) {
            return;
        }
        tickCounter++;
        if (tickCounter >= CHECK_INTERVAL) {
            tickCounter = 0;
            EntitySmokeBomb.applyBlindingEffects();
        }
    }
}