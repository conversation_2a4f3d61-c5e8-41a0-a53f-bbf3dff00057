package fr.minepiece.common.items.jobs.medic.healing;

import fr.minepiece.MinePiece;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.init.MobEffects;
import net.minecraft.item.EnumAction;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.potion.PotionEffect;
import net.minecraft.util.ActionResult;
import net.minecraft.util.EnumActionResult;
import net.minecraft.util.EnumHand;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.world.World;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class CremeDeSoinItem extends Item {

    private static final int APPLY_DURATION = 3 * 20; // 3 secondes en ticks
    private static final int EFFECT_DURATION = 30 * 20;
    private static final int COOLDOWN = 5 * 20; // 5 secondes en ticks
    private static final Map<UUID, Long> lastUseTime = new HashMap<>();

    public CremeDeSoinItem() {
        setRegistryName("creme_de_soin");
        setUnlocalizedName("creme_de_soin");
        setCreativeTab(MinePiece.ITEMS);
        setMaxStackSize(16);
    }

    @Override
    public int getMaxItemUseDuration(ItemStack stack) {
        return APPLY_DURATION;
    }

    @Override
    public EnumAction getItemUseAction(ItemStack stack) {
        return EnumAction.BOW; // Animation similaire à tirer avec un arc
    }

    @Override
    public ActionResult<ItemStack> onItemRightClick(World worldIn, EntityPlayer playerIn, EnumHand handIn) {
        ItemStack itemstack = playerIn.getHeldItem(handIn);

        // Vérifier le cooldown
        UUID playerUUID = playerIn.getUniqueID();
        long currentTime = System.currentTimeMillis();
        long lastUsed = lastUseTime.getOrDefault(playerUUID, 0L);

        if (currentTime - lastUsed < COOLDOWN) {
            if (!worldIn.isRemote) {
                long remainingCooldown = (COOLDOWN - (currentTime - lastUsed)) / 1000;
                playerIn.sendMessage(new TextComponentString("§cVous devez attendre encore " + remainingCooldown + " secondes !"));
            }
            return new ActionResult<>(EnumActionResult.FAIL, itemstack);
        }

        playerIn.setActiveHand(handIn);
        return new ActionResult<>(EnumActionResult.SUCCESS, itemstack);
    }

    @Override
    public ItemStack onItemUseFinish(ItemStack stack, World worldIn, EntityLivingBase entityLiving) {
        if (!(entityLiving instanceof EntityPlayer)) {
            return stack;
        }

        EntityPlayer player = (EntityPlayer) entityLiving;

        if (!worldIn.isRemote) {
            // Ajouter effet de régénération
            player.addPotionEffect(new PotionEffect(MobEffects.REGENERATION, EFFECT_DURATION, 0)); // Régénération 1 pendant 45 secondes

            // Mettre à jour le temps de dernière utilisation
            lastUseTime.put(player.getUniqueID(), System.currentTimeMillis());

            player.sendMessage(new TextComponentString("§aVous avez appliqué de la crème de soin. Effet de régénération activé."));

            // Diminuer le stack
            stack.shrink(1);
        }

        return stack;
    }
}