package fr.minepiece.common.items.weapons.shooting;

import fr.minepiece.common.entity.projectile.EntityExplosiveProjectile;
import fr.minepiece.common.init.ModWeapons;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.util.ActionResult;
import net.minecraft.util.EnumActionResult;
import net.minecraft.util.EnumHand;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

public class TieredBazooka extends TieredShootingWeapon {

    private static final float[] DAMAGE_VALUES = new float[] {9.0f, 10.5f, 12.0f}; // Dégâts de base pour chaque tier
    private static final int[] DURABILITY_VALUES = new int[] {1000, 2000, 3000}; // Durabilité pour chaque tier
    private static final int COOLDOWN = 400;
    private static final float BASE_ACCURACY = 0.06f; // Légère imprécision de base
    private static final float BASE_VELOCITY = 1.5f; // Vitesse de base du projectile
    private static final float GRAVITY = 0.05f; // Forte gravité pour une trajectoire parabolique prononcée

    // Taille de l'explosion pour chaque tier
    private static final float[] EXPLOSION_SIZE = new float[] {4.0F, 5.0F, 7.0F,};

    public TieredBazooka(String name, ToolMaterial material, Item fragmentItem) {
        super(
                name,
                3, // maxTier
                DURABILITY_VALUES,
                DAMAGE_VALUES,
                fragmentItem,
                ModWeapons.BAZOOKA_SHELL,
                BASE_ACCURACY,
                true, // Utilise une trajectoire parabolique
                BASE_VELOCITY,
                GRAVITY
        );
    }

    @Override
    protected float getMeleeDamage() {
        return BAZOOKA_MELEE_DAMAGE;
    }

    @Override
    protected ActionResult<ItemStack> fireWeaponWithTier(World world, EntityPlayer player, EnumHand hand, ItemStack stack, int tier) {
        if (!world.isRemote) {
            // Calcul de la direction de tir
            float rotationPitch = player.rotationPitch;
            float rotationYaw = player.rotationYaw;

            // Convertir les angles en radians
            float yawRadians = rotationYaw * 0.017453292F;
            float pitchRadians = rotationPitch * 0.017453292F;

            // Calculer les composantes directionnelles
            float cosYaw = MathHelper.cos(-yawRadians - (float)Math.PI);
            float sinYaw = MathHelper.sin(-yawRadians - (float)Math.PI);
            float cosPitch = -MathHelper.cos(-pitchRadians);
            float sinPitch = MathHelper.sin(-pitchRadians);

            // Ajuster la précision en fonction du tier
            float accuracy = getAdjustedAccuracy(tier);
            float randomX = (world.rand.nextFloat() - 0.5f) * accuracy;
            float randomY = (world.rand.nextFloat() - 0.5f) * accuracy;
            float randomZ = (world.rand.nextFloat() - 0.5f) * accuracy;

            // Calculer la vitesse en tenant compte du tier
            float velocityFactor = getAdjustedVelocityFactor(tier);

            // Calculer le vecteur de vélocité initial
            double velX = (sinYaw * cosPitch + randomX) * velocityFactor;
            double velY = (sinPitch + randomY) * velocityFactor;
            double velZ = (cosYaw * cosPitch + randomZ) * velocityFactor;

            // Position de départ du projectile
            double posX = player.posX;
            double posY = player.posY + player.getEyeHeight() - 0.1;
            double posZ = player.posZ;

            // Créer un projectile explosif
            EntityExplosiveProjectile explosive = new EntityExplosiveProjectile(
                    world,
                    player,
                    damages[tier], // Dégâts de base
                    tier,
                    EXPLOSION_SIZE[tier], // Puissance de l'explosion basée sur le tier
                    usesParabolicPath(), // Utilise les paramètres de l'arme
                    getGravity()
            );

            // Définir la position et la vélocité
            explosive.setPosition(posX, posY, posZ);
            explosive.shoot(velX, velY, velZ, velocityFactor, 0.0F); // 0.0F = pas de dispersion supplémentaire

            // Paramètres supplémentaires
            explosive.setWeaponType("bazooka");
            explosive.setIgnoreEntity(player);
            int maxTicks = 20 + (tier * 5); // 20, 25, 30 ticks selon le tier
            explosive.setMaxTicksAlive(maxTicks);

            // Dans le mode créatif ou pour les tiers supérieurs, permet de casser les blocs
            boolean breakBlocks = player.isCreative() || tier >= 2;
            explosive.setBreakBlocks(breakBlocks);

            world.spawnEntity(explosive);
            stack.damageItem(1, player);

            // Effet de recul pour le joueur en fonction du tier
            float knockbackStrength = 0.7f + (tier * 0.5f);
            Vec3d lookVec = player.getLookVec();
            player.addVelocity(-lookVec.x * knockbackStrength, 0.1, -lookVec.z * knockbackStrength);
            player.velocityChanged = true;
        }

        return ActionResult.newResult(EnumActionResult.SUCCESS, stack);
    }

    @Override
    public int getCooldown() {
        return COOLDOWN;
    }

    @Override
    public float getRange() {
        return 28.0f; // Le bazooka a une portée plus longue
    }
}