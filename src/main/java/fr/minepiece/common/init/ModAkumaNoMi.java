package fr.minepiece.common.init;

import fr.minepiece.Reference;
import fr.minepiece.common.items.fruits.logia.MaguMaguFruitItem;
import fr.minepiece.common.items.fruits.paramecia.BaneBaneFruitItem;
import fr.minepiece.common.items.fruits.paramecia.BukuBukuFruitItem;
import fr.minepiece.common.items.fruits.paramecia.MiraMiraFruitItem;
import fr.minepiece.common.items.fruits.paramecia.WapuWapuFruitItem;
import net.minecraft.client.renderer.block.model.ModelResourceLocation;
import net.minecraft.item.Item;
import net.minecraft.util.ResourceLocation;
import net.minecraftforge.client.event.ModelRegistryEvent;
import net.minecraftforge.client.model.ModelLoader;
import net.minecraftforge.event.RegistryEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;
import net.minecraftforge.registries.IForgeRegistry;

/**
 * Classe d'initialisation pour les items de fruits du démon
 */
@Mod.EventBusSubscriber(modid = Reference.MOD_ID)
public class ModAkumaNoMi {

    ///////////////////////////////////////////////////////////////////////////
    // FRUITS DU DÉMON
    ///////////////////////////////////////////////////////////////////////////

    // Paramécia
    public static final Item BUKU_BUKU_FRUIT = new BukuBukuFruitItem();
    public static final Item MIRA_MIRA_FRUIT = new MiraMiraFruitItem();
    public static final Item BANE_BANE_FRUIT = new BaneBaneFruitItem();
    public static final Item WAPU_WAPU_FRUIT = new WapuWapuFruitItem();

    // Zoan

    // Logia
    public static final Item MAGU_MAGU_FRUIT = new MaguMaguFruitItem();

    /**
     * Enregistre tous les items de fruits du démon
     */
    @SubscribeEvent
    public static void registerItems(final RegistryEvent.Register<Item> event) {
        IForgeRegistry<Item> registry = event.getRegistry();

        // Paramécia
        registry.register(BUKU_BUKU_FRUIT);
        registry.register(MIRA_MIRA_FRUIT);
        registry.register(BANE_BANE_FRUIT);
        registry.register(WAPU_WAPU_FRUIT);

        // Zoan

        // Logia
        registry.register(MAGU_MAGU_FRUIT);
    }

    /**
     * Enregistre les modèles pour les items de fruits du démon
     */
    @SubscribeEvent
    @SideOnly(Side.CLIENT)
    public static void registerModels(ModelRegistryEvent event) {

        // Paramécia
        registerItemRenderer(BUKU_BUKU_FRUIT);
        registerItemRenderer(MIRA_MIRA_FRUIT);
        registerItemRenderer(BANE_BANE_FRUIT);
        registerItemRenderer(WAPU_WAPU_FRUIT);

        // Zoan

        // Logia
        registerItemRenderer(MAGU_MAGU_FRUIT);
    }

    /**
     * Méthode utilitaire pour enregistrer le rendu d'un item
     */
    @SideOnly(Side.CLIENT)
    private static void registerItemRenderer(Item item) {
        ModelLoader.setCustomModelResourceLocation(
                item,
                0,
                new ModelResourceLocation(
                        new ResourceLocation(Reference.MOD_ID, item.getUnlocalizedName().substring(5)),
                        "inventory"
                )
        );
    }
}