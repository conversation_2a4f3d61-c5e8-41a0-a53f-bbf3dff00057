/*
 * Copyright (c) 2021.  Zom'
 * https://twitter.com/ZOm__YT
 */

package fr.minepiece.common.init;

import fr.minepiece.MinePiece;
import fr.minepiece.Reference;
import net.minecraftforge.common.config.Configuration;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;

import java.io.File;

public class ModConfiguration {

	public static File config_dir;
	public static Configuration config;

	//DESCRIPTIONS
	public static String HUMAN_DESC, FISHMAN_DESC, CYBORG_DESC, MINKS_DESC, GIANT_DESC, TONTATTA_DESC;

	//PASSIFS
	public static int human_health_boost_passive, cyborg_resistance_boost_passive, minks_speed_boost_passive;
	public static float fishman_speed_boost_passive;

	//POWER UPS
	public static int human_health_powerup, cyborg_resistance_powerup, minks_strength_powerup, fishman_strength_powerup;

	/* POWERS */
	//ENERGY COST
	public static int human_skill1_energy_cost, human_skill2_energy_cost, human_skill3_energy_cost;
	public static int fishman_skill1_energy_cost, fishman_skill2_energy_cost, fishman_skill3_energy_cost;
	public static int cyborg_skill1_energy_cost, cyborg_skill2_energy_cost, cyborg_skill3_energy_cost;
	public static int minks_skill1_energy_cost, minks_skill2_energy_cost, minks_skill3_energy_cost;
	public static int giant_skill1_energy_cost, giant_skill2_energy_cost, giant_skill3_energy_cost;
	public static int tontatta_skill1_energy_cost, tontatta_skill2_energy_cost, tontatta_skill3_energy_cost;
	// COOLDOWNS

	//HUMAN
	//FISHMAN
	public static int fishman_waterdrop_damage;
	public static int fishman_watershark_damage;
	public static int race_skill_cooldown;
	public static int energy_regen_delay;
	//CYBORG
	//MINKS
	public static float minks_skill2_damage;

	//XP
	public static int player_xp_level_2;

	public static int maxPlayerLevel;
	public static int maxJobLevel;

	public static void init(File file) {
		config = new Configuration(file);
		String category = "races";

		config.addCustomCategoryComment(category, "Config des races");
		HUMAN_DESC = config.getString("Description humain", category, "", "Description affichée dans le gui de choix des races");
		FISHMAN_DESC = config.getString("Description homme-poisson", category, "", "Description affichée dans le gui de choix des races");
		CYBORG_DESC = config.getString("Description cyborg", category, "", "Description affichée dans le gui de choix des races");
		MINKS_DESC = config.getString("Description minks", category, "", "Description affichée dans le gui de choix des races");
		GIANT_DESC = config.getString("Description geant", category, "", "Description affichée dans le gui de choix des races");
		TONTATTA_DESC = config.getString("Description tontatta", category, "", "Description affichée dans le gui de choix des races");

		category = "races passifs";
		human_health_boost_passive = config.getInt("Boost de vie (Passif humain)", category, 6, 0, 1000, "Bonus de vie passif donné aux humains");
		cyborg_resistance_boost_passive = config.getInt("Effet resistance (Passif cyborg)", category, 1, 1, 5, "Niveau de résistance passif donné aux cyborgs");
		minks_speed_boost_passive = config.getInt("Effet speed (Passif minks)", category, 1, 1, 5, "Niveau de speed passif donné aux minks");
		fishman_speed_boost_passive = config.getFloat("Vitesse des hommes-poisson dans l'eau", category, 3f, 1, 10f, "");

		category = "races power ups";
		human_health_powerup = config.getInt("Boost de vie (PowerUP humain)", category, 8, 0, 1000, "Bonus de vie donné aux humains (powerup)");
		cyborg_resistance_powerup = config.getInt("Effet resistance (PowerUP cyborg)", category, 2, 1, 5, "Niveau de résistance donné aux cyborgs (powerup)");
		fishman_strength_powerup = config.getInt("Effet de force (PowerUP homme-poisson)", category, 1, 1, 5, "Niveau de force donné aux hommes-poisson (powerup)");
		minks_strength_powerup = config.getInt("Effet de force (PowerUP minks)", category, 1, 1, 5, "Niveau de force donné aux minks (powerup)");

		category = "races pouvoirs";
		config.addCustomCategoryComment(category, "Pouvoirs des races (Actifs)");
		race_skill_cooldown = config.getInt("Délai entre chaque utilisation de pouvoirs (en ms)", category, 500, 10, 100000, "");
		energy_regen_delay = config.getInt("Délai entre chaque regen de point d'energie (en ms)", category, 6000, 10, 100000, "");
		category = "races energy costs";
		human_skill1_energy_cost = config.getInt("Cout d'energie du pouvoir 1 (Humain)", category, 15, 0, 100, "");
		human_skill2_energy_cost = config.getInt("Cout d'energie du pouvoir 2 (Humain)", category, 75, 0, 100, "");
		human_skill3_energy_cost = config.getInt("Cout d'energie du pouvoir 3 (Humain)", category, 35, 0, 100, "");

		fishman_skill1_energy_cost = config.getInt("Cout d'energie du pouvoir 1 (Homme-Poisson)", category, 30, 0, 100, "");
		fishman_skill2_energy_cost = config.getInt("Cout d'energie du pouvoir 2 (Homme-Poisson)", category, 55, 0, 100, "");
		fishman_skill3_energy_cost = config.getInt("Cout d'energie du pouvoir 3 (Homme-Poisson)", category, 75, 0, 100, "");

		cyborg_skill1_energy_cost = config.getInt("Cout d'energie du pouvoir 1 (Cyborg)", category, 30, 0, 100, "");
		cyborg_skill2_energy_cost = config.getInt("Cout d'energie du pouvoir 2 (Cyborg)", category, 45, 0, 100, "");
		cyborg_skill3_energy_cost = config.getInt("Cout d'energie du pouvoir 3 (Cyborg)", category, 150, 0, 200, "");

		minks_skill1_energy_cost = config.getInt("Cout d'energie du pouvoir 1 (Minks)", category, 40, 0, 100, "");
		minks_skill2_energy_cost = config.getInt("Cout d'energie du pouvoir 2 (Minks)", category, 75, 0, 100, "");
		minks_skill3_energy_cost = config.getInt("Cout d'energie du pouvoir 3 (Minks)", category, 120, 0, 200, "");

		giant_skill1_energy_cost = config.getInt("Cout d'energie du pouvoir 1 (Geant)", category, 45, 0, 200, "");
		giant_skill2_energy_cost = config.getInt("Cout d'energie du pouvoir 2 (Geant)", category, 75, 0, 200, "");
		giant_skill3_energy_cost = config.getInt("Cout d'energie du pouvoir 3 (Geant)", category, 25, 0, 200, "");

		tontatta_skill1_energy_cost = config.getInt("Cout d'energie du pouvoir 1 (Tontatta)", category, 35, 0, 200, "");
		tontatta_skill2_energy_cost = config.getInt("Cout d'energie du pouvoir 2 (Tontatta)", category, 55, 0, 200, "");
		tontatta_skill3_energy_cost = config.getInt("Cout d'energie du pouvoir 3 (Tontatta)", category, 110, 0, 200, "");


		category = "human powers";
		category = "fishman powers";
		fishman_waterdrop_damage = config.getInt("Dégats des gouttes d'eau", category, 2, 1, 1000, "");
		fishman_watershark_damage = config.getInt("Dégats du requin d'eau", category, 7, 1, 1000, "");
		category = "cyborg powers";
		category = "minks powers";
		minks_skill2_damage = config.getInt("Dégats de l'orbe électrique", category, 2, 1, 100, "");

		category = "xp settings";
		player_xp_level_2 = config.getInt("Xp nécessaire au passage du niveau 2", category, 200, 1, 1000000, "");

		category = "level limits";
		maxPlayerLevel = config.getInt("Max Player Level", category, -1, -1, 1000, "Maximum level a player can reach");
		maxJobLevel = config.getInt("Max Job Level", category, -1, -1, 1000, "Maximum job level a player can reach");

		config.save();
	}

	public static void registerConfig(FMLPreInitializationEvent e) {
		config_dir = new File(e.getModConfigurationDirectory() + "/" + Reference.MOD_ID);
		config_dir.mkdirs();
		init(new File(config_dir.getPath(), Reference.MOD_ID + ".cfg"));
	}

}
