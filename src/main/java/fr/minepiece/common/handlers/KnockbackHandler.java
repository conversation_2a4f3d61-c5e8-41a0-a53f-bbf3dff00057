package fr.minepiece.common.handlers;

import fr.minepiece.Reference;
import fr.minepiece.common.items.weapons.IKnockbackWeapon;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.item.ItemStack;
import net.minecraftforge.event.entity.living.LivingKnockBackEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;

@Mod.EventBusSubscriber(modid = Reference.MOD_ID)
public class KnockbackHandler {

    @SubscribeEvent
    public static void onLivingKnockback(LivingKnockBackEvent event) {
        EntityLivingBase target = event.getEntityLiving();
        Entity attacker = event.getAttacker(); // The entity causing the knockback

        // Knockback calculations are server-side
        if (target.world.isRemote || attacker == null || !(attacker instanceof EntityLivingBase)) {
            return;
        }

        EntityLivingBase livingAttacker = (EntityLivingBase) attacker;
        ItemStack heldWeapon = livingAttacker.getHeldItemMainhand(); // Get the weapon used

        if (!heldWeapon.isEmpty() && heldWeapon.getItem() instanceof IKnockbackWeapon) {
            IKnockbackWeapon knockbackWeapon = (IKnockbackWeapon) heldWeapon.getItem();

            // Get the extra strength from the weapon
            float extraStrength = knockbackWeapon.getKnockback(heldWeapon, livingAttacker, target) *0.15f;

            // Get the current strength (includes base 0.4F + potentially other modifiers)
            float currentStrength = event.getStrength();

            // Add our extra strength
            float newStrength = currentStrength + extraStrength;

            // Set the modified strength back to the event
            event.setStrength(newStrength);
        }
    }
}
