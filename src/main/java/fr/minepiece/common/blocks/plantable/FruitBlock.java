package fr.minepiece.common.blocks.plantable;

import fr.minepiece.common.api.jobs.MinePieceJobs;
import fr.minepiece.common.api.utils.PlayerUtils;
import fr.minepiece.common.capability.mpdata.IMinePieceData;
import fr.minepiece.common.capability.mpdata.MinePieceDataStorage;
import fr.minepiece.common.items.jobs.cook.CookToolItem;
import net.minecraft.block.Block;
import net.minecraft.block.material.Material;
import net.minecraft.block.state.IBlockState;
import net.minecraft.entity.item.EntityItem;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.util.EnumFacing;
import net.minecraft.util.EnumHand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.world.World;

import java.util.UUID;

/**
 * Classe pour les fruits et légumes récoltables par les cuisiniers
 * Avec trois stades de croissance: coupé (cut) -> immature -> mature
 */
public class FruitBlock extends AbstractFlowerBlock {

    // Item de nourriture associé à ce fruit
    private Item foodItem;

    // Item qui peut être récupéré au stade immature (optionnel)
    private Item immatureFoodItem;

    // Détermine si ce bloc est immature (stade intermédiaire)
    private final boolean isImmature;

    // Le bloc qui remplace ce bloc après récolte (cut pour mature, rien pour immature)
    private final Block nextStateBlock;

    // Le bloc mature qui sera créé après la croissance du bloc immature
    private final String matureBlockName;

    // Le bloc cut qui sera placé après récolte du bloc mature
    private final Block cutStateBlock;

    /**
     * Constructeur pour fruit/légume coupé (non mature)
     */
    public FruitBlock(String name, int jobXpAmount) {
        super(name, Material.PLANTS, jobXpAmount, MinePieceJobs.COOK,
                name.replace("_cut", "_immature")); // Le bloc immature remplace le bloc coupé
        this.isImmature = false;
        this.nextStateBlock = null;
        this.matureBlockName = null;
        this.cutStateBlock = null;
        setHardness(0.9F); // Temps de base pour casser le bloc
    }

    /**
     * Constructeur pour fruit/légume immature (stade intermédiaire)
     */
    public FruitBlock(String name, int jobXpAmount, FruitBlock cutState) {
        super(name, Material.PLANTS, jobXpAmount, MinePieceJobs.COOK, true,
                cutState, cutState.getRegistryName().getResourcePath());
        this.isImmature = true;
        this.nextStateBlock = cutState; // Après récolte, remplace par le bloc cut
        this.matureBlockName = name.replace("_immature", "");
        this.cutStateBlock = null;
        setHardness(1.0F); // Intermédiaire entre coupé et mature
    }

    /**
     * Constructeur pour fruit/légume mature
     */
    public FruitBlock(String name, int jobXpAmount, FruitBlock immatureState, Item foodItem) {
        super(name, Material.PLANTS, jobXpAmount, MinePieceJobs.COOK, true,
                immatureState, immatureState.getRegistryName().getResourcePath());
        this.isImmature = false;
        this.foodItem = foodItem;

        this.cutStateBlock = immatureState.nextStateBlock; // Le bloc cut est stocké dans le nextStateBlock de l'immature
        this.nextStateBlock = this.cutStateBlock; // Utilise le bloc cut pour remplacer
        this.matureBlockName = null;
        setHardness(1.1F); // Un peu plus dur pour les fruits matures
    }

    @Override
    protected boolean isValidTool(Item tool) {
        return tool instanceof CookToolItem;
    }

    /**
     * Définit l'item de nourriture associé à ce fruit
     * @param foodItem L'item de nourriture
     * @return this pour chaining
     */
    public FruitBlock setFoodItem(Item foodItem) {
        this.foodItem = foodItem;
        return this;
    }

    /**
     * Définit l'item de nourriture qui peut être récupéré au stade immature
     * @param immatureFoodItem L'item de nourriture du stade immature
     * @return this pour chaining
     */
    public FruitBlock setImmatureFoodItem(Item immatureFoodItem) {
        this.immatureFoodItem = immatureFoodItem;
        return this;
    }

    /**
     * Retourne l'item de nourriture associé
     */
    public Item getFoodItem() {
        return this.foodItem;
    }

    /**
     * Retourne l'item de nourriture pour le stade immature, ou null si non défini
     */
    public Item getImmatureFoodItem() {
        return this.immatureFoodItem;
    }

    @Override
    protected Item getHarvestDrop() {
        // Si c'est un bloc immature et qu'un item immature est défini, on le retourne
        if (isImmature && immatureFoodItem != null) {
            return this.immatureFoodItem;
        }
        // Sinon pour les blocs immatures sans item défini, pas de drop
        else if (isImmature) {
            return null;
        }
        // Et pour les blocs matures, retourne l'item standard
        return this.foodItem;
    }

    @Override
    public boolean onBlockActivated(World worldIn, BlockPos pos, IBlockState state, EntityPlayer playerIn,
                                    EnumHand hand, EnumFacing facing, float hitX, float hitY, float hitZ) {
        if (worldIn.isRemote) return true;

        // Vérifie si le bloc est un stade coupé (non mature) - comportement standard
        if (!isMature) {
            return false;
        }

        ItemStack heldItem = playerIn.getHeldItem(hand);
        Item tool = heldItem.getItem();

        // Vérification si l'outil est compatible avec le bloc
        if (!isValidTool(tool)) {
            return false;
        }

        // Vérification si le joueur a le bon métier et niveau
        IMinePieceData mpData = playerIn.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);
        if (mpData == null || mpData.getJob() != requiredJob ||
                !(tool instanceof CookToolItem) ||
                !((CookToolItem) tool).isUsableAtLevel(mpData.getJobLevel())) {
            return false;
        }

        // Utilisez l'ID du joueur pour suivre la récolte
        UUID playerUUID = playerIn.getUniqueID();

        // Vérifie si le joueur est déjà en train de récolter ce bloc
        if (isPlayerHarvesting(playerUUID, pos)) {
            long startTime = getHarvestStartTime(playerUUID, pos);
            long currentTime = System.currentTimeMillis();

            // Si le temps de récolte est écoulé
            if (currentTime - startTime >= HARVEST_TIME) {
                // Si c'est un bloc immature, comportement spécial
                if (isImmature) {
                    completeImmatureHarvest(worldIn, pos, state, playerIn, (CookToolItem) tool, mpData);
                } else {
                    // Récolte terminée pour un bloc mature - comportement standard
                    completeMatureHarvest(worldIn, pos, state, playerIn, (CookToolItem) tool, mpData);
                }
                // Supprime l'entrée du joueur pour ce bloc
                removeHarvestEntry(playerUUID, pos);
            }
        } else {
            // Commence la récolte
            startHarvest(playerUUID, pos);
            // Envoyer un message au joueur pour indiquer que la récolte a commencé
            playerIn.sendMessage(new TextComponentString("§aRécolte en cours..."));
        }

        return true;
    }

    /**
     * Gère la récolte spécifique pour les blocs immatures
     */
    private void completeImmatureHarvest(World worldIn, BlockPos pos, IBlockState state,
                                         EntityPlayer playerIn, CookToolItem tool, IMinePieceData mpData) {
        // Ajoute l'XP de job (moitié de l'XP normale pour immature)
        if (playerIn instanceof EntityPlayerMP) {
            PlayerUtils.addJobXpToPlayer((EntityPlayerMP) playerIn, jobXpAmount / 2 + tool.getXpBonus() / 2);
        }

        // Endommage l'outil
        playerIn.getHeldItemMainhand().damageItem(1, playerIn);

        // Si un item immature est défini, on le drop
        if (immatureFoodItem != null) {
            ItemStack itemStack = new ItemStack(immatureFoodItem);
            worldIn.spawnEntity(new EntityItem(worldIn, pos.getX() + 0.5, pos.getY() + 0.5, pos.getZ() + 0.5, itemStack));
            playerIn.sendMessage(new TextComponentString("§aRécolte terminée! Vous avez obtenu un fruit immature."));
        } else {
            // Message de récolte réussie mais sans fruit
            playerIn.sendMessage(new TextComponentString("§aRécolte terminée! Le fruit n'était pas encore mûr."));
        }

        // Remplace le bloc immature par sa version coupée
        if (nextStateBlock != null) {
            worldIn.setBlockState(pos, nextStateBlock.getDefaultState());

            // Programme la repousse du bloc coupé vers le bloc immature, puis mature
            scheduleGrowth(worldIn, pos, nextStateBlock);
        } else {
            // Si le bloc immature n'a pas d'état coupé (ne devrait pas arriver)
            worldIn.setBlockToAir(pos);
        }
    }

    /**
     * Gère la récolte pour les blocs matures
     */
    private void completeMatureHarvest(World worldIn, BlockPos pos, IBlockState state,
                                       EntityPlayer playerIn, CookToolItem tool, IMinePieceData mpData) {

        if (playerIn instanceof EntityPlayerMP) {
            PlayerUtils.addJobXpToPlayer((EntityPlayerMP) playerIn, jobXpAmount + tool.getXpBonus());
            mpData.clientSync((EntityPlayerMP) playerIn);
        }

        // Endommage l'outil
        playerIn.getHeldItemMainhand().damageItem(1, playerIn);

        // Message de récolte réussie
        playerIn.sendMessage(new TextComponentString("§aRécolte terminée!"));

        // Drop le fruit
        if (foodItem != null) {
            ItemStack itemStack = new ItemStack(foodItem);
            worldIn.spawnEntity(new EntityItem(worldIn, pos.getX() + 0.5, pos.getY() + 0.5, pos.getZ() + 0.5, itemStack));
        }

        // Remplace le bloc mature directement par sa version cut
        if (nextStateBlock != null) {
            worldIn.setBlockState(pos, nextStateBlock.getDefaultState());

            // Programme la repousse du bloc coupé
            scheduleGrowth(worldIn, pos, nextStateBlock);
        } else {
            worldIn.setBlockToAir(pos);
        }
    }

    @Override
    public void onBlockAdded(World worldIn, BlockPos pos, IBlockState state) {
        super.onBlockAdded(worldIn, pos, state);

        if (!worldIn.isRemote) {
            if (!isMature) {
                // Programme la croissance du bloc coupé
                scheduleGrowth(worldIn, pos, this);
            } else if (isImmature) {
                // Programme la croissance du bloc immature vers le bloc mature
                scheduleGrowthToMature(worldIn, pos, this);
            }
        }
    }

    /**
     * Programme la croissance d'un bloc immature vers son état mature
     */
    protected void scheduleGrowthToMature(World world, BlockPos pos, Block currentBlock) {
        if (isImmature && matureBlockName != null && !matureBlockName.isEmpty()) {
            // Vérifie si le TileEntity existe déjà
            FlowerTileEntity flowerTE = getFruitTileEntity(world, pos);
            if (flowerTE != null) {
                // Définit le temps de croissance
                int growthTime = DEFAULT_GROWTH_TIME; // ou DEFAULT_GROWTH_TIME en production
                flowerTE.setGrowthTime(growthTime);
                flowerTE.setNextGrowthBlock(matureBlockName);

                // Forcer la synchronisation avec les clients
                world.notifyBlockUpdate(pos, world.getBlockState(pos), world.getBlockState(pos), 3);
                world.markChunkDirty(pos, flowerTE);
            }
        }
    }

    /**
     * Utilitaire pour obtenir ou créer le TileEntity associé
     */
    private FlowerTileEntity getFruitTileEntity(World world, BlockPos pos) {
        // Vérifie si le TileEntity existe déjà
        net.minecraft.tileentity.TileEntity existingTE = world.getTileEntity(pos);

        // Si le TileEntity existe mais n'est pas un FlowerTileEntity, il faut le détruire
        if (existingTE != null && !(existingTE instanceof FlowerTileEntity)) {
            world.removeTileEntity(pos);
            existingTE = null;
        }

        // Créer ou obtenir le tile entity
        FlowerTileEntity flowerTE;
        if (existingTE instanceof FlowerTileEntity) {
            flowerTE = (FlowerTileEntity) existingTE;
        } else {
            // Si le TileEntity n'existe pas ou n'est pas du bon type, créez-en un nouveau
            flowerTE = new FlowerTileEntity();
            world.setTileEntity(pos, flowerTE);
        }

        return flowerTE;
    }

    @Override
    public Item getItemDropped(IBlockState state, java.util.Random rand, int fortune) {
        // Retourne null pour éviter les drops automatiques de Minecraft
        // Notre système de drop personnalisé gère ça dans completeHarvest
        return null;
    }
}