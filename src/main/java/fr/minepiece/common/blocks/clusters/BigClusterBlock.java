package fr.minepiece.common.blocks.clusters;

import fr.minepiece.common.utils.ClusterConfigManager;
import net.minecraft.block.SoundType;
import net.minecraft.block.material.Material;
import net.minecraft.item.Item;
import net.minecraft.util.math.AxisAlignedBB;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Classe pour les grands clusters de minerais
 */
public class BigClusterBlock extends AbstractClusterBlock {

    // Bounding boxes par défaut pour les grands clusters
    private static final List<AxisAlignedBB> DEFAULT_BOUNDING_BOXES = createDefaultBoundingBoxes();

    // L'item à dropper lors de la récolte
    private Item dropItem;

    /**
     * Constructeur pour les grands clusters
     */
    public BigClusterBlock(Material material, SoundType soundType, String blockName,
                           String oreType, int jobXpAmount, String mediumBlockName, Item dropItem) {
        super(material, soundType, blockName, oreType, jobXpAmount, false,
                mediumBlockName, null, DEFAULT_BOUNDING_BOXES);

        this.dropItem = dropItem;
    }

    /**
     * Création des bounding boxes par défaut pour les grands clusters
     */
    private static List<AxisAlignedBB> createDefaultBoundingBoxes() {
        List<AxisAlignedBB> boundingBoxes = new ArrayList<>();
        float[][] elements = {
                {9.5f, -0.5f, 13f, 13.5f, 8.5f, 16f},
                {8f, 3.25f, 2.5f, 12f, 15.25f, 6.5f},
                {0.25f, 1.5f, 6.75f, 5.25f, 11.5f, 12.75f},
                {5.57611f, 1.38949f, 6f, 18.57611f, 6.38949f, 12f},
                {11.19552f, 1.5f, 9.46927f, 14.19552f, 15.5f, 13.46927f},
                {1.5f, -2.25f, 11.75f, 7.5f, 13.75f, 15.75f},
                {2.04172f, -0.75f, -0.06075f, 12.04172f, 21.25f, 8.93925f},
                {4f, -0.75f, 4f, 15f, 32f, 14f}
        };

        for (float[] element : elements) {
            boundingBoxes.add(new AxisAlignedBB(
                    element[0] / 16.0, element[1] / 16.0, element[2] / 16.0,
                    element[3] / 16.0, element[4] / 16.0, element[5] / 16.0
            ));
        }
        return boundingBoxes;
    }

    /**
     * Définit l'item à dropper lors de la récolte
     */
    public BigClusterBlock setDropItem(Item item) {
        this.dropItem = item;
        return this;
    }

    @Override
    protected Item getHarvestDrop() {
        return dropItem;
    }

    @Override
    protected int getDropCount() {
        Random rand = new Random();
        int min = ClusterConfigManager.getBigMinDropCount();
        int max = ClusterConfigManager.getBigMaxDropCount();

        // Protection contre une configuration invalide
        if (min > max) min = max;

        return min + rand.nextInt(max - min + 1);
    }
}