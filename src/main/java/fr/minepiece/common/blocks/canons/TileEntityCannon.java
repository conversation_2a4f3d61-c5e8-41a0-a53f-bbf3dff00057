package fr.minepiece.common.blocks.canons;

import fr.minepiece.common.entity.projectile.EntityCannonBall;
import net.minecraft.block.state.IBlockState;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.init.SoundEvents;
import net.minecraft.nbt.NBTTagCompound;
import net.minecraft.network.NetworkManager;
import net.minecraft.network.play.server.SPacketUpdateTileEntity;
import net.minecraft.tileentity.TileEntity;
import net.minecraft.util.EnumFacing;
import net.minecraft.util.EnumParticleTypes;
import net.minecraft.util.ITickable;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;
import net.minecraft.world.WorldServer;
import software.bernie.geckolib3.core.IAnimatable;
import software.bernie.geckolib3.core.PlayState;
import software.bernie.geckolib3.core.builder.AnimationBuilder;
import software.bernie.geckolib3.core.controller.AnimationController;
import software.bernie.geckolib3.core.event.predicate.AnimationEvent;
import software.bernie.geckolib3.core.manager.AnimationData;
import software.bernie.geckolib3.core.manager.AnimationFactory;

import javax.annotation.Nullable;
import java.util.UUID;

public class TileEntityCannon extends TileEntity implements IAnimatable, ITickable {

    // GeckoLib Animation Boilerplate
    private final AnimationFactory factory = new AnimationFactory(this);

    private CannonMaterial material = CannonMaterial.STEEL; // Default
    private CannonLength length = CannonLength.SMALL;   // Default

    // --- State Fields ---
    @Nullable
    private UUID controllingPlayerUUID = null;
    private float cannonYaw = 0.0f;
    private float cannonPitch = 0.0f;
    private boolean needsSync = false;
    private boolean justActedUpon = false; // Client-side heuristic flag

    // Animation trigger state
    private boolean isShooting = false;
    private int shootAnimationTicks = 0;
    private static final int SHOOT_ANIMATION_LENGTH = 30; // 1.5 seconds * 20 ticks/sec

    private boolean isFiringSequenceActive = false; // Is the load-up/delay happening?
    private int shotDelayTicks = -1; // Ticks until the shot actually happens (-1 means inactive)
    private static final int SHOT_DELAY = 13; // 1 second delay before projectile spawn

    private int cooldownTicks = 0; // Add cooldown timer
    private static final int MAX_COOLDOWN = 40; // 2 seconds cooldown
    private boolean hasGunpowder = false;
    private boolean hasCannonball = false;

    // --- Animation Handling ---
    private <E extends TileEntity & IAnimatable> PlayState predicate(AnimationEvent<E> event) {
        if (this.isShooting) {
            // Play shoot animation ONCE when triggered
            event.getController().setAnimation(new AnimationBuilder().addAnimation("animation.Canon_long.shoot", true));
            return PlayState.CONTINUE;
        } else {
            // No looping idle animation defined, so stop controller
            return PlayState.STOP; // Or play an actual idle animation if you have one
        }
    }

    @Override
    public void registerControllers(AnimationData data) {
        // Register an animation controller. "controller" is just an ID.
        data.addAnimationController(new AnimationController<>(this, "controller", 0, this::predicate));
        // You can have multiple controllers for different animation sets.
        // The '0' is the transition time in ticks between animations.
    }

    public IBlockState getCannonBlockState() {
        if (this.world != null && this.pos != null) {
            return this.world.getBlockState(this.pos);
        }
        // Return a default state if world/pos isn't available yet (should be rare)
        // Ensure ModBlocks.CANNON is initialized before TEs are loaded
        return fr.minepiece.common.init.ModBlocks.CANNON.getDefaultState(); // Replace with your actual block instance access
    }

    @Nullable
    public EntityPlayer getControllingPlayer(World world) {
        if (world == null || this.controllingPlayerUUID == null) {
            return null;
        }
        return world.getPlayerEntityByUUID(this.controllingPlayerUUID);
    }

    public void setControllingPlayer(@Nullable UUID playerUUID) {
        if (this.controllingPlayerUUID != playerUUID) { // Check if changed (allows null comparison)
            this.controllingPlayerUUID = playerUUID;
            this.needsSync = true;
            this.markDirty(); // Mark for saving and update
        }
    }

    public void triggerShootAnimation() {
        if (!isShooting) { // Prevent re-triggering while already shooting
            this.isShooting = true;
            this.shootAnimationTicks = SHOOT_ANIMATION_LENGTH; // Set duration
            this.needsSync = true; // Sync animation state change
            this.markDirty();
        }
    }

    /**
     * Starts the firing sequence: triggers animation, sets delays and cooldowns.
     * Does NOT spawn the projectile immediately.
     * @param controller The player initiating the sequence (can be null).
     * @return True if the sequence was successfully started, false otherwise (e.g., on cooldown).
     */
    public boolean triggerFireSequence(@Nullable EntityPlayer controller) {
        if (world.isRemote || this.cooldownTicks > 0 || this.isFiringSequenceActive || !this.hasGunpowder || !this.hasCannonball) {
            // Don't start on client, or if main cooldown active, or if already in firing sequence
            return false;
        }

        // Start Sequence
        this.isFiringSequenceActive = true;
        this.shotDelayTicks = SHOT_DELAY; // Set the delay until the shot
        this.cooldownTicks = MAX_COOLDOWN; // Set the main cooldown preventing next sequence start

        // Trigger Animation Phase for visuals
        this.isShooting = true;
        this.shootAnimationTicks = SHOOT_ANIMATION_LENGTH; // Set visual duration timer
        this.needsSync = true; // Tell clients to start animating

        this.setHasCannonball(false);
        this.setHasGunpowder(false);

        this.markDirty(); // Mark TE for saving state
        return true; // Sequence started successfully
    }

    // --- ITickable Implementation (Server-side Logic) ---
    @Override
    public void update() {
        if (!world.isRemote) { // Server side
            this.justActedUpon = false; // Reset server flag
            boolean controllerStillValid = false;
            if (this.controllingPlayerUUID != null) {
                EntityPlayer controller = getControllingPlayer(world);
                if (controller != null && !controller.isDead && controller.getDistanceSq(getPos()) < 10 * 10) { // Check distance (10 blocks)
                    if (controller.isSneaking()) {
                        // Dismount if sneaking
                        setControllingPlayer(null);
                        // Send message from block activate now
                    } else {
                        // Player is valid and controlling
                        controllerStillValid = true;
                        float targetYaw = controller.rotationYaw; // Use player's direct yaw
                        float targetPitch = controller.rotationPitch; // Use player's direct pitch

                        IBlockState blockState = this.world.getBlockState(this.pos);
                        EnumFacing facing = EnumFacing.NORTH; // default
                        if (blockState.getBlock() instanceof BlockCannon && blockState.getPropertyKeys().contains(BlockCannon.FACING)) {
                            facing = blockState.getValue(BlockCannon.FACING);
                        }

                        float blockAngleOffset = facing.getOpposite().getHorizontalAngle();

                        // Clamp Pitch (e.g., -30 to +45 degrees)
                        targetPitch = MathHelper.clamp(targetPitch, -30.0f, 45.0f);
                        targetYaw = MathHelper.wrapDegrees(controller.rotationYaw - blockAngleOffset); // Normalize to -180 to +180
                        targetYaw = MathHelper.clamp(targetYaw, -5.0f, 5.0f);

                        // Update if changed significantly (avoids excessive syncing)
                        if (Math.abs(targetYaw - this.cannonYaw) > 0.1f || Math.abs(targetPitch - this.cannonPitch) > 0.1f) {
                            this.cannonYaw = targetYaw;
                            this.cannonPitch = targetPitch;
                            this.needsSync = true;
                        }
                    }
                }

                if (!controllerStillValid) {
                    // Player is no longer valid (logged off, too far, dead, or started sneaking)
                    setControllingPlayer(null);
                }
            }

            // Handle shooting animation timer

            boolean needsSave = false;

            if (this.cooldownTicks > 0) {
                this.cooldownTicks--;
                this.markDirty(); // Need to save cooldown potentially
            }

            // Handle Firing Sequence Delay
            if (this.isFiringSequenceActive) {
                this.shotDelayTicks--;
                if (this.shotDelayTicks <= 0) {
                    // --- Time to fire! ---
                    EntityPlayer controller = getControllingPlayer(world); // Get controller if exists
                    spawnCannonball(controller);
                    this.isFiringSequenceActive = false; // End the active firing sequence
                    this.shotDelayTicks = -1; // Reset delay timer
                }
                needsSave = true; // Need to save countdown progress
            }

            // Handle Animation Phase Timer (visual duration)
            if (this.isShooting) {
                this.shootAnimationTicks--;
                if (this.shootAnimationTicks <= 0) {
                    this.isShooting = false;
                    this.needsSync = true; // Sync end of animation phase
                }
                needsSave = true;
            }

            // If state changed, send update packet to clients
            if (this.needsSync) {
                world.notifyBlockUpdate(pos, world.getBlockState(pos), world.getBlockState(pos), 3);
                this.needsSync = false;
            }
            if(needsSave) {
                this.markDirty();
            }
        } else {
            // Client side - reset heuristic flag after a short delay maybe? Or just once per tick.
            this.justActedUpon = false;
        }
    }

    // --- NBT & Synchronization ---

    @Override
    public NBTTagCompound writeToNBT(NBTTagCompound compound) {
        super.writeToNBT(compound);
        compound.setFloat("CannonYaw", this.cannonYaw);
        compound.setFloat("CannonPitch", this.cannonPitch);
        if (this.controllingPlayerUUID != null) {
            compound.setUniqueId("ControllerUUID", this.controllingPlayerUUID);
        }
        compound.setBoolean("IsShooting", this.isShooting);
        compound.setInteger("ShootTicks", this.shootAnimationTicks);
        compound.setInteger("Cooldown", this.cooldownTicks); // Save cooldown
        compound.setString("CannonMaterial", this.material == null ? CannonMaterial.STEEL.getName() : this.material.getName());
        compound.setString("CannonLength", this.length == null ? CannonLength.SMALL.getName() : this.length.getName());
        compound.setBoolean("HasGunpowder", this.hasGunpowder);
        compound.setBoolean("HasCannonball", this.hasCannonball);
        return compound;
    }

    @Override
    public void readFromNBT(NBTTagCompound compound) {
        super.readFromNBT(compound);
        this.cannonYaw = compound.getFloat("CannonYaw");
        this.cannonPitch = compound.getFloat("CannonPitch");
        if (compound.hasUniqueId("ControllerUUID")) {
            this.controllingPlayerUUID = compound.getUniqueId("ControllerUUID");
        } else {
            this.controllingPlayerUUID = null;
        }
        this.isShooting = compound.getBoolean("IsShooting");
        this.shootAnimationTicks = compound.getInteger("ShootTicks");
        this.cooldownTicks = compound.getInteger("Cooldown"); // Load cooldown
        this.material = CannonMaterial.fromName(compound.getString("CannonMaterial"));
        this.length = CannonLength.fromName(compound.getString("CannonLength"));
        this.hasGunpowder = compound.getBoolean("HasGunpowder");
        this.hasCannonball = compound.getBoolean("HasCannonball");
    }

    // Sync packet on block update
    @Override
    public SPacketUpdateTileEntity getUpdatePacket() {
        return new SPacketUpdateTileEntity(this.pos, 3, this.getUpdateTag()); // Use action number 1 (or any)
    }

    @Override
    public NBTTagCompound getUpdateTag() {
        return writeToNBT(new NBTTagCompound()); // Write all NBT for simplicity, can optimize later
    }

    // Receive sync packet
    @Override
    public void onDataPacket(NetworkManager net, SPacketUpdateTileEntity pkt) {
        handleUpdateTag(pkt.getNbtCompound());
        // Trigger a visual update on the client
        // World world = Minecraft.getMinecraft().world; // Doesn't work here
        // if (world != null) { world.markBlockRangeForRenderUpdate(pos, pos); }
        // This ^ often doesn't work reliably inside here. The renderer should pick up changes.
    }

    @Override
    public void handleUpdateTag(NBTTagCompound tag) {
        readFromNBT(tag);
        this.justActedUpon = true;
    }

    /**
     * Spawns the actual cannonball entity and plays firing effects.
     * Called internally after the shotDelayTicks runs out.
     * @param controller The player who initiated the shot (can be null).
     */
    private void spawnCannonball(@Nullable EntityPlayer controller) {

        IBlockState blockState = this.getCannonBlockState();
        CannonLength length = getCannonLength();

        EnumFacing facing = EnumFacing.NORTH; // default
        if (blockState.getBlock() instanceof BlockCannon && blockState.getPropertyKeys().contains(BlockCannon.FACING)) {
            facing = blockState.getValue(BlockCannon.FACING);
        }
        float blockAngleOffset = facing.getOpposite().getHorizontalAngle();
        float effectiveYaw = this.cannonYaw + blockAngleOffset;

        // --- Calculate Spawn Position & Velocity ---
        float yawRad = effectiveYaw * (float)Math.PI / 180.0F;
        float pitchRad = this.cannonPitch * (float)Math.PI / 180.0F;
        float fwdX = -MathHelper.sin(yawRad) * MathHelper.cos(pitchRad);
        float fwdY = -MathHelper.sin(pitchRad);
        float fwdZ = MathHelper.cos(yawRad) * MathHelper.cos(pitchRad);
        Vec3d forwardVector = new Vec3d(fwdX, fwdY, fwdZ).normalize();
        double spawnOffsetDist = 1.5D;
        Vec3d cannonCenter = new Vec3d(this.pos.getX() + 0.5, this.pos.getY() + 0.8, this.pos.getZ() + 0.5);
        Vec3d spawnPos = cannonCenter.add(forwardVector.scale(spawnOffsetDist));
        float speed = length.getRangeModifier() * material.getRangeModifier();
        float inaccuracy = 0.0F;

        // --- Spawn Projectile ---
        EntityCannonBall cannonball = new EntityCannonBall(material, length, world, spawnPos.x, spawnPos.y, spawnPos.z);
        if (controller != null) {
            cannonball.setThrower(controller);
        }
        cannonball.shoot(forwardVector.x, forwardVector.y, forwardVector.z, speed, inaccuracy);
        world.spawnEntity(cannonball);

        // --- Effects (Now happen AT the moment of firing) ---
        // Sound
        world.playSound(null, this.pos, SoundEvents.ENTITY_GENERIC_EXPLODE, SoundCategory.BLOCKS, 2.0F, 1.0F / (world.rand.nextFloat() * 0.4F + 0.8F));
        // Muzzle Flash Particle Example
        if (this.world instanceof WorldServer) {
            ((WorldServer)this.world).spawnParticle(
                    EnumParticleTypes.LAVA, // Use LAVA or FLAME for muzzle flash effect
                    spawnPos.x, spawnPos.y, spawnPos.z,    // Spawn at barrel end
                    15,                                 // Particle count
                    0.2D, 0.2D, 0.2D,                   // Spread (dX, dY, dZ)
                    0.0D                                // Speed
            );
            ((WorldServer)this.world).spawnParticle(
                    EnumParticleTypes.SMOKE_LARGE, // Large smoke puff
                    spawnPos.x, spawnPos.y, spawnPos.z,
                    10, 0.3D, 0.3D, 0.3D, 0.0D
            );
        }
    }

    public void setHasGunpowder(boolean hasGunpowder) {
        if (this.hasGunpowder != hasGunpowder) {
            this.hasGunpowder = hasGunpowder;
            this.needsSync = true;
            this.markDirty();
        }
    }

    public void setHasCannonball(boolean hasCannonball) {
        if (this.hasCannonball != hasCannonball) {
            this.hasCannonball = hasCannonball;
            this.needsSync = true;
            this.markDirty();
        }
    }

    // NEW Getters for loading state
    public boolean hasGunpowder() {
        return this.hasGunpowder;
    }

    public boolean hasCannonball() {
        return this.hasCannonball;
    }

    public CannonMaterial getCannonMaterial() {
        return this.material;
    }

    public CannonLength getCannonLength() {
        return this.length;
    }

    public void setMaterial(CannonMaterial material) {
        this.material = material;
    }

    public void setLength(CannonLength length) {
        this.length = length;
    }

    public boolean isOnCooldown() { return this.cooldownTicks > 0; } // Helper for feedback

    @Override
    public AnimationFactory getFactory() {
        return this.factory;
    }

    public float getCannonYaw() { return this.cannonYaw; }
    public float getCannonPitch() { return this.cannonPitch; }

    public boolean isControlled() { return this.controllingPlayerUUID != null; }
    public boolean isController(UUID uuid) { return this.controllingPlayerUUID != null && this.controllingPlayerUUID.equals(uuid); }
    public boolean isControlledBySomeoneOrActedUpon() { return isControlled() || justActedUpon; }
}
