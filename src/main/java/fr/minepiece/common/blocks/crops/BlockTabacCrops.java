package fr.minepiece.common.blocks.crops;

import fr.minepiece.common.api.jobs.MinePieceJobs;
import fr.minepiece.common.init.ModItems;
import fr.minepiece.common.init.ModSeeds;
import fr.minepiece.common.items.jobs.medic.tools.MedicToolItem;
import net.minecraft.item.Item;

public class BlockTabacCrops extends AbstractCropBlock {

    public BlockTabacCrops(String name, int jobXpAmount) {
        super(name, jobXpAmount, MedicToolItem.class, 3, MinePieceJobs.COOK);
    }

    @Override
    protected Item getSeed() {
        return ModSeeds.TABAC_SEED;
    }

    @Override
    protected Item getCrop() {
        return ModItems.TABAC;
    }

    /**
     * Exemple de surcharge de méthode si besoin d'un comportement spécifique
     * Pour les tomates, on augmente légèrement la chance de recevoir des graines
     */
    @Override
    protected float getAdditionalSeedDropChance() {
        return 0.10f;
    }
}