package fr.minepiece.common.blocks.crops;

import fr.minepiece.common.api.jobs.MinePieceJobs;
import fr.minepiece.common.init.ModFoods;
import fr.minepiece.common.init.ModSeeds;
import fr.minepiece.common.items.jobs.cook.CookToolItem;
import net.minecraft.item.Item;

public class BlockOignonCrops extends AbstractCropBlock {

    public BlockOignonCrops(String name, int jobXpAmount) {
        super(name, jobXpAmount, CookToolItem.class, 3, MinePieceJobs.COOK);
    }

    @Override
    protected Item getSeed() {
        return ModSeeds.OIGNON_SEED;
    }

    @Override
    protected Item getCrop() {
        return ModFoods.OIGNON;
    }

    /**
     * Exemple de surcharge de méthode si besoin d'un comportement spécifique
     * Pour les tomates, on augmente légèrement la chance de recevoir des graines
     */
    @Override
    protected float getAdditionalSeedDropChance() {
        return 0.10f;
    }
}