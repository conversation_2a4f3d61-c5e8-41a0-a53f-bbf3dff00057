package fr.minepiece.common.blocks.crops;

import fr.minepiece.common.api.jobs.MinePieceJobs;
import fr.minepiece.common.api.utils.PlayerUtils;
import fr.minepiece.common.capability.mpdata.IMinePieceData;
import fr.minepiece.common.capability.mpdata.MinePieceDataStorage;
import fr.minepiece.common.items.jobs.JobToolItem;
import net.minecraft.block.BlockCrops;
import net.minecraft.block.state.IBlockState;
import net.minecraft.entity.item.EntityItem;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.util.EnumFacing;
import net.minecraft.util.EnumHand;
import net.minecraft.util.math.AxisAlignedBB;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.IBlockAccess;
import net.minecraft.world.World;

/**
 * Classe abstraite pour tous les blocs de culture du mod
 * Permet de réduire la duplication de code et de centraliser la logique commune
 */
public abstract class AbstractCropBlock extends BlockCrops {

    /**
     * Boîtes de collision communes à toutes les cultures
     * Représente les différentes étapes de croissance (0-7)
     */
    protected static final AxisAlignedBB[] CROP_AABB = new AxisAlignedBB[] {
            new AxisAlignedBB(0.0D, 0.0D, 0.0D, 1.0D, 0.125D, 1.0D),
            new AxisAlignedBB(0.0D, 0.0D, 0.0D, 1.0D, 0.25D, 1.0D),
            new AxisAlignedBB(0.0D, 0.0D, 0.0D, 1.0D, 0.375D, 1.0D),
            new AxisAlignedBB(0.0D, 0.0D, 0.0D, 1.0D, 0.5625D, 1.0D),
            new AxisAlignedBB(0.0D, 0.0D, 0.0D, 1.0D, 0.8125D, 1.0D),
            new AxisAlignedBB(0.0D, 0.0D, 0.0D, 1.0D, 1.0D, 1.0D),
            new AxisAlignedBB(0.0D, 0.0D, 0.0D, 1.0D, 1.0D, 1.0D),
            new AxisAlignedBB(0.0D, 0.0D, 0.0D, 1.0D, 1.05D, 1.0D)
    };

    protected final int jobXpAmount;
    protected final Class<? extends JobToolItem> requiredToolClass;
    protected final int dropAmount;
    protected final MinePieceJobs requiredJob;

    /**
     * Constructeur pour un bloc de culture
     *
     * @param name Nom du bloc
     * @param jobXpAmount Quantité d'XP donnée lors de la récolte
     * @param requiredToolClass Classe d'outil requise pour récolter
     * @param dropAmount Nombre d'items donnés à la récolte
     * @param requiredJob Métier requis pour récolter
     */
    public AbstractCropBlock(String name, int jobXpAmount,
                             Class<? extends JobToolItem> requiredToolClass,
                             int dropAmount,
                             MinePieceJobs requiredJob) {
        this.jobXpAmount = jobXpAmount;
        this.requiredToolClass = requiredToolClass;
        this.dropAmount = dropAmount;
        this.requiredJob = requiredJob;

        setUnlocalizedName(name);
        setRegistryName(name);
    }

    /**
     * Constructeur simplifié avec valeur par défaut pour dropAmount (3)
     */
    public AbstractCropBlock(String name, int jobXpAmount,
                             Class<? extends JobToolItem> requiredToolClass,
                             MinePieceJobs requiredJob) {
        this(name, jobXpAmount, requiredToolClass, 3, requiredJob);
    }

    /**
     * Constructeur de compatibilité pour l'ancien code
     * À utiliser uniquement pour la transition
     */
    public AbstractCropBlock(String name, int jobXpAmount,
                             Class<? extends JobToolItem> requiredToolClass,
                             int dropAmount) {
        this(name, jobXpAmount, requiredToolClass, dropAmount, null);
    }

    /**
     * Gère le clic droit sur la culture
     * Vérifie si le joueur a l'outil approprié, le métier requis et le niveau requis
     * Si la culture est mature, donne la récolte et réinitialise la croissance
     */
    @Override
    public boolean onBlockActivated(World worldIn, BlockPos pos, IBlockState state, EntityPlayer playerIn, EnumHand hand,
                                    EnumFacing facing, float hitX, float hitY, float hitZ) {
        // Ne fait rien côté client
        if (worldIn.isRemote) return false;

        ItemStack heldItem = playerIn.getHeldItem(hand);
        Item item = heldItem.getItem();

        // Vérifie si l'outil est du bon type
        if (!requiredToolClass.isInstance(item)) {
            return false;
        }

        JobToolItem tool = (JobToolItem) item;

        // Récupère les données du joueur
        IMinePieceData mpData = playerIn.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);
        if (mpData == null) {
            return false;
        }

        // Vérifie si le joueur a le métier requis (si spécifié)
        if (requiredJob != null && mpData.getJob() != requiredJob) {
            playerIn.sendMessage(new TextComponentString(
                    TextFormatting.RED + "Vous devez être " + requiredJob.getName() +
                            " pour récolter cette plante !"));
            return false;
        }

        // Vérifie si le joueur a le niveau requis pour utiliser l'outil
        if (!tool.isUsableAtLevel(mpData.getJobLevel())) {
            playerIn.sendMessage(new TextComponentString(
                    TextFormatting.RED + "Vous n'avez pas le niveau requis pour utiliser cet outil !"));
            return false;
        }

        // Vérifie si la culture est mature
        if (this.isMaxAge(state)) {
            // Ajoute l'XP de métier
            PlayerUtils.addJobXpToPlayer((EntityPlayerMP) playerIn, jobXpAmount + tool.getXpBonus());
            mpData.clientSync((EntityPlayerMP) playerIn);

            // Fait apparaître les items récoltés
            worldIn.spawnEntity(new EntityItem(worldIn, pos.getX(), pos.getY(), pos.getZ(),
                    new ItemStack(getCrop(), dropAmount)));

            // Chance de donner aussi une graine (25% par défaut)
            if (worldIn.rand.nextFloat() < getAdditionalSeedDropChance()) {
                worldIn.spawnEntity(new EntityItem(worldIn, pos.getX(), pos.getY(), pos.getZ(),
                        new ItemStack(getSeed(), 1)));
            }

            // Endommage l'outil
            heldItem.damageItem(1, playerIn);

            // Réinitialise la culture à l'âge 0
            worldIn.setBlockState(pos, this.withAge(0));

            return true;
        }

        return false;
    }

    /**
     * Chance de drop une graine supplémentaire lors de la récolte
     * Peut être surchargée par les sous-classes pour des chances différentes
     *
     * @return La probabilité (0.0-1.0) de drop une graine supplémentaire
     */
    protected float getAdditionalSeedDropChance() {
        return 0.25f; // 25% par défaut
    }

    @Override
    public AxisAlignedBB getBoundingBox(IBlockState state, IBlockAccess source, BlockPos pos) {
        return CROP_AABB[state.getValue(this.getAgeProperty())];
    }
}