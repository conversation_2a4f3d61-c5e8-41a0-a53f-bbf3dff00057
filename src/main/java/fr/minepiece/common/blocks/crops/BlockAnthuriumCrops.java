package fr.minepiece.common.blocks.crops;

import fr.minepiece.common.api.jobs.MinePieceJobs;
import fr.minepiece.common.init.ModItems;
import fr.minepiece.common.init.ModSeeds;
import fr.minepiece.common.items.jobs.medic.tools.MedicToolItem;
import net.minecraft.item.Item;

public class BlockAnthuriumCrops extends AbstractCropBlock {

    public BlockAnthuriumCrops(String name, int jobXpAmount) {
        super(name, jobXpAmount, MedicToolItem.class, 3, MinePieceJobs.MEDIC);
    }

    @Override
    protected Item getSeed() {
        return ModSeeds.ANTHURIUM_SEED;
    }

    @Override
    protected Item getCrop() {
        return ModItems.ANTHURIUM;
    }

    /**
     * Exemple de surcharge de méthode si besoin d'un comportement spécifique
     * Pour les tomates, on augmente légèrement la chance de recevoir des graines
     */
    @Override
    protected float getAdditionalSeedDropChance() {
        return 0.10f;
    }
}