package fr.minepiece.common.blocks.furniture;

import com.google.common.collect.Lists;
import fr.minepiece.common.api.utils.CollisionUtils;
import fr.minepiece.common.blocks.BlockRotatable;
import net.minecraft.block.SoundType;
import net.minecraft.block.material.Material;
import net.minecraft.block.state.IBlockState;
import net.minecraft.entity.Entity;
import net.minecraft.util.BlockRenderLayer;
import net.minecraft.util.EnumFacing;
import net.minecraft.util.math.AxisAlignedBB;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.RayTraceResult;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.IBlockAccess;
import net.minecraft.world.World;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

import java.util.List;

public class BlockCasinoGameBigwinGreen extends BlockRotatable {

    public static AxisAlignedBB BOUNDING_BOX = new AxisAlignedBB(0.1,-1,0.1,0.9, 1.5, 0.9);
    public static AxisAlignedBB LIGHTS = new AxisAlignedBB(0.48,1.35,0.52,0.52, 1.5, 0.52);
    public static AxisAlignedBB MAIN_NORTH = new AxisAlignedBB(0, -1, 0., 1, 1.35, 0.8);
    public static AxisAlignedBB MAIN_EAST = CollisionUtils.rotateAABB(MAIN_NORTH, EnumFacing.EAST);
    public static AxisAlignedBB MAIN_SOUTH = CollisionUtils.rotateAABB(MAIN_NORTH, EnumFacing.SOUTH);
    public static AxisAlignedBB MAIN_WEST = CollisionUtils.rotateAABB(MAIN_NORTH, EnumFacing.WEST);

    public static AxisAlignedBB BUTTONS_NORTH = new AxisAlignedBB(0.1, -0.2, 0.8, 0.9, 0.55, 1.15);
    public static AxisAlignedBB BUTTONS_EAST = CollisionUtils.rotateAABB(BUTTONS_NORTH, EnumFacing.EAST);
    public static AxisAlignedBB BUTTONS_SOUTH = CollisionUtils.rotateAABB(BUTTONS_NORTH, EnumFacing.SOUTH);
    public static AxisAlignedBB BUTTONS_WEST = CollisionUtils.rotateAABB(BUTTONS_NORTH, EnumFacing.WEST);


    private List<AxisAlignedBB> getCollisionBoxList(IBlockState state) {
        List<AxisAlignedBB> list = Lists.newArrayList();
        EnumFacing facing = state.getValue(FACING);
        switch (facing) {
            case EAST:
                list.add(MAIN_EAST);
                list.add(BUTTONS_EAST);
                break;
            case SOUTH:
                list.add(MAIN_SOUTH);
                list.add(BUTTONS_SOUTH);
                break;
            case WEST:
                list.add(MAIN_WEST);
                list.add(BUTTONS_WEST);
                break;
            default:
                list.add(MAIN_NORTH);
                list.add(BUTTONS_NORTH);
                break;
        }
        list.add(LIGHTS);
        return list;
    }

    @Override
    public void addCollisionBoxToList(IBlockState state, World worldIn, BlockPos pos, AxisAlignedBB entityBox, List<AxisAlignedBB> collidingBoxes, Entity entityIn, boolean p_185477_7_) {
        List<AxisAlignedBB> list = getCollisionBoxList(this.getActualState(state, worldIn, pos));
        for(AxisAlignedBB box : list) {
            addCollisionBoxToList(pos, entityBox, collidingBoxes, box);
        }
    }

    @Override
    public RayTraceResult collisionRayTrace(IBlockState blockState, World worldIn, BlockPos pos, Vec3d start, Vec3d end) {
        List<RayTraceResult> list = Lists.newArrayList();

        for(AxisAlignedBB axisalignedbb : getCollisionBoxList(this.getActualState(blockState, worldIn, pos))) {
            list.add(this.rayTrace(pos, start, end, axisalignedbb));
        }

        RayTraceResult raytraceresult1 = null;
        double d1 = 0.0D;

        for(RayTraceResult raytraceresult : list) {
            if(raytraceresult != null) {
                double d0 = raytraceresult.hitVec.squareDistanceTo(end);

                if(d0 > d1) {
                    raytraceresult1 = raytraceresult;
                    d1 = d0;
                }
            }
        }

        return raytraceresult1;
    }

    public BlockCasinoGameBigwinGreen(final Material materialIn, SoundType soundType, final String blockName) {
        super(materialIn, soundType, blockName);
    }
    @Override
    public AxisAlignedBB getBoundingBox(IBlockState state, IBlockAccess source, BlockPos pos) {
        return BOUNDING_BOX;
    }
    @SideOnly(Side.CLIENT)
    public BlockRenderLayer getBlockLayer() {
        return BlockRenderLayer.CUTOUT;
    }
}
