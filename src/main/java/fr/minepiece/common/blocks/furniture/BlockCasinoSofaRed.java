package fr.minepiece.common.blocks.furniture;

import fr.minepiece.common.blocks.BlockMinePiece;
import net.minecraft.block.BlockHorizontal;
import net.minecraft.block.material.Material;
import net.minecraft.block.properties.IProperty;
import net.minecraft.block.properties.PropertyDirection;
import net.minecraft.block.state.BlockFaceShape;
import net.minecraft.block.state.BlockStateContainer;
import net.minecraft.block.state.IBlockState;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.util.*;
import net.minecraft.util.math.AxisAlignedBB;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.IBlockAccess;
import net.minecraft.world.World;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

@Deprecated
public class BlockCasinoSofaRed extends BlockMinePiece {

    public static final PropertyDirection FACING = BlockHorizontal.FACING;
    protected static final AxisAlignedBB AABB_SLAB_BOTTOM_NORTH = new AxisAlignedBB(0.0, 0.0D, 0.0D, 2, 0.5D, 1);
    protected static final AxisAlignedBB AABB_SLAB_BOTTOM_SOUTH = new AxisAlignedBB(1D, 0.0D, 0.0D, -1, 0.5D, 1);
    protected static final AxisAlignedBB AABB_SLAB_BOTTOM_EAST = new AxisAlignedBB(0.0, 0.0D, 0.0D, 1, 0.5D, 2);
    protected static final AxisAlignedBB AABB_SLAB_BOTTOM_WEST = new AxisAlignedBB(0.0, 0.0D, 1D, 1, 0.5D, -1);


    public BlockCasinoSofaRed(IBlockState modelState) {
        super(Material.WOOD, "casino_sofa_red");
        this.setDefaultState(this.blockState.getBaseState().withProperty(FACING, EnumFacing.NORTH));
        System.out.println("SKARARA "+modelState.getBlock().getUnlocalizedName());
    }

    public BlockCasinoSofaRed() {
        super(Material.WOOD, "casino_sofa_red");
        this.setDefaultState(this.blockState.getBaseState().withProperty(FACING, EnumFacing.NORTH));
    }

    public boolean isFullCube(IBlockState state) {
        return false;
    }
    public boolean isOpaqueCube(IBlockState state) {
        return false;
    }


    public boolean doesSideBlockRendering(IBlockState state, IBlockAccess world, BlockPos pos, EnumFacing face) {
        return true;  // Always render sides
    }
    @SideOnly(Side.CLIENT)
    public BlockRenderLayer getBlockLayer() {
        return BlockRenderLayer.CUTOUT;
    }

    public IBlockState getStateForPlacement(World worldIn, BlockPos pos, EnumFacing facing, float hitX, float hitY, float hitZ, int meta, EntityLivingBase placer) {
        return super.getStateForPlacement(worldIn, pos, facing, hitX, hitY, hitZ, meta, placer).withProperty(FACING, placer.getHorizontalFacing());
    }

    @SideOnly(Side.CLIENT)
    public AxisAlignedBB getSelectedBoundingBox(IBlockState state, World worldIn, BlockPos pos) {
        //System.out.println("t "+state);
        EnumFacing facing = ((EnumFacing) state.getValue(FACING));
        //System.out.println("F "+pos);
        switch (facing) {
            case NORTH:
                return AABB_SLAB_BOTTOM_NORTH.offset(pos);
            case WEST:
                return AABB_SLAB_BOTTOM_WEST.offset(pos);
            case SOUTH:
                return AABB_SLAB_BOTTOM_SOUTH.offset(pos);
            case EAST:
                return AABB_SLAB_BOTTOM_EAST.offset(pos);
            default:
                return AABB_SLAB_BOTTOM_NORTH.offset(pos);  // Default to NORTH if facing is invalid
        }

    }

    public AxisAlignedBB getBoundingBox(IBlockState state, IBlockAccess source, BlockPos pos) {
        EnumFacing facing = ((EnumFacing) state.getValue(FACING));
        //System.out.println("F "+pos);
        switch (facing) {
            case NORTH:
                return AABB_SLAB_BOTTOM_NORTH;
            case WEST:
                return AABB_SLAB_BOTTOM_WEST;
            case SOUTH:
                return AABB_SLAB_BOTTOM_SOUTH;
            case EAST:
                return AABB_SLAB_BOTTOM_EAST;
            default:
                return AABB_SLAB_BOTTOM_NORTH;  // Default to NORTH if facing is invalid
        }
    }

    public AxisAlignedBB getCollisionBoundingBox(IBlockState blockState, IBlockAccess worldIn, BlockPos pos) {
        EnumFacing facing = ((EnumFacing) blockState.getValue(FACING));
        //System.out.println("t "+pos);
        switch (facing) {
            case NORTH:
                return AABB_SLAB_BOTTOM_NORTH;
            case WEST:
                return AABB_SLAB_BOTTOM_WEST;
            case SOUTH:
                return AABB_SLAB_BOTTOM_SOUTH;
            case EAST:
                return AABB_SLAB_BOTTOM_EAST;
            default:
                return AABB_SLAB_BOTTOM_NORTH;  // Default to NORTH if facing is invalid
        }
    }

    public IBlockState withRotation(IBlockState state, Rotation rot) {
        return state.withProperty(FACING, rot.rotate((EnumFacing) state.getValue(FACING)));
    }

    public IBlockState getStateFromMeta(int meta) {
        EnumFacing facing = EnumFacing.getFront(meta & 7);  // 7 is used to mask the lower 3 bits representing facing
        if (facing.getAxis() == EnumFacing.Axis.Y)
            facing = EnumFacing.SOUTH;  // Default to north if it's an invalid facing for stairs

        return this.getDefaultState().withProperty(FACING, facing);
    }

    public int getMetaFromState(IBlockState state) {
        int meta = ((EnumFacing) state.getValue(FACING)).getIndex();
        return meta & 7;  // 7 is used to mask the lower 3 bits representing facing
    }

    protected BlockStateContainer createBlockState() {
        return new BlockStateContainer(this, new IProperty[] {FACING});
    }

    @Override
    public BlockFaceShape getBlockFaceShape(IBlockAccess worldIn, IBlockState state, BlockPos pos, EnumFacing face) {
        System.out.println("hi");
        state = this.getActualState(state, worldIn, pos);
        EnumFacing enumfacing = (EnumFacing)state.getValue(FACING);
        if (enumfacing == face) System.out.println("AAA"); else System.out.println("BBB");
        return enumfacing == face ? BlockFaceShape.SOLID : BlockFaceShape.UNDEFINED;
    }

}
