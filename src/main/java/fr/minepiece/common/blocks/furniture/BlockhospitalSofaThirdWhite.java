package fr.minepiece.common.blocks.furniture;

import com.google.common.collect.Lists;
import fr.minepiece.common.api.utils.CollisionUtils;
import fr.minepiece.common.api.utils.SeatUtils;
import fr.minepiece.common.blocks.BlockRotatable;
import net.minecraft.block.SoundType;
import net.minecraft.block.material.Material;
import net.minecraft.block.state.IBlockState;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.BlockRenderLayer;
import net.minecraft.util.EnumFacing;
import net.minecraft.util.EnumHand;
import net.minecraft.util.math.AxisAlignedBB;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.RayTraceResult;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.IBlockAccess;
import net.minecraft.world.World;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

import java.util.List;

public class BlockhospitalSofaThirdWhite extends BlockRotatable {
    public static AxisAlignedBB BOUNDING_BOX;
    public static AxisAlignedBB BOUNDING_BOX_NORTH = new AxisAlignedBB(-1, 0, 0, 2, 1.2, 1);
    public static AxisAlignedBB BOUNDING_BOX_EAST = CollisionUtils.rotateAABB(BOUNDING_BOX_NORTH, EnumFacing.EAST);
    public static AxisAlignedBB BOUNDING_BOX_SOUTH = CollisionUtils.rotateAABB(BOUNDING_BOX_NORTH, EnumFacing.SOUTH);
    public static AxisAlignedBB BOUNDING_BOX_WEST = CollisionUtils.rotateAABB(BOUNDING_BOX_NORTH, EnumFacing.WEST);
    public static AxisAlignedBB SEAT_NORTH = new AxisAlignedBB(-1, 0, 0, 2, 0.5, 0.9);
    public static AxisAlignedBB SEAT_EAST = CollisionUtils.rotateAABB(SEAT_NORTH, EnumFacing.EAST);
    public static AxisAlignedBB SEAT_WEST = CollisionUtils.rotateAABB(SEAT_NORTH, EnumFacing.WEST);
    public static AxisAlignedBB SEAT_SOUTH = CollisionUtils.rotateAABB(SEAT_NORTH, EnumFacing.SOUTH);
    public static AxisAlignedBB REST_NORTH = new AxisAlignedBB(-0.8, 0, 0, 1.8, 1.15, 0.2);
    public static AxisAlignedBB REST_EAST = CollisionUtils.rotateAABB(REST_NORTH, EnumFacing.EAST);
    public static AxisAlignedBB REST_WEST = CollisionUtils.rotateAABB(REST_NORTH, EnumFacing.WEST);
    public static AxisAlignedBB REST_SOUTH = CollisionUtils.rotateAABB(REST_NORTH, EnumFacing.SOUTH);
    public static AxisAlignedBB REST_RIGHT_NORTH = new AxisAlignedBB(-1.1, 0, 0, -1.05, 0.85, 1);
    public static AxisAlignedBB REST_RIGHT_EAST = CollisionUtils.rotateAABB(REST_RIGHT_NORTH, EnumFacing.EAST);
    public static AxisAlignedBB REST_RIGHT_WEST = CollisionUtils.rotateAABB(REST_RIGHT_NORTH, EnumFacing.WEST);
    public static AxisAlignedBB REST_RIGHT_SOUTH = CollisionUtils.rotateAABB(REST_RIGHT_NORTH, EnumFacing.SOUTH);
    public static AxisAlignedBB REST_LEFT_NORTH = new AxisAlignedBB(1.95, 0, 0, 2.1, 0.85, 1);
    public static AxisAlignedBB REST_LEFT_EAST = CollisionUtils.rotateAABB(REST_LEFT_NORTH, EnumFacing.EAST);
    public static AxisAlignedBB REST_LEFT_WEST = CollisionUtils.rotateAABB(REST_LEFT_NORTH, EnumFacing.WEST);
    public static AxisAlignedBB REST_LEFT_SOUTH = CollisionUtils.rotateAABB(REST_LEFT_NORTH, EnumFacing.SOUTH);

    private List<AxisAlignedBB> getCollisionBoxList(IBlockState state) {
        List<AxisAlignedBB> list = Lists.newArrayList();
        EnumFacing facing = state.getValue(FACING);
        switch (facing) {
            case EAST:
                list.add(SEAT_EAST);
                list.add(REST_EAST);
                list.add(REST_RIGHT_EAST);
                list.add(REST_LEFT_EAST);
                BOUNDING_BOX = BOUNDING_BOX_EAST;
                break;
            case SOUTH:
                list.add(SEAT_SOUTH);
                list.add(REST_SOUTH);
                list.add(REST_RIGHT_SOUTH);
                list.add(REST_LEFT_SOUTH);
                BOUNDING_BOX = BOUNDING_BOX_SOUTH;
                break;
            case WEST:
                list.add(SEAT_WEST);
                list.add(REST_WEST);
                list.add(REST_RIGHT_WEST);
                list.add(REST_LEFT_WEST);
                BOUNDING_BOX = BOUNDING_BOX_WEST;
                break;
            default:
                list.add(SEAT_NORTH);
                list.add(REST_NORTH);
                list.add(REST_RIGHT_NORTH);
                list.add(REST_LEFT_NORTH);
                BOUNDING_BOX = BOUNDING_BOX_NORTH;
                break;
        }
        return list;
    }

    public void addCollisionBoxToList(IBlockState state, World worldIn, BlockPos pos, AxisAlignedBB entityBox, List<AxisAlignedBB> collidingBoxes, Entity entityIn, boolean p_185477_7_) {
        List<AxisAlignedBB> list = getCollisionBoxList(this.getActualState(state, worldIn, pos));
        for(AxisAlignedBB box : list) {
            addCollisionBoxToList(pos, entityBox, collidingBoxes, box);
        }
    }

    @Override
    public RayTraceResult collisionRayTrace(IBlockState blockState, World worldIn, BlockPos pos, Vec3d start, Vec3d end) {
        List<RayTraceResult> list = Lists.newArrayList();

        for(AxisAlignedBB axisalignedbb : getCollisionBoxList(this.getActualState(blockState, worldIn, pos))) {
            list.add(this.rayTrace(pos, start, end, axisalignedbb));
        }

        RayTraceResult raytraceresult1 = null;
        double d1 = 0.0D;

        for(RayTraceResult raytraceresult : list) {
            if(raytraceresult != null) {
                double d0 = raytraceresult.hitVec.squareDistanceTo(end);

                if(d0 > d1) {
                    raytraceresult1 = raytraceresult;
                    d1 = d0;
                }
            }
        }

        return raytraceresult1;
    }
    public boolean onBlockActivated(World worldIn, BlockPos pos, IBlockState state, EntityPlayer playerIn, EnumHand hand, EnumFacing facing, float hitX, float hitY, float hitZ) {
        // Add seating logic here
        if (!playerIn.isSneaking()) {
            if (SeatUtils.sitOnBlock(worldIn, pos.getX(), pos.getY(), pos.getZ(), playerIn, 0.3))
                return true;
        }
        return false;
    }

    public BlockhospitalSofaThirdWhite(final Material materialIn, SoundType soundType, final String blockName) {
        super(materialIn, soundType, blockName);
    }
    @Override
    public AxisAlignedBB getBoundingBox(IBlockState state, IBlockAccess source, BlockPos pos) {
        return BOUNDING_BOX;
    }
    @SideOnly(Side.CLIENT)
    public BlockRenderLayer getBlockLayer() {
        return BlockRenderLayer.CUTOUT;
    }
}
