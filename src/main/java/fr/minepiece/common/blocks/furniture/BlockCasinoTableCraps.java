package fr.minepiece.common.blocks.furniture;

import com.google.common.collect.Lists;
import fr.minepiece.common.api.utils.CollisionUtils;
import fr.minepiece.common.blocks.BlockRotatable;
import net.minecraft.block.SoundType;
import net.minecraft.block.material.Material;
import net.minecraft.block.state.IBlockState;
import net.minecraft.entity.Entity;
import net.minecraft.util.BlockRenderLayer;
import net.minecraft.util.EnumFacing;
import net.minecraft.util.math.AxisAlignedBB;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.RayTraceResult;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.IBlockAccess;
import net.minecraft.world.World;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

import java.util.List;

public class BlockCasinoTableCraps extends BlockRotatable {
    public static AxisAlignedBB BOUNDING_BOX;
    public static AxisAlignedBB BOUNDING_BOX_NORTH = new AxisAlignedBB(-1, 0, -0.25, 2, 1.25, 1.25);
    public static AxisAlignedBB BOUNDING_BOX_EAST = CollisionUtils.rotateAABB(BOUNDING_BOX_NORTH, EnumFacing.EAST);
    public static AxisAlignedBB BOUNDING_BOX_SOUTH = CollisionUtils.rotateAABB(BOUNDING_BOX_NORTH, EnumFacing.SOUTH);
    public static AxisAlignedBB BOUNDING_BOX_WEST = CollisionUtils.rotateAABB(BOUNDING_BOX_NORTH, EnumFacing.WEST);
    public static AxisAlignedBB TABLE_NORTH = new AxisAlignedBB(-1, 0, -0.25, 2, 1, 1.25);
    public static AxisAlignedBB TABLE_EAST = CollisionUtils.rotateAABB(TABLE_NORTH, EnumFacing.EAST);
    public static AxisAlignedBB TABLE_SOUTH = CollisionUtils.rotateAABB(TABLE_NORTH, EnumFacing.SOUTH);
    public static AxisAlignedBB TABLE_WEST = CollisionUtils.rotateAABB(TABLE_NORTH, EnumFacing.WEST);

    private List<AxisAlignedBB> getCollisionBoxList(IBlockState state) {
        List<AxisAlignedBB> list = Lists.newArrayList();
        EnumFacing facing = state.getValue(FACING);
        switch (facing) {
            case EAST:
                list.add(TABLE_EAST);
                BOUNDING_BOX = BOUNDING_BOX_EAST;
                break;
            case SOUTH:
                list.add(TABLE_SOUTH);
                BOUNDING_BOX = BOUNDING_BOX_SOUTH;
                break;
            case WEST:
                list.add(TABLE_WEST);
                BOUNDING_BOX = BOUNDING_BOX_WEST;
                break;
            default:
                list.add(TABLE_NORTH);
                BOUNDING_BOX = BOUNDING_BOX_NORTH;
                break;
        }
        return list;
    }

    @Override
    public void addCollisionBoxToList(IBlockState state, World worldIn, BlockPos pos, AxisAlignedBB entityBox, List<AxisAlignedBB> collidingBoxes, Entity entityIn, boolean p_185477_7_) {
        List<AxisAlignedBB> list = getCollisionBoxList(this.getActualState(state, worldIn, pos));
        for(AxisAlignedBB box : list) {
            addCollisionBoxToList(pos, entityBox, collidingBoxes, box);
        }
    }

    @Override
    public RayTraceResult collisionRayTrace(IBlockState blockState, World worldIn, BlockPos pos, Vec3d start, Vec3d end) {
        List<RayTraceResult> list = Lists.newArrayList();

        for(AxisAlignedBB axisalignedbb : getCollisionBoxList(this.getActualState(blockState, worldIn, pos))) {
            list.add(this.rayTrace(pos, start, end, axisalignedbb));
        }

        RayTraceResult raytraceresult1 = null;
        double d1 = 0.0D;

        for(RayTraceResult raytraceresult : list) {
            if(raytraceresult != null) {
                double d0 = raytraceresult.hitVec.squareDistanceTo(end);

                if(d0 > d1) {
                    raytraceresult1 = raytraceresult;
                    d1 = d0;
                }
            }
        }

        return raytraceresult1;
    }
    public BlockCasinoTableCraps(final Material materialIn, SoundType soundType, final String blockName) {
        super(materialIn, soundType, blockName);
    }
    @Override
    public AxisAlignedBB getBoundingBox(IBlockState state, IBlockAccess source, BlockPos pos) {
        return BOUNDING_BOX;
    }
    @SideOnly(Side.CLIENT)
    public BlockRenderLayer getBlockLayer() {
        return BlockRenderLayer.CUTOUT;
    }
}
