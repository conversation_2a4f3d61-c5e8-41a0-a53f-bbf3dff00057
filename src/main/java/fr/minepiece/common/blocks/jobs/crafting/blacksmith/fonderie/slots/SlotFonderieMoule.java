package fr.minepiece.common.blocks.jobs.crafting.blacksmith.fonderie.slots;

import fr.minepiece.common.blocks.jobs.crafting.blacksmith.fonderie.tileentity.TileEntityFonderie;
import net.minecraft.inventory.IInventory;
import net.minecraft.inventory.Slot;
import net.minecraft.item.ItemStack;

public class SlotFonderieMoule extends Slot {

    public SlotFonderieMoule(IInventory inventory, int index, int x, int y) {
        super(inventory, index, x, y);
    }

    @Override
    public boolean isItemValid(ItemStack stack) {
        return TileEntityFonderie.isMoule(stack);
    }
}