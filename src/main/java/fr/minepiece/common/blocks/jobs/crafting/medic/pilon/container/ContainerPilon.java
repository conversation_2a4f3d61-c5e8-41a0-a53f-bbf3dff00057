package fr.minepiece.common.blocks.jobs.crafting.medic.pilon.container;

import fr.minepiece.common.api.jobs.MinePieceJobs;
import fr.minepiece.common.blocks.jobs.crafting.medic.pilon.slots.SlotPilonOutput;
import fr.minepiece.common.blocks.jobs.crafting.medic.pilon.tileentity.TileEntityPilon;
import fr.minepiece.common.capability.mpdata.IMinePieceData;
import fr.minepiece.common.capability.mpdata.MinePieceDataStorage;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.player.InventoryPlayer;
import net.minecraft.inventory.Container;
import net.minecraft.inventory.IContainerListener;
import net.minecraft.inventory.Slot;
import net.minecraft.item.ItemStack;

public class ContainerPilon extends Container {
    private final TileEntityPilon tileEntity;
    private final EntityPlayer player;
    private int processTime;

    public ContainerPilon(InventoryPlayer playerInventory, TileEntityPilon tileEntity) {
        this.tileEntity = tileEntity;
        this.player = playerInventory.player;
        tileEntity.setUsingPlayer(player);

        // Slot d'entrée
        this.addSlotToContainer(new Slot(tileEntity, 0, 76, 31));

        // Slot de sortie
        this.addSlotToContainer(new SlotPilonOutput(player, tileEntity, 1, 125, 31));

        // Inventaire du joueur (3 lignes x 9 colonnes)
        for (int row = 0; row < 3; row++) {
            for (int col = 0; col < 9; col++) {
                this.addSlotToContainer(new Slot(playerInventory, col + row * 9 + 9, 8 + col * 18, 84 + row * 18));
            }
        }

        // Hotbar (9 slots)
        for (int col = 0; col < 9; col++) {
            this.addSlotToContainer(new Slot(playerInventory, col, 8 + col * 18, 142));
        }
    }

    @Override
    public boolean canInteractWith(EntityPlayer playerIn) {
        IMinePieceData mpData = playerIn.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);
        return mpData != null && mpData.getDailyCraftLimit() > 0;
    }

    @Override
    public void detectAndSendChanges() {
        super.detectAndSendChanges();

        for (IContainerListener listener : this.listeners) {
            if (this.processTime != this.tileEntity.getField(0)) {
                listener.sendWindowProperty(this, 0, this.tileEntity.getField(0));
            }
        }

        this.processTime = this.tileEntity.getField(0);
    }

    @Override
    public ItemStack transferStackInSlot(EntityPlayer playerIn, int index) {
        ItemStack itemstack = ItemStack.EMPTY;
        Slot slot = this.inventorySlots.get(index);

        if (slot != null && slot.getHasStack()) {
            ItemStack itemInSlot = slot.getStack();
            itemstack = itemInSlot.copy();

            if (index == 1) { // Slot de sortie
                if (!this.mergeItemStack(itemInSlot, 2, 38, true)) {
                    return ItemStack.EMPTY;
                }
                slot.onSlotChange(itemInSlot, itemstack);
            } else if (index != 0) { // Pas le slot d'entrée
                if (!this.mergeItemStack(itemInSlot, 0, 1, false)) {
                    return ItemStack.EMPTY;
                }
            }

            if (itemInSlot.isEmpty()) {
                slot.putStack(ItemStack.EMPTY);
            } else {
                slot.onSlotChanged();
            }

            if (itemInSlot.getCount() == itemstack.getCount()) {
                return ItemStack.EMPTY;
            }

            slot.onTake(playerIn, itemInSlot);
        }
        return itemstack;
    }

    @Override
    public void onContainerClosed(EntityPlayer playerIn) {
        super.onContainerClosed(playerIn);
        this.tileEntity.setUsingPlayer(null);

        if (!playerIn.world.isRemote) {
            clearCraftingGrid(playerIn);
        }
    }

    private void clearCraftingGrid(EntityPlayer playerIn) {
        ItemStack stack = this.tileEntity.removeStackFromSlot(0);
            if (!stack.isEmpty()) {
                playerIn.dropItem(stack, false);
            }
    }
}