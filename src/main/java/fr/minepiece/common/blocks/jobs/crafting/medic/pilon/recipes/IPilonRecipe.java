package fr.minepiece.common.blocks.jobs.crafting.medic.pilon.recipes;

import fr.minepiece.common.api.jobs.MinePieceJobs;
import fr.minepiece.common.blocks.jobs.crafting.medic.pilon.tileentity.TileEntityPilon;
import net.minecraft.item.ItemStack;
import net.minecraft.world.World;

/**
 * Interface pour les recettes du pilon
 */
public interface IPilonRecipe {
    /**
     * Vérifie si la recette correspond à l'inventaire
     * @param inv L'inventaire du pilon
     * @param world Le monde
     * @return true si la recette correspond
     */
    boolean matches(TileEntityPilon inv, World world);

    /**
     * Retourne le niveau de métier requis pour cette recette
     * @return Le niveau de métier requis
     */
    int getLevelRequired();

    /**
     * Retourne la récompense d'XP pour cette recette
     * @return La récompense d'XP
     */
    int getXpReward();

    /**
     * Retourne le résultat de la recette
     * @param inv L'inventaire du pilon
     * @return Le résultat de la recette
     */
    ItemStack getCraftingResult(TileEntityPilon inv);

    /**
     * Retourne le métier requis pour cette recette
     * @return Le métier requis (MinePieceJobs)
     */
    MinePieceJobs getRequiredJob();
}