package fr.minepiece.common.blocks.jobs.crafting.medic.bancdemedicament;

import fr.minepiece.MinePiece;
import fr.minepiece.common.api.jobs.MinePieceJobs;
import fr.minepiece.common.blocks.BlockLongHorizontal;
import fr.minepiece.common.blocks.jobs.crafting.medic.bancdemedicament.container.ContainerBancDeMedicament;
import fr.minepiece.common.blocks.jobs.crafting.medic.gui.MedicGuiIDs;
import fr.minepiece.common.capability.mpdata.IMinePieceData;
import fr.minepiece.common.capability.mpdata.MinePieceDataStorage;
import net.minecraft.block.material.Material;
import net.minecraft.block.state.IBlockState;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.player.InventoryPlayer;
import net.minecraft.inventory.Container;
import net.minecraft.util.EnumFacing;
import net.minecraft.util.EnumHand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.ITextComponent;
import net.minecraft.util.text.TextComponentTranslation;
import net.minecraft.world.IInteractionObject;
import net.minecraft.world.World;

public class BlockBancDeMedicamentLong extends BlockLongHorizontal {
    private final int requiredLevel;

    public BlockBancDeMedicamentLong(Material material, String name, int requiredLevel) {
        super(material, name);
        this.requiredLevel = requiredLevel;
        this.setDefaultState(this.blockState.getBaseState().withProperty(FACING, EnumFacing.NORTH));
        this.setHardness(2.0F);
        this.setResistance(5.0F);
        this.setLightOpacity(0);
        this.setCreativeTab(MinePiece.JOBS);
    }

    @Override
    public boolean onBlockActivated(World worldIn, BlockPos pos, IBlockState state, EntityPlayer playerIn, EnumHand hand, EnumFacing facing, float hitX, float hitY, float hitZ) {
        if(worldIn.isRemote) return true;

        IMinePieceData mpData = playerIn.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);
        if(mpData == null) return false;

        if(mpData.getJob() != MinePieceJobs.MEDIC) {
            playerIn.sendMessage(new TextComponentTranslation("message.bancdemedicament.wrong_job"));
            return false;
        }

        if(mpData.getJobLevel() < requiredLevel) {
            playerIn.sendMessage(new TextComponentTranslation("message.bancdemedicament.level_too_low", requiredLevel));
            return false;
        }

        if(mpData.getDailyCraftLimit() <= 0) {
            playerIn.sendMessage(new TextComponentTranslation("message.bancdemedicament.daily_limit"));
            return false;
        }
        playerIn.openGui(MinePiece.INSTANCE, MedicGuiIDs.BANC_DE_MEDICAMENT, worldIn, pos.getX(), pos.getY(), pos.getZ());
        return true;
    }

    @Override
    public boolean isOpaqueCube(IBlockState state) {
        return false;
    }

    @Override
    public boolean isFullCube(IBlockState state) {
        return false;
    }

    // Interface interne similaire à InterfaceCraftingTable de BlockWorkbench
    public static class InterfaceBancDeMedicament implements IInteractionObject {
        private final World world;
        private final BlockPos position;

        public InterfaceBancDeMedicament(World worldIn, BlockPos pos) {
            this.world = worldIn;
            this.position = pos;
        }

        @Override
        public String getName() {
            return "banc_de_medicament";
        }

        @Override
        public boolean hasCustomName() {
            return false;
        }

        @Override
        public ITextComponent getDisplayName() {
            return new TextComponentTranslation("container.banc_de_medicament");
        }

        @Override
        public Container createContainer(InventoryPlayer playerInventory, EntityPlayer playerIn) {
            return new ContainerBancDeMedicament(playerInventory, world, position);
        }

        @Override
        public String getGuiID() {
            return "minepiece:banc_de_medicament";
        }
    }
}
