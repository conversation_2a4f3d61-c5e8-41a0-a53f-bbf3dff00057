package fr.minepiece.common.blocks.jobs.crafting.medic.paillasse.recipes;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import fr.minepiece.common.craft.CraftingResult;
import fr.minepiece.common.craft.IMinePieceCraftingInventory;
import fr.minepiece.common.init.ModTools;
import fr.minepiece.common.items.jobs.medic.tools.MedicToolItem;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.crafting.IRecipe;
import net.minecraft.item.crafting.Ingredient;
import net.minecraft.item.crafting.ShapedRecipes;
import net.minecraft.inventory.InventoryCrafting;
import net.minecraft.util.JsonUtils;
import net.minecraft.util.NonNullList;
import net.minecraft.util.ResourceLocation;
import net.minecraft.world.World;
import net.minecraftforge.common.crafting.CraftingHelper;
import net.minecraftforge.common.crafting.IRecipeFactory;
import net.minecraftforge.common.crafting.JsonContext;
import net.minecraftforge.oredict.ShapelessOreRecipe;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nonnull;
import java.util.*;

public class ShapelessPaillasseRecipe extends ShapelessOreRecipe implements IPaillasseRecipe {
    private static final Logger LOGGER = LogManager.getLogger("MinePiece Recipes");

    private final int levelRequired;
    private final int xpReward;
    private final ResourceLocation recipeId;
    private final boolean[] specialSlotUsage; // Indique les slots spéciaux utilisés (0=Input1, 1=Input2)
    private final Ingredient[] specialSlotIngredients; // Ingrédients pour les slots spéciaux

    public ShapelessPaillasseRecipe(ResourceLocation group, NonNullList<Ingredient> input, @Nonnull ItemStack result,
                                    int levelRequired, int xpReward, boolean[] specialSlotUsage,
                                    Ingredient[] specialSlotIngredients) {
        super(group, input, result);
        this.levelRequired = levelRequired;
        this.xpReward = xpReward;
        this.recipeId = group;
        this.specialSlotUsage = specialSlotUsage;
        this.specialSlotIngredients = specialSlotIngredients;

        LOGGER.debug("Creating new ShapelessPaillasseRecipe:");
        LOGGER.debug(" - Group: {}", group);
        LOGGER.debug(" - Result: {}", result.getDisplayName());
        LOGGER.debug(" - Level Required: {}", levelRequired);
        LOGGER.debug(" - XP Reward: {}", xpReward);
        LOGGER.debug(" - Special slots used: slot1={}, slot2={}",
                specialSlotUsage[0], specialSlotUsage[1]);
    }

    @Override
    public boolean matches(InventoryCrafting inv, World world) {
        // Compter combien d'ingrédients de chaque type sont nécessaires
        List<Ingredient> ingredients = new ArrayList<>(this.getIngredients());

        // Si pas d'ingrédients, pas de match
        if (ingredients.isEmpty()) {
            return false;
        }

        // Tableau pour suivre quels slots ont déjà été utilisés
        boolean[] used = new boolean[36];

        // Pour chaque ingrédient, essayer de le trouver dans l'inventaire
        for (Iterator<Ingredient> iterator = ingredients.iterator(); iterator.hasNext();) {
            Ingredient ingredient = iterator.next();
            boolean found = false;

            for (int i = 0; i < 36; i++) {
                if (!used[i]) {
                    ItemStack stackInSlot = inv.getStackInSlot(i);
                    if (stackInSlot.isEmpty()) {
                        continue;
                    }

                    // Vérification spéciale pour les outils
                    if (isTool(stackInSlot.getItem())) {
                        if (ingredient.getMatchingStacks().length > 0) {
                            for (ItemStack matchStack : ingredient.getMatchingStacks()) {
                                if (stackInSlot.getItem() == matchStack.getItem()) {
                                    used[i] = true;
                                    found = true;
                                    break;
                                }
                            }
                            if (found) {
                                iterator.remove();
                                break;
                            }
                        }
                    } else if (ingredient.apply(stackInSlot)) {
                        used[i] = true;
                        iterator.remove();
                        found = true;
                        break;
                    }
                }
            }

            if (!found) {
                // Si on n'a pas trouvé un ingrédient dans la grille, vérifier les slots spéciaux
                boolean foundInSpecial = false;
                for (int i = 0; i < 2; i++) {
                    if (specialSlotUsage[i]) {
                        ItemStack specialStack = inv.getStackInSlot(36 + i);
                        if (!specialStack.isEmpty()) {
                            // Vérification pour les outils
                            if (isTool(specialStack.getItem())) {
                                for (ItemStack matchStack : ingredient.getMatchingStacks()) {
                                    if (specialStack.getItem() == matchStack.getItem()) {
                                        iterator.remove();
                                        foundInSpecial = true;
                                        break;
                                    }
                                }
                                if (foundInSpecial) break;
                            }
                            else if (ingredient.apply(specialStack)) {
                                iterator.remove();
                                foundInSpecial = true;
                                break;
                            }
                        }
                    }
                }

                if (!foundInSpecial) {
                    return false;
                }
            }
        }

        // Vérifier que toutes les cases non-vides sont utilisées
        for (int i = 0; i < 36; i++) {
            if (!inv.getStackInSlot(i).isEmpty() && !used[i]) {
                // Item présent mais non utilisé par la recette
                return false;
            }
        }

        // Vérifier les slots spéciaux si nécessaire
        for (int i = 0; i < 2; i++) {
            if (specialSlotUsage[i]) {
                ItemStack specialStack = inv.getStackInSlot(36 + i);
                // Si slot spécial vide mais requis
                if (specialStack.isEmpty()) {
                    return false;
                }
                // Si spécial slot ne correspond pas
                if (!specialSlotIngredients[i].apply(specialStack)) {
                    boolean isToolMatch = false;
                    if (isTool(specialStack.getItem())) {
                        for (ItemStack matchStack : specialSlotIngredients[i].getMatchingStacks()) {
                            if (specialStack.getItem() == matchStack.getItem()) {
                                isToolMatch = true;
                                break;
                            }
                        }
                    }
                    if (!isToolMatch) {
                        return false;
                    }
                }
            }
        }

        return ingredients.isEmpty();
    }

    /**
     * Vérifie si l'item est un outil qui doit être préservé
     */
    private boolean isTool(Item item) {return item == ModTools.SCISSORS || item == ModTools.SCALPEL || item == ModTools.SICKLE || item == ModTools.GOLDEN_SICKLE ||
            item instanceof MedicToolItem;
    }

    @Override
    public int getRecipeSize() {
        return getIngredients().size() + (specialSlotUsage[0] ? 1 : 0) + (specialSlotUsage[1] ? 1 : 0);
    }

    @Override
    public int getLevelRequired() {
        return levelRequired;
    }

    @Override
    public int getXpReward() {
        return xpReward;
    }

    @Override
    public int getBaseXpReward() {
        return xpReward;
    }

    // Version améliorée de getCraftingResult pour gérer les outils
    @Override
    public CraftingResult getCraftingResult(IMinePieceCraftingInventory inventory) {
        if (!(inventory instanceof InventoryCrafting)) {
            return new CraftingResult(ItemStack.EMPTY, 0, new ArrayList<>());
        }

        InventoryCrafting craftMatrix = (InventoryCrafting) inventory;

        // Vérifier si tous les ingrédients sont présents
        if (!matches(craftMatrix, inventory.getWorld())) {
            return new CraftingResult(ItemStack.EMPTY, 0, new ArrayList<>());
        }

        // Garder la trace des ingrédients utilisés
        Map<Integer, ItemStack> usedIngredients = new HashMap<>();
        List<Ingredient> remainingIngredients = new ArrayList<>(this.getIngredients());
        boolean[] used = new boolean[36];

        // Trouver quels slots sont utilisés pour les ingrédients
        for (Iterator<Ingredient> iterator = remainingIngredients.iterator(); iterator.hasNext();) {
            Ingredient ingredient = iterator.next();
            boolean found = false;

            // Chercher d'abord dans les slots normaux
            for (int i = 0; i < 36; i++) {
                if (!used[i]) {
                    ItemStack stackInSlot = craftMatrix.getStackInSlot(i);
                    if (!stackInSlot.isEmpty()) {
                        if (isTool(stackInSlot.getItem())) {
                            // Vérification spéciale pour les outils
                            for (ItemStack matchStack : ingredient.getMatchingStacks()) {
                                if (stackInSlot.getItem() == matchStack.getItem()) {
                                    usedIngredients.put(i, stackInSlot);
                                    used[i] = true;
                                    found = true;
                                    break;
                                }
                            }
                        } else if (ingredient.apply(stackInSlot)) {
                            usedIngredients.put(i, stackInSlot);
                            used[i] = true;
                            found = true;
                        }

                        if (found) {
                            iterator.remove();
                            break;
                        }
                    }
                }
            }

            // Si pas trouvé, chercher dans les slots spéciaux
            if (!found) {
                for (int i = 0; i < 2; i++) {
                    if (specialSlotUsage[i]) {
                        ItemStack specialStack = craftMatrix.getStackInSlot(36 + i);
                        if (!specialStack.isEmpty()) {
                            if (isTool(specialStack.getItem())) {
                                // Vérification spéciale pour les outils
                                for (ItemStack matchStack : ingredient.getMatchingStacks()) {
                                    if (specialStack.getItem() == matchStack.getItem()) {
                                        usedIngredients.put(36 + i, specialStack);
                                        found = true;
                                        break;
                                    }
                                }
                            } else if (ingredient.apply(specialStack)) {
                                usedIngredients.put(36 + i, specialStack);
                                found = true;
                            }

                            if (found) {
                                iterator.remove();
                                break;
                            }
                        }
                    }
                }
            }
        }

        // Déterminer combien de fois la recette peut être faite
        int minMultiplier = Integer.MAX_VALUE;
        for (Map.Entry<Integer, ItemStack> entry : usedIngredients.entrySet()) {
            ItemStack stack = entry.getValue();
            if (!stack.isEmpty() && !isTool(stack.getItem())) {
                minMultiplier = Math.min(minMultiplier, stack.getCount());
            }
        }

        if (minMultiplier == Integer.MAX_VALUE || minMultiplier <= 0) {
            minMultiplier = 1; // Par défaut au moins 1 craft si on n'a que des outils
        }

        // Créer la liste des items à consommer
        List<CraftingResult.ItemPosition> consumedItems = new ArrayList<>();

        for (Map.Entry<Integer, ItemStack> entry : usedIngredients.entrySet()) {
            int slotIndex = entry.getKey();
            ItemStack stack = entry.getValue();

            if (!stack.isEmpty()) {
                if (!isTool(stack.getItem())) {
                    // Consommer les ingrédients normaux
                    consumedItems.add(new CraftingResult.ItemPosition(slotIndex, minMultiplier));
                } else {
                    // Ne pas consommer les outils (ils seront gérés par le Container)
                    consumedItems.add(new CraftingResult.ItemPosition(slotIndex, 0));
                }
            }
        }

        // Créer le résultat
        ItemStack craftedResult = getRecipeOutput().copy();
        craftedResult.setCount(craftedResult.getCount() * minMultiplier);

        return new CraftingResult(craftedResult, minMultiplier, consumedItems);
    }

    @Override
    public ItemStack getCraftingResult(InventoryCrafting inv) {
        return getRecipeOutput().copy();
    }

    @Override
    public boolean usesSpecialSlot(int slotIndex) {
        if (slotIndex < 0 || slotIndex >= specialSlotUsage.length) {
            return false;
        }
        return specialSlotUsage[slotIndex];
    }

    @Override
    public String toString() {
        return "ShapelessPaillasseRecipe{" +
                "output=" + getRecipeOutput().getDisplayName() +
                ", recipeId=" + recipeId +
                ", levelRequired=" + levelRequired +
                ", xpReward=" + xpReward +
                '}';
    }

    public static class RecipeFactory implements IRecipeFactory {
        @Override
        public IRecipe parse(JsonContext context, JsonObject json) {
            String group = JsonUtils.getString(json, "group", "");
            LOGGER.debug("Parsing shapeless recipe JSON: {}", json);

            try {
                NonNullList<Ingredient> ingredients = NonNullList.create();
                for (JsonElement element : JsonUtils.getJsonArray(json, "ingredients")) {
                    ingredients.add(CraftingHelper.getIngredient(element, context));
                }

                if (ingredients.isEmpty()) {
                    throw new JsonParseException("No ingredients for shapeless recipe");
                }

                // Parse les slots spéciaux
                boolean[] specialSlotUsage = new boolean[2];
                Ingredient[] specialSlotIngredients = new Ingredient[2];

                if (json.has("special_slots")) {
                    JsonObject specialSlots = JsonUtils.getJsonObject(json, "special_slots");
                    if (specialSlots.has("slot1")) {
                        specialSlotUsage[0] = true;
                        specialSlotIngredients[0] = CraftingHelper.getIngredient(specialSlots.get("slot1"), context);
                    }
                    if (specialSlots.has("slot2")) {
                        specialSlotUsage[1] = true;
                        specialSlotIngredients[1] = CraftingHelper.getIngredient(specialSlots.get("slot2"), context);
                    }
                }

                ItemStack result = ShapedRecipes.deserializeItem(JsonUtils.getJsonObject(json, "result"), true);
                int levelRequired = JsonUtils.getInt(json, "level_required", 1);
                int xpReward = JsonUtils.getInt(json, "xp_reward", 0);

                return new ShapelessPaillasseRecipe(
                        group.isEmpty() ? null : new ResourceLocation(group),
                        ingredients,
                        result,
                        levelRequired,
                        xpReward,
                        specialSlotUsage,
                        specialSlotIngredients
                );
            } catch (Exception e) {
                LOGGER.error("Error parsing shapeless recipe: {}", e.getMessage());
                LOGGER.error("Recipe JSON: {}", json);
                e.printStackTrace();
                throw e;
            }
        }
    }
}