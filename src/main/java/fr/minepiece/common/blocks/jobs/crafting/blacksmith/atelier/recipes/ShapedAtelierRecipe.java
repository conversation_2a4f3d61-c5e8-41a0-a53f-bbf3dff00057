package fr.minepiece.common.blocks.jobs.crafting.blacksmith.atelier.recipes;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonSyntaxException;
import fr.minepiece.common.api.jobs.MinePieceJobs;
import fr.minepiece.common.craft.AbstractShapedMinePieceRecipe;
import fr.minepiece.common.craft.CraftingResult;
import fr.minepiece.common.craft.IMinePieceCraftingInventory;
import fr.minepiece.common.init.ModTools;
import net.minecraft.inventory.InventoryCrafting;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.crafting.IRecipe;
import net.minecraft.item.crafting.Ingredient;
import net.minecraft.nbt.JsonToNBT;
import net.minecraft.nbt.NBTException;
import net.minecraft.nbt.NBTTagCompound;
import net.minecraft.util.JsonUtils;
import net.minecraft.util.NonNullList;
import net.minecraft.util.ResourceLocation;
import net.minecraft.world.World;
import net.minecraftforge.common.crafting.CraftingHelper;
import net.minecraftforge.common.crafting.IRecipeFactory;
import net.minecraftforge.common.crafting.JsonContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.annotation.Nonnull;
import java.util.*;

public class ShapedAtelierRecipe extends AbstractShapedMinePieceRecipe implements IAtelierRecipe {
    private static final Logger LOGGER = LogManager.getLogger("MinePiece Recipes");
    private static final int MAX_GRID_WIDTH = 6;
    private static final int MAX_GRID_HEIGHT = 6;

    private final int recipeWidth;
    private final int recipeHeight;
    private final boolean[] specialSlotUsage; // Indique les slots spéciaux utilisés (0=Input1, 1=Input2)
    private final Ingredient[] specialSlotIngredients; // Ingrédients pour les slots spéciaux

    public ShapedAtelierRecipe(ResourceLocation group, @Nonnull ItemStack result,
                               NonNullList<Ingredient> ingredients, int width, int height,
                               int levelRequired, int xpReward,
                               boolean[] specialSlotUsage, Ingredient[] specialSlotIngredients) {
        super(group, result, ingredients, width, height, levelRequired, xpReward, MinePieceJobs.BLACKSMITH);
        this.recipeWidth = width;
        this.recipeHeight = height;
        this.specialSlotUsage = specialSlotUsage;
        this.specialSlotIngredients = specialSlotIngredients;

        LOGGER.debug("Created ShapedAtelierRecipe: {}x{}, result: {}, level: {}, xp: {}",
                width, height, result.getDisplayName(), levelRequired, xpReward);
    }

    @Override
    public boolean matches(InventoryCrafting inv, World world) {
        // Parcourir toutes les positions possibles dans la grille
        for (int startX = 0; startX <= MAX_GRID_WIDTH - this.recipeWidth; ++startX) {
            for (int startY = 0; startY <= MAX_GRID_HEIGHT - this.recipeHeight; ++startY) {
                if (this.checkMatch(inv, startX, startY, false)) {
                    // Si aucun slot spécial n'est utilisé OU si les slots spéciaux correspondent
                    if (!specialSlotUsage[0] && !specialSlotUsage[1]) {
                        return true;
                    } else if (checkSpecialSlots(inv)) {
                        return true;
                    }
                }

                // Version miroir (comme dans le vanilla)
                if (this.checkMatch(inv, startX, startY, true)) {
                    // Si aucun slot spécial n'est utilisé OU si les slots spéciaux correspondent
                    if (!specialSlotUsage[0] && !specialSlotUsage[1]) {
                        return true;
                    } else if (checkSpecialSlots(inv)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Vérifie si les slots spéciaux contiennent les ingrédients requis
     */
    private boolean checkSpecialSlots(InventoryCrafting inv) {
        for (int i = 0; i < 2; i++) {
            if (specialSlotUsage[i]) {
                ItemStack specialItem = inv.getStackInSlot(36 + i);

                // Si le slot est vide mais requis
                if (specialItem.isEmpty()) {
                    return false;
                }

                // Vérification spéciale pour les outils dans les slots spéciaux
                if (!specialItem.isEmpty() && isTool(specialItem.getItem())) {
                    boolean toolMatches = false;
                    for (ItemStack matchStack : specialSlotIngredients[i].getMatchingStacks()) {
                        if (specialItem.getItem() == matchStack.getItem()) {
                            toolMatches = true;
                            break;
                        }
                    }
                    if (!toolMatches) {
                        return false;
                    }
                } else if (!specialSlotIngredients[i].apply(specialItem)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * Vérifie si le pattern correspond à un emplacement spécifique dans la grille
     * Basé sur l'implémentation vanilla mais adapté pour vérifier aussi
     * que les cases hors du pattern sont bien vides
     */
    public boolean checkMatch(InventoryCrafting inv, int startX, int startY, boolean mirrored) {
        // D'abord vérifier que toutes les cases hors du pattern actuel sont vides
        for (int x = 0; x < MAX_GRID_WIDTH; x++) {
            for (int y = 0; y < MAX_GRID_HEIGHT; y++) {
                // Si la position est en dehors du pattern
                if (x < startX || x >= startX + recipeWidth ||
                        y < startY || y >= startY + recipeHeight) {

                    // Cette case doit être vide
                    int invIndex = y * MAX_GRID_WIDTH + x;
                    if (invIndex < 36 && !inv.getStackInSlot(invIndex).isEmpty()) {
                        return false;
                    }
                }
            }
        }

        // Ensuite vérifier que le pattern correspond
        for (int y = 0; y < recipeHeight; y++) {
            for (int x = 0; x < recipeWidth; x++) {
                int invX = startX + x;
                int invY = startY + y;

                // Skip si en dehors de la grille (bien que cela ne devrait pas arriver)
                if (invX >= MAX_GRID_WIDTH || invY >= MAX_GRID_HEIGHT) {
                    continue;
                }

                int invIndex = invY * MAX_GRID_WIDTH + invX;

                // Ici on skip aussi les vérifications pour les ingrédients vides
                if (invIndex >= 36) {
                    continue;
                }

                Ingredient expectedIngredient;
                if (mirrored) {
                    expectedIngredient = ingredients.get((recipeWidth - x - 1) + y * recipeWidth);
                } else {
                    expectedIngredient = ingredients.get(x + y * recipeWidth);
                }

                ItemStack stack = inv.getStackInSlot(invIndex);

                // Si l'ingrédient attendu est vide, on s'attend à un slot vide
                if (expectedIngredient == Ingredient.EMPTY) {
                    if (!stack.isEmpty()) {
                        return false;
                    }
                    continue;
                }

                // Vérification pour les outils spéciaux
                if (!stack.isEmpty() && isTool(stack.getItem())) {
                    boolean toolMatches = false;
                    for (ItemStack matchStack : expectedIngredient.getMatchingStacks()) {
                        if (stack.getItem() == matchStack.getItem()) {
                            toolMatches = true;
                            break;
                        }
                    }
                    if (!toolMatches) {
                        return false;
                    }
                }
                // Vérification normale
                else if (!expectedIngredient.apply(stack)) {
                    return false;
                }
            }
        }

        return true;
    }

    @Override
    public boolean matches(IMinePieceCraftingInventory inventory, World world) {
        if (inventory instanceof InventoryCrafting) {
            return matches((InventoryCrafting) inventory, world);
        }
        return false;
    }

    @Override
    public CraftingResult getCraftingResult(IMinePieceCraftingInventory inventory) {
        if (!(inventory instanceof InventoryCrafting)) {
            return new CraftingResult(ItemStack.EMPTY, 0, new ArrayList<>());
        }

        InventoryCrafting craftMatrix = (InventoryCrafting) inventory;

        // Vérifier si tous les ingrédients sont présents
        if (!matches(craftMatrix, inventory.getWorld())) {
            return new CraftingResult(ItemStack.EMPTY, 0, new ArrayList<>());
        }

        // Garder la trace des ingrédients utilisés
        Map<Integer, ItemStack> usedIngredients = new HashMap<>();
        List<Ingredient> remainingIngredients = new ArrayList<>(this.getIngredients());
        boolean[] used = new boolean[36];

        // Trouver quels slots sont utilisés pour les ingrédients
        for (Iterator<Ingredient> iterator = remainingIngredients.iterator(); iterator.hasNext();) {
            Ingredient ingredient = iterator.next();
            boolean found = false;

            // Chercher d'abord dans les slots normaux
            for (int i = 0; i < 36; i++) {
                if (!used[i]) {
                    ItemStack stackInSlot = craftMatrix.getStackInSlot(i);
                    if (!stackInSlot.isEmpty()) {
                        if (isTool(stackInSlot.getItem())) {
                            // Vérification spéciale pour les outils
                            for (ItemStack matchStack : ingredient.getMatchingStacks()) {
                                if (stackInSlot.getItem() == matchStack.getItem()) {
                                    usedIngredients.put(i, stackInSlot);
                                    used[i] = true;
                                    found = true;
                                    break;
                                }
                            }
                        } else if (ingredient.apply(stackInSlot)) {
                            usedIngredients.put(i, stackInSlot);
                            used[i] = true;
                            found = true;
                        }

                        if (found) {
                            iterator.remove();
                            break;
                        }
                    }
                }
            }

            // Si pas trouvé, chercher dans les slots spéciaux
            if (!found) {
                for (int i = 0; i < 2; i++) {
                    if (specialSlotUsage[i]) {
                        ItemStack specialStack = craftMatrix.getStackInSlot(36 + i);
                        if (!specialStack.isEmpty()) {
                            if (isTool(specialStack.getItem())) {
                                // Vérification spéciale pour les outils
                                for (ItemStack matchStack : ingredient.getMatchingStacks()) {
                                    if (specialStack.getItem() == matchStack.getItem()) {
                                        usedIngredients.put(36 + i, specialStack);
                                        found = true;
                                        break;
                                    }
                                }
                            } else if (ingredient.apply(specialStack)) {
                                usedIngredients.put(36 + i, specialStack);
                                found = true;
                            }

                            if (found) {
                                iterator.remove();
                                break;
                            }
                        }
                    }
                }
            }
        }

        // Déterminer combien de fois la recette peut être faite
        int minMultiplier = Integer.MAX_VALUE;
        for (Map.Entry<Integer, ItemStack> entry : usedIngredients.entrySet()) {
            ItemStack stack = entry.getValue();
            if (!stack.isEmpty() && !isTool(stack.getItem())) {
                minMultiplier = Math.min(minMultiplier, stack.getCount());
            }
        }

        if (minMultiplier == Integer.MAX_VALUE || minMultiplier <= 0) {
            minMultiplier = 1; // Par défaut au moins 1 craft si on n'a que des outils
        }

        // Créer la liste des items à consommer
        List<CraftingResult.ItemPosition> consumedItems = new ArrayList<>();

        for (Map.Entry<Integer, ItemStack> entry : usedIngredients.entrySet()) {
            int slotIndex = entry.getKey();
            ItemStack stack = entry.getValue();

            if (!stack.isEmpty()) {
                if (!isTool(stack.getItem())) {
                    // Consommer les ingrédients normaux
                    consumedItems.add(new CraftingResult.ItemPosition(slotIndex, minMultiplier));
                } else {
                    // Ne pas consommer les outils (ils seront gérés par le Container)
                    consumedItems.add(new CraftingResult.ItemPosition(slotIndex, 0));
                }
            }
        }

        // Créer le résultat
        ItemStack craftedResult = getRecipeOutput().copy();
        craftedResult.setCount(craftedResult.getCount() * minMultiplier);

        return new CraftingResult(craftedResult, minMultiplier, consumedItems);
    }

    /**
     * Trouve les ingrédients utilisés par la recette dans la matrice de craft
     */
    private Map<Integer, ItemStack> findUsedIngredients(InventoryCrafting craftMatrix) {
        Map<Integer, ItemStack> usedIngredients = new HashMap<>();

        // Trouver le pattern dans la grille
        patternSearch:
        for (int startX = 0; startX <= MAX_GRID_WIDTH - this.recipeWidth; ++startX) {
            for (int startY = 0; startY <= MAX_GRID_HEIGHT - this.recipeHeight; ++startY) {
                boolean found = false;
                boolean mirrored = false;

                // Essayer le pattern normal
                if (this.checkMatch(craftMatrix, startX, startY, false)) {
                    found = true;
                    mirrored = false;
                }
                // Essayer le pattern miroir
                else if (this.checkMatch(craftMatrix, startX, startY, true)) {
                    found = true;
                    mirrored = true;
                }

                if (found) {
                    // Marquer les ingrédients utilisés
                    for (int y = 0; y < this.recipeHeight; ++y) {
                        for (int x = 0; x < this.recipeWidth; ++x) {
                            int patternIndex = mirrored ?
                                    (this.recipeWidth - x - 1) + y * this.recipeWidth :
                                    x + y * this.recipeWidth;

                            if (this.ingredients.get(patternIndex) != Ingredient.EMPTY) {
                                int invIndex = (startY + y) * MAX_GRID_WIDTH + (startX + x);
                                if (invIndex < 36) { // Vérification de sécurité
                                    usedIngredients.put(invIndex, craftMatrix.getStackInSlot(invIndex));
                                }
                            }
                        }
                    }
                    break patternSearch;
                }
            }
        }

        return usedIngredients;
    }

    @Override
    public boolean usesSpecialSlot(int slotIndex) {
        if (slotIndex < 0 || slotIndex >= specialSlotUsage.length) {
            return false;
        }
        return specialSlotUsage[slotIndex];
    }

    @Override
    public ItemStack getCraftingResult(InventoryCrafting inv) {
        return getRecipeOutput().copy();
    }

    @Override
    public int getRecipeSize() {
        return recipeWidth * recipeHeight;
    }

    @Override
    public ItemStack getRecipeOutput() {
        return super.getRecipeOutput();
    }

    @Override
    public int getXpReward() {
        return super.getBaseXpReward();
    }

    @Override
    public MinePieceJobs getRequiredJob() {
        return MinePieceJobs.BLACKSMITH;
    }

    /**
     * Donne accès aux ingrédients des slots spéciaux
     */
    public Ingredient[] getSpecialSlotIngredients() {
        return specialSlotIngredients;
    }

    private boolean isTool(Item item) {
        return item == ModTools.MARTEAU || item == ModTools.PINCE || item == ModTools.SCISSORS;
    }

    public int getWidth() {
        return recipeWidth;
    }

    public int getHeight() {
        return recipeHeight;
    }

    public static class RecipeFactory implements IRecipeFactory {
        @Override
        public IRecipe parse(JsonContext context, JsonObject json) {
            String group = JsonUtils.getString(json, "group", "");

            try {
                // Parse la map des ingrédients
                Map<Character, Ingredient> ingredientMap = new HashMap<>();
                for (Map.Entry<String, JsonElement> entry : JsonUtils.getJsonObject(json, "key").entrySet()) {
                    if (entry.getKey().length() != 1) {
                        throw new JsonSyntaxException(
                                "Invalid key entry: '" + entry.getKey() + "' is an invalid symbol (must be 1 character only)."
                        );
                    }
                    if (" ".equals(entry.getKey())) {
                        throw new JsonSyntaxException("Invalid key entry: ' ' is a reserved symbol.");
                    }
                    ingredientMap.put(entry.getKey().toCharArray()[0],
                            CraftingHelper.getIngredient(entry.getValue(), context));
                }
                ingredientMap.put(' ', Ingredient.EMPTY);

                // Obtenir le pattern
                JsonArray pattern = JsonUtils.getJsonArray(json, "pattern");
                if (pattern.size() == 0) {
                    throw new JsonSyntaxException("Invalid pattern: empty pattern not allowed");
                }

                if (pattern.size() > MAX_GRID_HEIGHT) {
                    throw new JsonSyntaxException("Invalid pattern: too many rows, " + MAX_GRID_HEIGHT + " is maximum");
                }

                String[] rows = new String[pattern.size()];
                for (int i = 0; i < rows.length; i++) {
                    String row = JsonUtils.getString(pattern.get(i), "pattern[" + i + "]");
                    if (row.length() > MAX_GRID_WIDTH) {
                        throw new JsonSyntaxException("Invalid pattern: too many columns, " + MAX_GRID_WIDTH + " is maximum");
                    }
                    rows[i] = row;
                }

                // Mesurer les dimensions réelles du pattern (après retrait des espaces superflus)
                int width = rows[0].length();
                int height = rows.length;

                // Créer la liste d'ingrédients
                NonNullList<Ingredient> ingredients = NonNullList.withSize(width * height, Ingredient.EMPTY);

                // Remplir la liste avec les ingrédients
                Set<Character> keys = new HashSet<>(ingredientMap.keySet());
                keys.remove(' ');

                for (int y = 0; y < height; ++y) {
                    String row = rows[y];
                    for (int x = 0; x < row.length(); ++x) {
                        char c = row.charAt(x);
                        Ingredient ingredient = ingredientMap.get(c);
                        if (ingredient == null) {
                            throw new JsonSyntaxException("Pattern references symbol '" + c + "' but it's not defined in the key");
                        }
                        ingredients.set(x + y * width, ingredient);
                        keys.remove(c);
                    }
                }

                if (!keys.isEmpty()) {
                    throw new JsonSyntaxException("Key defines symbols that aren't used in pattern: " + keys);
                }

                // Parse les slots spéciaux
                boolean[] specialSlotUsage = new boolean[2];
                Ingredient[] specialSlotIngredients = new Ingredient[2];

                if (json.has("special_slots")) {
                    JsonObject specialSlots = JsonUtils.getJsonObject(json, "special_slots");
                    if (specialSlots.has("slot1")) {
                        specialSlotUsage[0] = true;
                        specialSlotIngredients[0] = CraftingHelper.getIngredient(specialSlots.get("slot1"), context);
                    }
                    if (specialSlots.has("slot2")) {
                        specialSlotUsage[1] = true;
                        specialSlotIngredients[1] = CraftingHelper.getIngredient(specialSlots.get("slot2"), context);
                    }
                }

                // Parse le résultat et les exigences
                JsonObject resultJson = JsonUtils.getJsonObject(json, "result");
                ItemStack result = CraftingHelper.getItemStack(resultJson, context);

                // Vérifier s'il y a des données NBT à appliquer au résultat
                if (resultJson.has("nbt")) {
                    try {
                        String nbtString = resultJson.get("nbt").getAsString();
                        NBTTagCompound nbt = JsonToNBT.getTagFromJson(nbtString);
                        result.setTagCompound(nbt);
                    } catch (NBTException e) {
                        throw new JsonSyntaxException("Invalid NBT for recipe result: " + e.getMessage());
                    }
                }
                int levelRequired = JsonUtils.getInt(json, "level_required", 1);
                int xpReward = JsonUtils.getInt(json, "xp_reward", 0);

                // Crée la recette
                return new ShapedAtelierRecipe(
                        group.isEmpty() ? null : new ResourceLocation(group),
                        result,
                        ingredients,
                        width,
                        height,
                        levelRequired,
                        xpReward,
                        specialSlotUsage,
                        specialSlotIngredients
                );
            } catch (Exception e) {
                LOGGER.error("Error parsing recipe: {}", e.getMessage());
                throw e;
            }
        }
    }
}