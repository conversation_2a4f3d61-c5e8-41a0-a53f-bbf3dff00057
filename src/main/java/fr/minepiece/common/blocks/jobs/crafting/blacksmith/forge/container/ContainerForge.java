package fr.minepiece.common.blocks.jobs.crafting.blacksmith.forge.container;

import fr.minepiece.common.api.jobs.MinePieceJobs;
import fr.minepiece.common.api.utils.PlayerUtils;
import fr.minepiece.common.blocks.jobs.crafting.blacksmith.forge.recipes.ForgeRecipeManager;
import fr.minepiece.common.blocks.jobs.crafting.blacksmith.forge.recipes.IForgeRecipe;
import fr.minepiece.common.blocks.jobs.crafting.blacksmith.forge.slots.SlotForgeOutput;
import fr.minepiece.common.blocks.jobs.crafting.blacksmith.recipes.AtelierToForgeAdapter;
import fr.minepiece.common.blocks.jobs.crafting.blacksmith.recipes.ShapelessAtelierToForgeAdapter;
import fr.minepiece.common.capability.mpdata.IMinePieceData;
import fr.minepiece.common.capability.mpdata.MinePieceDataStorage;
import fr.minepiece.common.init.ModTools;
import fr.minepiece.common.items.jobs.blacksmith.BlacksmithToolItem;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.entity.player.InventoryPlayer;
import net.minecraft.init.Items;
import net.minecraft.inventory.Container;
import net.minecraft.inventory.IInventory;
import net.minecraft.inventory.InventoryCraftResult;
import net.minecraft.inventory.InventoryCrafting;
import net.minecraft.inventory.Slot;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.crafting.CraftingManager;
import net.minecraft.item.crafting.IRecipe;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.world.World;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;

public class ContainerForge extends Container {
    private static final Logger LOGGER = LogManager.getLogger("MinePiece Craft");
    private static final int STATION_TYPE = 2; // 2 = Forge

    public InventoryCrafting craftMatrix;
    public InventoryCraftResult craftResult;
    private final World world;
    private final BlockPos pos;
    private final EntityPlayer player;
    private boolean hasProcessedRecipe = false;

    public ContainerForge(InventoryPlayer playerInventory, World worldIn, BlockPos posIn) {
        this.world = worldIn;
        this.pos = posIn;
        this.player = playerInventory.player;

        // Grille de craft 6x6 + 2 slots spéciaux = 38 slots
        this.craftMatrix = new InventoryCrafting(this, 6, 7); // 42 slots au total
        this.craftResult = new InventoryCraftResult();

        // Slot de sortie standard
        this.addSlotToContainer(new SlotForgeOutput(playerInventory.player, this.craftMatrix, this.craftResult, 0, 143, 76));

        // Grille de craft 6x6 (slots 1-36)
        for (int row = 0; row < 6; row++) {
            for (int col = 0; col < 6; col++) {
                this.addSlotToContainer(new Slot(this.craftMatrix, col + row * 6, 13 + col * 18, 32 + row * 18));
            }
        }

        // Slots spéciaux (37-38)
        this.addSlotToContainer(new Slot(this.craftMatrix, 36, 13, 10)); // Input1
        this.addSlotToContainer(new Slot(this.craftMatrix, 37, 125, 122)); // Input2

        // Inventaire joueur
        for (int row = 0; row < 3; row++) {
            for (int col = 0; col < 9; col++) {
                this.addSlotToContainer(new Slot(playerInventory, col + row * 9 + 9, 8 + col * 18, 166 + row * 18));
            }
        }

        // Hotbar
        for (int col = 0; col < 9; col++) {
            this.addSlotToContainer(new Slot(playerInventory, col, 8 + col * 18, 224));
        }
    }

    @Override
    public void onCraftMatrixChanged(IInventory inventoryIn) {
        // Réinitialisation
        hasProcessedRecipe = false;

        // Trouver une recette correspondante
        IRecipe matchingRecipe = null;
        ItemStack resultStack = ItemStack.EMPTY;

        // Initialiser les adaptateurs si nécessaire
        ForgeRecipeManager.initializeAdapters();

        // Vérifier les recettes de Forge natives d'abord
        for (IRecipe recipe : CraftingManager.REGISTRY) {
            if (recipe instanceof IForgeRecipe) {
                IForgeRecipe forgeRecipe = (IForgeRecipe) recipe;
                IMinePieceData mpData = player.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);

                if (mpData != null && mpData.getJob() == MinePieceJobs.BLACKSMITH &&
                        mpData.getJobLevel() >= forgeRecipe.getLevelRequired() &&
                        mpData.getDailyCraftLimit() > 0 &&
                        forgeRecipe.matches(this.craftMatrix, this.world)) {

                    matchingRecipe = recipe;
                    resultStack = recipe.getCraftingResult(this.craftMatrix);
                    break;
                }
            }
        }

        // Si aucune recette native n'est trouvée, essayer les recettes adaptées
        if (matchingRecipe == null) {
            // Essayer les recettes adaptées shaped
            for (AtelierToForgeAdapter adapter : ForgeRecipeManager.getShapedAdaptedRecipes()) {
                IMinePieceData mpData = player.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);

                if (mpData != null && mpData.getJob() == MinePieceJobs.BLACKSMITH &&
                        mpData.getJobLevel() >= adapter.getLevelRequired() &&
                        mpData.getDailyCraftLimit() > 0 &&
                        adapter.matches(this.craftMatrix, this.world)) {

                    matchingRecipe = (IRecipe) adapter;
                    resultStack = adapter.getCraftingResult(this.craftMatrix);
                    break;
                }
            }

            // Essayer les recettes adaptées shapeless si toujours rien trouvé
            if (matchingRecipe == null) {
                for (ShapelessAtelierToForgeAdapter adapter : ForgeRecipeManager.getShapelessAdaptedRecipes()) {
                    IMinePieceData mpData = player.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);

                    if (mpData != null && mpData.getJob() == MinePieceJobs.BLACKSMITH &&
                            mpData.getJobLevel() >= adapter.getLevelRequired() &&
                            mpData.getDailyCraftLimit() > 0 &&
                            adapter.matches(this.craftMatrix, this.world)) {

                        matchingRecipe = (IRecipe) adapter;
                        resultStack = adapter.getCraftingResult(this.craftMatrix);
                        break;
                    }
                }
            }
        }

        // Mettre à jour le slot de résultat
        this.craftResult.setInventorySlotContents(0, resultStack);

        if (matchingRecipe != null) {
            this.craftResult.setRecipeUsed(matchingRecipe);
        } else {
            this.craftResult.setRecipeUsed(null);
        }
    }

    @Override
    public ItemStack slotClick(int slotId, int dragType, net.minecraft.inventory.ClickType clickTypeIn, EntityPlayer playerIn) {
        // Sauvegarde des outils avant de les manipuler
        Map<Integer, ItemStack> toolsBefore = new HashMap<>();

        // Identifier les outils dans la matrice de craft
        for (int i = 0; i < this.craftMatrix.getSizeInventory(); i++) {
            ItemStack stack = this.craftMatrix.getStackInSlot(i);
            if (!stack.isEmpty() && isSpecialTool(stack.getItem())) {
                toolsBefore.put(i, stack.copy());
            }
        }

        // Vérifier si c'est le slot de résultat
        if (slotId == 0 && !hasProcessedRecipe && !this.craftResult.getStackInSlot(0).isEmpty()) {
            IRecipe recipe = this.craftResult.getRecipeUsed();

            if (recipe instanceof IForgeRecipe) {
                hasProcessedRecipe = true;
                handleCraftCompletion((IForgeRecipe) recipe);
            }
        }

        // Appeler la méthode parent
        ItemStack result = super.slotClick(slotId, dragType, clickTypeIn, playerIn);

        // Si on a cliqué sur le slot de résultat, gérer les outils spécialement
        if (slotId == 0 && !result.isEmpty()) {
            restoreToolsWithDamage(toolsBefore);
        }

        return result;
    }

    /**
     * Restaure les outils avec un niveau d'usure après le craft
     */
    private void restoreToolsWithDamage(Map<Integer, ItemStack> toolsBefore) {
        if (toolsBefore.isEmpty()) return;

        for (Map.Entry<Integer, ItemStack> entry : toolsBefore.entrySet()) {
            int slotIndex = entry.getKey();
            ItemStack toolStack = entry.getValue().copy();

            // Vérifier que l'outil n'est plus dans le slot (consommé par la recette)
            if (this.craftMatrix.getStackInSlot(slotIndex).isEmpty()) {
                // Endommager l'outil
                if (toolStack.getItem().isDamageable()) {
                    boolean broke = false;
                    if (player instanceof EntityPlayerMP) {
                        broke = toolStack.attemptDamageItem(1, player.getRNG(), (EntityPlayerMP) player);
                    } else {
                        toolStack.setItemDamage(toolStack.getItemDamage() + 1);
                        broke = toolStack.getItemDamage() >= toolStack.getMaxDamage();
                    }

                    if (!broke) {
                        // Remettre l'outil endommagé dans la matrice
                        this.craftMatrix.setInventorySlotContents(slotIndex, toolStack);
                    }
                } else {
                    // Remettre l'outil tel quel s'il n'est pas endommageable
                    this.craftMatrix.setInventorySlotContents(slotIndex, toolStack);
                }
            }
        }
    }

    @Override
    public ItemStack transferStackInSlot(EntityPlayer playerIn, int index) {
        ItemStack itemstack = ItemStack.EMPTY;
        Slot slot = this.inventorySlots.get(index);

        if (slot != null && slot.getHasStack()) {
            ItemStack itemstack1 = slot.getStack();
            itemstack = itemstack1.copy();

            // Si on fait shift+clic sur le résultat
            if (index == 0) {
                // Récupérer la recette avant de faire des modifications
                IRecipe recipe = this.craftResult.getRecipeUsed();

                // Cloner l'état actuel des outils dans la matrice pour les restaurer après
                Map<Integer, ItemStack> toolSlots = new HashMap<>();
                for (int i = 0; i < this.craftMatrix.getSizeInventory(); i++) {
                    ItemStack stack = this.craftMatrix.getStackInSlot(i);
                    if (!stack.isEmpty() && isSpecialTool(stack.getItem())) {
                        // Retirer l'outil du slot pour qu'il ne soit pas consommé
                        toolSlots.put(i, stack.copy());
                        this.craftMatrix.setInventorySlotContents(i, ItemStack.EMPTY);
                    }
                }

                // Traiter le craft
                if (recipe instanceof IForgeRecipe && !hasProcessedRecipe) {
                    hasProcessedRecipe = true;
                    handleCraftCompletion((IForgeRecipe) recipe);
                }

                // Transfert vers l'inventaire
                if (!this.mergeItemStack(itemstack1, 39, 75, true)) {
                    // Restaurer les outils si le transfert a échoué
                    for (Map.Entry<Integer, ItemStack> entry : toolSlots.entrySet()) {
                        this.craftMatrix.setInventorySlotContents(entry.getKey(), entry.getValue());
                    }
                    return ItemStack.EMPTY;
                }

                // Restaurer les outils avec un point de durabilité en moins
                for (Map.Entry<Integer, ItemStack> entry : toolSlots.entrySet()) {
                    ItemStack toolStack = entry.getValue();
                    boolean broke = false;

                    if (toolStack.getItem().isDamageable() && player instanceof EntityPlayerMP) {
                        broke = toolStack.attemptDamageItem(1, player.getRNG(), (EntityPlayerMP) player);
                    }

                    if (!broke) {
                        this.craftMatrix.setInventorySlotContents(entry.getKey(), toolStack);
                    }
                }

                slot.onSlotChange(itemstack1, itemstack);
            }
            // Si le shift-clic vient des slots de la grille
            else if (index >= 1 && index < 39) {
                if (!this.mergeItemStack(itemstack1, 39, 75, false)) {
                    return ItemStack.EMPTY;
                }
            }
            // Si le shift-clic vient de l'inventaire
            else if (index >= 39 && index < 75) {
                if (!this.mergeItemStack(itemstack1, 1, 39, false)) {
                    return ItemStack.EMPTY;
                }
            }

            if (itemstack1.isEmpty()) {
                slot.putStack(ItemStack.EMPTY);
            } else {
                slot.onSlotChanged();
            }

            if (itemstack1.getCount() == itemstack.getCount()) {
                return ItemStack.EMPTY;
            }

            slot.onTake(playerIn, itemstack1);
        }

        return itemstack;
    }

    /**
     * Gère les aspects du métier (XP, limite quotidienne) lors d'un craft réussi
     */
    private void handleCraftCompletion(IForgeRecipe recipe) {
        if (!(player instanceof EntityPlayerMP)) return;
        EntityPlayerMP playerMP = (EntityPlayerMP) player;

        IMinePieceData mpData = player.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);
        if (mpData == null) return;

        // Calculer l'XP avec bonus pour la Forge
        int baseXp = recipe.getBaseXpReward();
        int xpBonus = recipe.getXpBonus(STATION_TYPE);
        int totalXp = baseXp + xpBonus;

        // Ajouter l'XP
        PlayerUtils.addJobXpToPlayer(playerMP, totalXp);

        // Réduire la limite quotidienne
        int newLimit = mpData.getDailyCraftLimit() - 1;
        mpData.setDailyCraftLimit(Math.max(0, newLimit));
        mpData.clientSync(playerMP);

        // Envoyer un message au joueur
        StringBuilder message = new StringBuilder();
        message.append(TextFormatting.GREEN).append("Craft réussi ! ");
        message.append(TextFormatting.GOLD).append("+").append(totalXp).append(" XP");

        if (xpBonus > 0) {
            message.append(TextFormatting.GREEN).append(" (")
                    .append(TextFormatting.GOLD).append("+")
                    .append(xpBonus).append(" ")
                    .append(TextFormatting.GREEN).append("bonus)");
        }

        player.sendMessage(new TextComponentString(message.toString()));
        player.sendMessage(new TextComponentString(
                TextFormatting.GREEN + "Craft journalier restant : " +
                        TextFormatting.GOLD + mpData.getDailyCraftLimit()
        ));
    }

    /**
     * Vérifie si un item est un outil spécial
     */
    private boolean isSpecialTool(Item item) {
        return item == ModTools.MARTEAU ||
                item == ModTools.PINCE ||
                item == ModTools.SCISSORS ||
                item instanceof BlacksmithToolItem ||
                item == Items.WATER_BUCKET; // Eau préservée pour le refroidissement
    }

    @Override
    public void onContainerClosed(EntityPlayer playerIn) {
        super.onContainerClosed(playerIn);

        if (!this.world.isRemote) {
            for (int i = 0; i < this.craftMatrix.getSizeInventory(); i++) {
                ItemStack stack = this.craftMatrix.getStackInSlot(i);
                if (!stack.isEmpty()) {
                    playerIn.inventory.placeItemBackInInventory(this.world, stack);
                }
            }
        }
    }

    @Override
    public boolean canInteractWith(EntityPlayer playerIn) {
        IMinePieceData mpData = playerIn.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);

        return mpData != null &&
                mpData.getJob() == MinePieceJobs.BLACKSMITH &&
                mpData.getDailyCraftLimit() > 0 &&
                playerIn.getDistanceSq(this.pos.getX() + 0.5D, this.pos.getY() + 0.5D, this.pos.getZ() + 0.5D) <= 64.0D;
    }
}