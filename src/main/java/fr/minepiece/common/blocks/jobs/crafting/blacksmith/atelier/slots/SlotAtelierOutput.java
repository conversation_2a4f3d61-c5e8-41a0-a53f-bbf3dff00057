package fr.minepiece.common.blocks.jobs.crafting.blacksmith.atelier.slots;

import fr.minepiece.common.init.ModTools;
import fr.minepiece.common.items.jobs.blacksmith.BlacksmithToolItem;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.inventory.IInventory;
import net.minecraft.inventory.InventoryCrafting;
import net.minecraft.inventory.SlotCrafting;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;

import java.util.ArrayList;
import java.util.List;

public class SlotAtelierOutput extends SlotCrafting {
    private final EntityPlayer player;
    private final InventoryCrafting craftMatrix;

    // Pour stocker une copie des outils avant le craft
    private List<ItemStack> storedTools = new ArrayList<>();
    private List<Integer> storedToolSlots = new ArrayList<>();

    public SlotAtelierOutput(EntityPlayer player, InventoryCrafting craftMatrix, IInventory inventoryIn, int slotIndex, int xPosition, int yPosition) {
        super(player, craftMatrix, inventoryIn, slotIndex, xPosition, yPosition);
        this.player = player;
        this.craftMatrix = craftMatrix;
    }

    @Override
    public boolean isItemValid(ItemStack stack) {
        return false;
    }

    /**
     * Cette méthode est appelée avant que le joueur prenne un item du slot
     */
    @Override
    public ItemStack onTake(EntityPlayer thePlayer, ItemStack stack) {
        // Sauvegarder les outils avant qu'ils ne soient consommés
        findAndStoreTools();

        // Laisser la méthode parent faire son travail (consommer les ingrédients normaux)
        ItemStack result = super.onTake(thePlayer, stack);

        // Restaurer les outils après consommation, en les endommageant
        restoreToolsWithDamage();

        return result;
    }

    /**
     * Trouve et stocke tous les outils dans la matrice
     */
    private void findAndStoreTools() {
        storedTools.clear();
        storedToolSlots.clear();

        for (int i = 0; i < craftMatrix.getSizeInventory(); i++) {
            ItemStack stack = craftMatrix.getStackInSlot(i);
            if (!stack.isEmpty() && isSpecialTool(stack.getItem())) {
                storedTools.add(stack.copy());
                storedToolSlots.add(i);

                // On met un ItemStack vide dans le slot pour éviter la duplication
                // Le craft ne consommera pas l'outil qui n'est plus là
                craftMatrix.setInventorySlotContents(i, ItemStack.EMPTY);
            }
        }
    }

    /**
     * Restaure les outils stockés avec de la durabilité en moins
     */
    private void restoreToolsWithDamage() {
        for (int i = 0; i < storedTools.size(); i++) {
            ItemStack toolStack = storedTools.get(i).copy();
            int slotIndex = storedToolSlots.get(i);

            // Vérifier que l'outil est bien absent (consommé par la recette)
            ItemStack currentStack = craftMatrix.getStackInSlot(slotIndex);
            if (currentStack.isEmpty()) {
                // Endommager l'outil
                if (toolStack.getItem().isDamageable()) {
                    boolean broke = toolStack.attemptDamageItem(1, player.getRNG(),
                            player instanceof net.minecraft.entity.player.EntityPlayerMP ?
                                    (net.minecraft.entity.player.EntityPlayerMP) player : null);

                    if (!broke) {
                        // Remettre l'outil endommagé dans la matrice
                        craftMatrix.setInventorySlotContents(slotIndex, toolStack);
                    }
                } else {
                    // Remettre l'outil tel quel s'il n'est pas endommageable
                    craftMatrix.setInventorySlotContents(slotIndex, toolStack);
                }
            }
        }
    }

    /**
     * Vérifie si l'item est un outil spécial à préserver
     */
    private boolean isSpecialTool(Item item) {
        return item == ModTools.MARTEAU ||
                item == ModTools.PINCE ||
                item == ModTools.SCISSORS ||
                item instanceof BlacksmithToolItem;
    }
}