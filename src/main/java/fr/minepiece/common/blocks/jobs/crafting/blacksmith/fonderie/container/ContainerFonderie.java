package fr.minepiece.common.blocks.jobs.crafting.blacksmith.fonderie.container;

import fr.minepiece.common.blocks.jobs.crafting.blacksmith.fonderie.recipes.FonderieRecipes;
import fr.minepiece.common.blocks.jobs.crafting.blacksmith.fonderie.slots.SlotFonderieFuel;
import fr.minepiece.common.blocks.jobs.crafting.blacksmith.fonderie.slots.SlotFonderieMoule;
import fr.minepiece.common.blocks.jobs.crafting.blacksmith.fonderie.slots.SlotFonderieOutput;
import fr.minepiece.common.blocks.jobs.crafting.blacksmith.fonderie.tileentity.TileEntityFonderie;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.player.InventoryPlayer;
import net.minecraft.inventory.Container;
import net.minecraft.inventory.IContainerListener;
import net.minecraft.inventory.Slot;
import net.minecraft.item.ItemStack;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

public class ContainerFonderie extends Container {

    private final TileEntityFonderie tileentity;
    private int burnTime, currentBurnTime, cookTime, totalCookTime;

    public ContainerFonderie(InventoryPlayer player, TileEntityFonderie tileentity) {
        this.tileentity = tileentity;

        // Matériau à fondre
        this.addSlotToContainer(new Slot(tileentity, 0, 80, 84));

        // Charbon (combustible)
        this.addSlotToContainer(new SlotFonderieFuel(tileentity, 1, 80, 117));

        // Moule à lingot
        this.addSlotToContainer(new SlotFonderieMoule(tileentity, 2, 136, 85));

        // Slot de sortie du résultat
        this.addSlotToContainer(new SlotFonderieOutput(player.player, tileentity, 3, 136, 58));

        // Inventaire du joueur
        for(int y = 0; y < 3; ++y) {
            for(int x = 0; x < 9; ++x) {
                this.addSlotToContainer(new Slot(player, x + y * 9 + 9, 8 + x * 18, 143 + y * 18));
            }
        }

        // Barre du joueur
        for(int x = 0; x < 9; ++x) {
            this.addSlotToContainer(new Slot(player, x, 8 + x * 18, 202));
        }
    }

    @Override
    public void addListener(IContainerListener listener) {
        super.addListener(listener);
        listener.sendAllWindowProperties(this, this.tileentity);
    }

    @Override
    public void detectAndSendChanges() {
        super.detectAndSendChanges();

        for(int i = 0; i < this.listeners.size(); ++i) {
            IContainerListener listener = this.listeners.get(i);
            if(this.cookTime != this.tileentity.getField(2)) listener.sendWindowProperty(this, 2, this.tileentity.getField(2));
            if(this.burnTime != this.tileentity.getField(0)) listener.sendWindowProperty(this, 0, this.tileentity.getField(0));
            if(this.currentBurnTime != this.tileentity.getField(1)) listener.sendWindowProperty(this, 1, this.tileentity.getField(1));
            if(this.totalCookTime != this.tileentity.getField(3)) listener.sendWindowProperty(this, 3, this.tileentity.getField(3));
        }
    }

    @Override
    @SideOnly(Side.CLIENT)
    public void updateProgressBar(int id, int data) {
        this.tileentity.setField(id, data);
    }

    @Override
    public boolean canInteractWith(EntityPlayer playerIn) {
        return this.tileentity.isUsableByPlayer(playerIn);
    }

    @Override
    public ItemStack transferStackInSlot(EntityPlayer playerIn, int index) {
        ItemStack itemstack = ItemStack.EMPTY;
        Slot slot = this.inventorySlots.get(index);

        if (slot != null && slot.getHasStack()) {
            ItemStack itemstack1 = slot.getStack();
            itemstack = itemstack1.copy();

            // Si on prend depuis le slot de résultat (index 3)
            if (index == 3) {
                // Essaie de mettre dans l'inventaire du joueur
                if (!this.mergeItemStack(itemstack1, 4, 40, true)) {
                    return ItemStack.EMPTY;
                }
                slot.onSlotChange(itemstack1, itemstack);
            }
            // Si on prend depuis l'inventaire du joueur
            else if (index >= 4) {
                // Si c'est un matériau qui peut être fondu
                if (!FonderieRecipes.getInstance().getFonderieResult(itemstack1).isEmpty()) {
                    // Essaie de mettre dans le slot d'entrée (0)
                    if (!this.mergeItemStack(itemstack1, 0, 1, false)) {
                        return ItemStack.EMPTY;
                    }
                }
                // Si c'est un combustible
                else if (TileEntityFonderie.getItemBurnTime(itemstack1) > 0) {
                    // Essaie de mettre dans le slot de charbon (1)
                    if (!this.mergeItemStack(itemstack1, 1, 2, false)) {
                        return ItemStack.EMPTY;
                    }
                }
                // Si c'est un moule
                else if (TileEntityFonderie.isMoule(itemstack1)) {
                    // Essaie de mettre dans le slot de moule (2)
                    if (!this.mergeItemStack(itemstack1, 2, 3, false)) {
                        return ItemStack.EMPTY;
                    }
                }
                // Si on est dans la barre d'accès rapide
                else if (index >= 31 && index < 40) {
                    // Essaie de mettre dans l'inventaire principal
                    if (!this.mergeItemStack(itemstack1, 4, 31, false)) {
                        return ItemStack.EMPTY;
                    }
                }
                // Si on est dans l'inventaire principal
                else if (index >= 4 && index < 31) {
                    // Essaie de mettre dans la barre d'accès rapide
                    if (!this.mergeItemStack(itemstack1, 31, 40, false)) {
                        return ItemStack.EMPTY;
                    }
                }
            }
            // Si on prend depuis un slot de la fonderie (0-2)
            else {
                // Essaie de mettre dans l'inventaire du joueur
                if (!this.mergeItemStack(itemstack1, 4, 40, false)) {
                    return ItemStack.EMPTY;
                }
            }

            if (itemstack1.isEmpty()) {
                slot.putStack(ItemStack.EMPTY);
            } else {
                slot.onSlotChanged();
            }

            if (itemstack1.getCount() == itemstack.getCount()) {
                return ItemStack.EMPTY;
            }

            slot.onTake(playerIn, itemstack1);
        }
        return itemstack;
    }
}