package fr.minepiece.common.blocks;

import fr.minepiece.common.init.ModTools;
import net.minecraft.block.Block;
import net.minecraft.block.BlockPane;
import net.minecraft.block.SoundType;
import net.minecraft.block.material.Material;
import net.minecraft.block.properties.IProperty;
import net.minecraft.block.properties.PropertyBool;
import net.minecraft.block.state.BlockStateContainer;
import net.minecraft.block.state.IBlockState;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.init.SoundEvents;
import net.minecraft.item.ItemStack;
import net.minecraft.util.BlockRenderLayer;
import net.minecraft.util.EnumFacing;
import net.minecraft.util.EnumHand;
import net.minecraft.util.EnumParticleTypes;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.math.AxisAlignedBB;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.IBlockAccess;
import net.minecraft.world.World;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;
import fr.minepiece.MinePiece;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import javax.annotation.Nullable;

public class BlockBarreauFer extends BlockPane {

    // Map pour stocker les joueurs qui sont en train de démanteler un barreau et le temps restant
    private static Map<UUID, Map<BlockPos, Integer>> playersDisassembling = new HashMap<>();

    // Propriété pour indiquer si le barreau est en cours de démantèlement
    public static final PropertyBool DISASSEMBLING = PropertyBool.create("disassembling");

    // Temps total de démantèlement en ticks (10 secondes = 200 ticks)
    private static final int DISASSEMBLY_TIME = 200;

    public BlockBarreauFer() {
        super(Material.IRON, true);
        setUnlocalizedName("barreau_fer_block");
        setRegistryName("barreau_fer_block");
        setHardness(-1.0F);
        setResistance(6000.0F);
        setSoundType(SoundType.METAL);
        setCreativeTab(MinePiece.BLOCKS);
        setDefaultState(this.blockState.getBaseState()
                .withProperty(NORTH, Boolean.FALSE)
                .withProperty(EAST, Boolean.FALSE)
                .withProperty(SOUTH, Boolean.FALSE)
                .withProperty(WEST, Boolean.FALSE)
                .withProperty(DISASSEMBLING, Boolean.FALSE));
    }

    @Override
    protected BlockStateContainer createBlockState() {
        return new BlockStateContainer(this, new IProperty[] {NORTH, EAST, WEST, SOUTH, DISASSEMBLING});
    }

    @Override
    public IBlockState getStateFromMeta(int meta) {
        return this.getDefaultState().withProperty(DISASSEMBLING, (meta & 1) > 0);
    }

    @Override
    public int getMetaFromState(IBlockState state) {
        return state.getValue(DISASSEMBLING) ? 1 : 0;
    }

    @Override
    public boolean isToolEffective(String type, IBlockState state) {
        return false;
    }

    @Override
    public void onBlockClicked(World worldIn, BlockPos pos, EntityPlayer playerIn) {
        super.onBlockClicked(worldIn, pos, playerIn);
    }

    @Override
    @SideOnly(Side.CLIENT)
    public BlockRenderLayer getBlockLayer() {
        return BlockRenderLayer.CUTOUT_MIPPED;
    }

    @Override
    public void randomTick(World worldIn, BlockPos pos, IBlockState state, Random random) {
    }

    @Override
    public void updateTick(World worldIn, BlockPos pos, IBlockState state, Random rand) {
        if (state.getValue(DISASSEMBLING)) {
            boolean stillDisassembling = false;

            for (UUID playerId : playersDisassembling.keySet()) {
                Map<BlockPos, Integer> playerBlocks = playersDisassembling.get(playerId);
                if (playerBlocks.containsKey(pos)) {
                    stillDisassembling = true;
                    break;
                }
            }

            if (!stillDisassembling) {
                worldIn.setBlockState(pos, state.withProperty(DISASSEMBLING, false));
            } else {
                if (rand.nextInt(5) == 0) {
                    worldIn.playSound(null, pos, SoundEvents.BLOCK_METAL_STEP, SoundCategory.BLOCKS,
                            0.3F, worldIn.rand.nextFloat() * 0.2F + 0.9F);
                }

                for (int i = 0; i < 2; i++) {
                    float px = pos.getX() + 0.5F + (rand.nextFloat() - 0.5F) * 0.5F;
                    float py = pos.getY() + 0.5F + (rand.nextFloat() - 0.5F) * 0.5F;
                    float pz = pos.getZ() + 0.5F + (rand.nextFloat() - 0.5F) * 0.5F;
                    worldIn.spawnParticle(EnumParticleTypes.SMOKE_NORMAL, px, py, pz, 0, 0, 0);
                }

                worldIn.scheduleUpdate(pos, this, 5 + rand.nextInt(5));
            }
        }
    }

    public static void onPlayerTick(EntityPlayer player) {
        UUID playerId = player.getUniqueID();

        if (playersDisassembling.containsKey(playerId)) {
            Map<BlockPos, Integer> playerBlocks = playersDisassembling.get(playerId);
            Map<BlockPos, Integer> updatedBlocks = new HashMap<>();
            World world = player.world;

            for (Map.Entry<BlockPos, Integer> entry : playerBlocks.entrySet()) {
                BlockPos pos = entry.getKey();
                int timeLeft = entry.getValue() - 1;

                if (player.getDistanceSq(pos) > 10.0) {
                    if (world.getBlockState(pos).getBlock() instanceof BlockBarreauFer) {
                        IBlockState state = world.getBlockState(pos);
                        world.setBlockState(pos, state.withProperty(DISASSEMBLING, false));
                    }
                    continue;
                }

                if (timeLeft <= 0) {
                    if (world.getBlockState(pos).getBlock() instanceof BlockBarreauFer) {
                        world.playSound(null, pos, SoundEvents.ITEM_SHIELD_BREAK, SoundCategory.BLOCKS,
                                1.0F, world.rand.nextFloat() * 0.2F + 0.8F);

                        world.setBlockToAir(pos);

                        ItemStack mainHandItem = player.getHeldItemMainhand();
                        ItemStack offHandItem = player.getHeldItemOffhand();

                        if (!player.capabilities.isCreativeMode) {
                            if (mainHandItem.getItem() == ModTools.PINCE) {
                                mainHandItem.damageItem(1, player);
                            } else if (offHandItem.getItem() == ModTools.PINCE) {
                                offHandItem.damageItem(1, player);
                            }
                        }
                    }
                    continue;
                }
                updatedBlocks.put(pos, timeLeft);

                if (timeLeft % 20 == 0 && world.getBlockState(pos).getBlock() instanceof BlockBarreauFer) {
                    world.playSound(null, pos, SoundEvents.BLOCK_METAL_HIT, SoundCategory.BLOCKS,
                            0.5F, world.rand.nextFloat() * 0.2F + 0.9F);
                }
            }

            if (updatedBlocks.isEmpty()) {
                playersDisassembling.remove(playerId);
            } else {
                playersDisassembling.put(playerId, updatedBlocks);
            }
        }
    }

    @Override
    public boolean onBlockActivated(World worldIn, BlockPos pos, IBlockState state,
                                    EntityPlayer playerIn, EnumHand hand,
                                    EnumFacing facing, float hitX, float hitY, float hitZ) {
        ItemStack heldItem = playerIn.getHeldItem(hand);
        UUID playerId = playerIn.getUniqueID();

        if (heldItem.getItem() == ModTools.PINCE) {
            if (!worldIn.isRemote) {
                boolean alreadyDisassembling = false;

                if (playersDisassembling.containsKey(playerId)) {
                    Map<BlockPos, Integer> playerBlocks = playersDisassembling.get(playerId);
                    alreadyDisassembling = playerBlocks.containsKey(pos);
                }

                if (!alreadyDisassembling) {
                    worldIn.playSound(null, pos, SoundEvents.BLOCK_METAL_PLACE, SoundCategory.BLOCKS,
                            0.8F, worldIn.rand.nextFloat() * 0.2F + 0.9F);

                    worldIn.setBlockState(pos, state.withProperty(DISASSEMBLING, true));

                    Map<BlockPos, Integer> playerBlocks = playersDisassembling.getOrDefault(playerId, new HashMap<>());
                    playerBlocks.put(pos, DISASSEMBLY_TIME);
                    playersDisassembling.put(playerId, playerBlocks);

                    worldIn.scheduleUpdate(pos, this, 5);
                }
            }

            return true;
        }

        return false;
    }
}