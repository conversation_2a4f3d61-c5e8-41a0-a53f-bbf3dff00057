package fr.minepiece.common.api.fruits;

import fr.minepiece.common.api.fruits.logia.MaguMaguFruit;
import fr.minepiece.common.api.fruits.paramecia.BaneBaneFruit;
import fr.minepiece.common.api.fruits.paramecia.BukuBukuFruit;
import fr.minepiece.common.api.fruits.paramecia.MiraMiraFruit;
import fr.minepiece.common.api.fruits.paramecia.WapuWapuFruit;

/**
 * Classe contenant les instances de tous les fruits du démon disponibles.
 */
public class ModFruits {

    // Paramecia fruits
    public static final IMinePieceFruit BUKU_BUKU_FRUIT = new BukuBukuFruit();
    public static final IMinePieceFruit MIRA_MIRA_FRUIT = new MiraMiraFruit();
    public static final IMinePieceFruit BANE_BANE_FRUIT = new BaneBaneFruit();
    public static final IMinePieceFruit WAPU_WAPU_FRUIT = new WapuWapuFruit();

    // Zoan fruits

    // Logia fruits
    public static final IMinePieceFruit MAGU_MAGU_FRUIT = new MaguMaguFruit();
}