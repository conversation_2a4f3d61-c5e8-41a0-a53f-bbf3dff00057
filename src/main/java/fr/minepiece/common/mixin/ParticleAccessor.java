package fr.minepiece.common.mixin;

import net.minecraft.client.particle.Particle;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.gen.Accessor;

@Mixin(Particle.class)
public interface ParticleAccessor {

    @Accessor("posX")
    double getPosX();

    @Accessor("posY")
    double getPosY();

    @Accessor("posZ")
    double getPosZ();

    @Accessor("motionX")
    double getMotionX();

    @Accessor("motionY")
    double getMotionY();

    @Accessor("motionZ")
    double getMotionZ();

    @Accessor("particleScale")
    float getParticleScale();

    @Accessor("posX")
    void setPosX(double posX);

    @Accessor("posY")
    void setPosY(double posY);

    @Accessor("posZ")
    void setPosZ(double posZ);

    @Accessor("motionX")
    void setMotionX(double motionX);

    @Accessor("motionY")
    void setMotionY(double motionY);

    @Accessor("motionZ")
    void setMotionZ(double motionZ);
}
