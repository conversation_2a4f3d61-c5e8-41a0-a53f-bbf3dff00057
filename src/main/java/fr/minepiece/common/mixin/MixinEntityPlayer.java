package fr.minepiece.common.mixin;

import fr.minepiece.common.api.utils.SeatUtils;
import fr.minepiece.common.mixinaccessors.IMixinEntityPlayerLaidDown;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.EnumFacing;
import net.minecraft.util.math.AxisAlignedBB;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.World;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Overwrite;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin(EntityPlayer.class)
public abstract class MixinEntityPlayer extends EntityLivingBase implements IMixinEntityPlayerLaidDown {

    public MixinEntityPlayer(World worldIn) {
        super(worldIn);
    }


    /**
     * That is used for furniture who require laying down.
     * */
    @Unique
    public boolean minePieceMod$isLaidDown;

    @Unique
    public EnumFacing minePieceMod$laidDownFacing;

    @Unique
    public double minePieceMod$yOffset;
    @Unique
    public double minePieceMod$xOffset;
    @Unique
    public BlockPos laidPosition;

    /**
     * <AUTHOR>
     * @reason Used for the Giant Race
     */
    @Overwrite
    protected void updateSize() {
        //PlayerData data = PlayerAPIClient.getINSTANCE().getPlayerData();
        //EnumAnimationSize value = EnumAnimationSize.getAnimation(data.getSize());
        float f; //width
        float f1; //height

        if (true) { // need to check if player is giant or not
            if (this.minePieceMod$isLaidDown) {
                if (this.isSneaking()) {
                    SeatUtils.standUpPlayer(this.world, (EntityPlayer) (Object) this);
                }
            }
            if (this.isElytraFlying()) {
                f = 0.6F;
                f1 = 0.6F;
            } else if (this.isPlayerSleeping() || this.minePieceMod$isLaidDown) {
                f = 0.2F;
                f1 = 0.2F;
            } else if (this.isSneaking()) {
                f = 0.6f/*value.getSneak_width()*/;
                f1 = 1.5f/*value.getSneak_height()*/;
            } else {
                f = 0.6f/*value.getSneak_width()*/;
                f1 = 1.8f/*value.getSneak_height()*/;
            }
        } else {
            f = 0.6F;
            f1 = 1.8F;
        }

        if (f != this.width || f1 != this.height) {
            AxisAlignedBB axisalignedbb = this.getEntityBoundingBox();
            axisalignedbb = new AxisAlignedBB(axisalignedbb.minX, axisalignedbb.minY, axisalignedbb.minZ, axisalignedbb.minX + (double) f, axisalignedbb.minY + (double) f1, axisalignedbb.minZ + (double) f);

            if (!this.world.collidesWithAnyBlock(axisalignedbb)) {
                this.setSize(f, f1);
            }
        }
        //this.eyeHeight = 2.5f;
        net.minecraftforge.fml.common.FMLCommonHandler.instance().onPlayerPostTick((EntityPlayer) (Object) this);
    }

    /**
     * That is used for furniture who require laying down.
     * */
    @Override
    public boolean minePieceMod$isLaidDown() {
        return minePieceMod$isLaidDown;
    }

    /**
     * That is used for furniture who require laying down.
     * */
    @Override
    public void minePieceMod$setLaidDown(boolean laidDown) {
        this.minePieceMod$isLaidDown = laidDown;
    }

    @Override
    public EnumFacing minePieceMod$getLaidDownFacing() {
        return minePieceMod$laidDownFacing;
    }

    @Override
    public void minePieceMod$setLaidDownFacing(EnumFacing facing) {
        this.minePieceMod$laidDownFacing = facing;
    }

    @Override
    public double minePieceMod$getYOffset() {
        return this.minePieceMod$yOffset;
    }

    @Override
    public void minePieceMod$setYOffset(double yOffset) {
        this.minePieceMod$yOffset = yOffset;
    }

    @Override
    public double minePieceMod$getXOffset() {
        return this.minePieceMod$xOffset;
    }

    public void minePieceMod$setXOffset(double xOffset) {
        this.minePieceMod$xOffset = xOffset;
    }

    @Override
    public BlockPos minePieceMod$getLaidPosition() {
        return laidPosition;
    }

    public void minePieceMod$setLaidPosition(BlockPos laidPosition) {
        this.laidPosition = laidPosition;
    }

    /**
     * Used to change the return value of isMovementBlocked. That is used for furniture who require laying down.
     * */
    @Inject(method = "isMovementBlocked", at = @At("HEAD"), cancellable = true)
    public void onIsMovementBlocked(CallbackInfoReturnable<Boolean> cir) {
        cir.setReturnValue(this.getHealth() <= 0.0F || this.isPlayerSleeping() || this.minePieceMod$isLaidDown);
    }

}
