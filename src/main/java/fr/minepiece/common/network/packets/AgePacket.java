/*
 * Copyright (c) 2021.  Zom'
 * https://twitter.com/ZOm__YT
 */

package fr.minepiece.common.network.packets;

import fr.minepiece.common.capability.mpdata.MinePieceDataStorage;
import io.netty.buffer.ByteBuf;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;
import net.minecraftforge.fml.relauncher.Side;

public class AgePacket  implements IMessage, IMessageHandler< AgePacket, IMessage > {

	public int age;

	public AgePacket() {
	}

	public AgePacket(int age) {
		this.age = age;
	}

	@Override
	public void fromBytes(ByteBuf buf) {
		age = buf.readInt();
	}

	@Override
	public void toBytes(ByteBuf buf) {
		buf.writeInt(age);
	}

	@Override
	public IMessage onMessage(AgePacket message, MessageContext ctx) {
		if (ctx.side.isServer()) {
			EntityPlayer player = ctx.getServerHandler().player;
			player.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null).setRPAge(Math.min(Math.max(message.age, 1), 100));
		}
		return null;
	}

}
