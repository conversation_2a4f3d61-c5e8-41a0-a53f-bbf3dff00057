package fr.minepiece.common.network.packets.races;

import fr.minepiece.client.interactions.ClientController;
import io.netty.buffer.ByteBuf;
import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;

/**
 * S2C packet that syncs the variable hasActivatedJumpAbility in TontattaEvents
 * */
public class TontattaJumpPacket implements IMessage, IMessageHandler< TontattaJumpPacket, IMessage > {

    /**
     * EntityId of player who used the ability
     * */
    private int entityId;

    public TontattaJumpPacket() {}

    public TontattaJumpPacket(int entityId) {
        this.entityId = entityId;
    }

    @Override
    public void fromBytes(ByteBuf buf) {
        entityId = buf.readInt();
    }

    @Override
    public void toBytes(ByteBuf buf) {
        buf.writeInt(entityId);
    }

    @Override
    public IMessage onMessage(TontattaJumpPacket message, MessageContext ctx) {
        if (ctx.side.isClient()) {
            ClientController.handleEvent(ClientController.Events.TONTATTA_JUMP_BOOST, message);
        }
        return null;
    }

    public int getEntityId() {
        return entityId;
    }
}
