package fr.minepiece.common.network.packets.races;

import fr.minepiece.client.interactions.ClientController;
import io.netty.buffer.ByteBuf;
import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;

/**
 * S2C Packet that opens the overheat GUI
 * */
public class OpenOverheatGuiPacket implements IMessage, IMessageHandler<OpenOverheatGuiPacket, IMessage> {

    public OpenOverheatGuiPacket() {}

    @Override
    public void fromBytes(ByteBuf buf) {
    }

    @Override
    public void toBytes(ByteBuf buf) {
    }

    @Override
    public IMessage onMessage(OpenOverheatGuiPacket message, MessageContext ctx) {
        if (ctx.side.isClient()) {
            // Open the overheat GUI
            ClientController.handleEvent(ClientController.Events.OPEN_OVERHEAT_GUI, message);
        }
        return null;
    }
}
