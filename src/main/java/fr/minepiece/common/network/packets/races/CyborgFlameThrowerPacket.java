package fr.minepiece.common.network.packets.races;

import fr.minepiece.client.interactions.ClientController;
import io.netty.buffer.ByteBuf;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;
import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;

import java.util.*;
/*
/**
 * C2C packet that tells players to render particles for cyborg ability
 * Client: Sends this packet to ALL other clients
 * Other clients: Adds information to CyborgEvents for rendering purposes
 * */
/**
 * S2C packet that tells players to render particles for cyborg ability
 * */
public class CyborgFlameThrowerPacket implements IMessage, IMessageHandler<CyborgFlameThrowerPacket, IMessage > {

    private ArrayList<Vec3d> coordinates;

    public CyborgFlameThrowerPacket(ArrayList<Vec3d> coordinates) {
        this.coordinates = coordinates;
    }

    public CyborgFlameThrowerPacket() {}
    @Override
    public void fromBytes(ByteBuf buf) {
        int size = buf.readInt();
        coordinates = new ArrayList<>(size);

        for (int i = 0; i < size; i++) {
            double x = buf.readDouble();
            double y = buf.readDouble();
            double z = buf.readDouble();

            coordinates.add(new Vec3d(x, y, z));
        }
    }

    @Override
    public void toBytes(ByteBuf buf) {
        buf.writeInt(coordinates.size());

        for (Vec3d vec3d : coordinates) {
            buf.writeDouble(vec3d.x);
            buf.writeDouble(vec3d.y);
            buf.writeDouble(vec3d.z);
        }
    }

    @Override
    public IMessage onMessage(CyborgFlameThrowerPacket message, MessageContext ctx) {
        if (ctx.side.isClient()) {
            ClientController.handleEvent(ClientController.Events.CYBORG_FLAMETHROWER, message);
        }

        return null;

    }

    private World getServerWorld(MessageContext ctx) {
        return ctx.getServerHandler().player.getServerWorld();
    }
    public ArrayList<Vec3d> getCoordinates() {
        return coordinates;
    }

}
