package fr.minepiece.common.network.packets;

import fr.minepiece.client.events.InputEvent;
import io.netty.buffer.ByteBuf;
import net.minecraftforge.fml.common.network.ByteBufUtils;
import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;

import java.util.UUID;

/**
 * S2C packet that will stop all user input for a player depending on the given boolean
 * */
public class CancelMovementPacket implements IMessage, IMessageHandler < CancelMovementPacket, IMessage > {

    private UUID playerUUID;
    private boolean shouldCancelMovement;

    public CancelMovementPacket() {}

    public CancelMovementPacket(UUID playerUUID, boolean shouldCancelMovement) {
        this.playerUUID = playerUUID;
        this.shouldCancelMovement = shouldCancelMovement;
    }

    @Override
    public void fromBytes(ByteBuf buf) {
        playerUUID = UUID.fromString(ByteBufUtils.readUTF8String(buf));
        shouldCancelMovement = buf.readBoolean();
    }

    @Override
    public void toBytes(ByteBuf buf) {
        ByteBufUtils.writeUTF8String(buf, playerUUID.toString());
        buf.writeBoolean(shouldCancelMovement);
    }

    @Override
    public IMessage onMessage(CancelMovementPacket message, MessageContext ctx) {
        if (ctx.side.isClient()) {
            // Add the player to the list of players that should have their movement cancelled
            if (message.shouldCancelMovement)
                InputEvent.shouldCancelMovement.add(message.playerUUID);
            else
                InputEvent.shouldCancelMovement.remove(message.playerUUID);
        }
        return null;
    }

    public UUID getPlayerUUID() {
        return playerUUID;
    }

    public boolean isShouldCancelMovement() {
        return shouldCancelMovement;
    }
}
