package fr.minepiece.common.network.packets;

import fr.minepiece.common.capability.knockout.IKOState;
import fr.minepiece.common.capability.knockout.KOStateStorage;
import io.netty.buffer.ByteBuf;
import net.minecraft.client.Minecraft;
import net.minecraft.client.multiplayer.WorldClient;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraftforge.fml.common.network.ByteBufUtils;
import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

import java.util.UUID;

/**
 * Packet amélioré pour synchroniser l'état KO entre serveur et client
 */
public class PlayerKOSyncPacket implements IMessage, IMessageHandler<PlayerKOSyncPacket, IMessage> {
    private UUID playerUUID;
    private boolean isKO;

    public PlayerKOSyncPacket() {}

    /**
     * Constructeur pour créer un nouveau packet de synchronisation KO
     * @param playerUUID UUID du joueur
     * @param isKO État KO (true = KO, false = normal)
     */
    public PlayerKOSyncPacket(UUID playerUUID, boolean isKO) {
        this.playerUUID = playerUUID;
        this.isKO = isKO;
    }

    @Override
    public void fromBytes(ByteBuf buf) {
        this.playerUUID = UUID.fromString(ByteBufUtils.readUTF8String(buf));
        this.isKO = buf.readBoolean();
    }

    @Override
    public void toBytes(ByteBuf buf) {
        ByteBufUtils.writeUTF8String(buf, this.playerUUID.toString());
        buf.writeBoolean(this.isKO);
    }

    @Override
    @SideOnly(Side.CLIENT)
    public IMessage onMessage(PlayerKOSyncPacket message, MessageContext ctx) {
        if (ctx.side.isClient()) {
            Minecraft.getMinecraft().addScheduledTask(() -> {
                WorldClient world = Minecraft.getMinecraft().world;
                if (world == null) return;

                world.playerEntities.stream()
                        .filter(p -> p.getUniqueID().equals(message.playerUUID))
                        .findFirst()
                        .ifPresent(player -> {
                            IKOState data = player.getCapability(KOStateStorage.KO_STATE_CAPABILITY, null);
                            if (data != null) {
                                data.setKOState(message.isKO);
                            }
                        });
                // Mise à jour de l'état KO dans le ClientKOHandler
                //ClientKOHandler.updateKOState(message.playerUUID, message.isKO);
            });
        }

        return null;
    }

    /**
     * Méthode de fabrique pour créer un packet à partir d'un joueur
     */
    public static PlayerKOSyncPacket fromPlayer(EntityPlayerMP player, boolean isKO) {
        return new PlayerKOSyncPacket(
                player.getUniqueID(),
                isKO
        );
    }
}