package fr.minepiece.common.network.packets;

import fr.minepiece.common.network.ModPackets;
import io.netty.buffer.ByteBuf;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.util.IThreadListener;
import net.minecraft.world.WorldServer;
import net.minecraftforge.fml.common.network.ByteBufUtils;
import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;
import net.minecraftforge.fml.common.registry.ForgeRegistries;

import java.util.UUID;

public class GiveItemPacket implements IMessage, IMessageHandler<GiveItemPacket, IMessage> {

    private UUID playerUUID;
    private String itemRegistryName;
    private int count;
    private int meta;

    public GiveItemPacket() {
        // Constructeur sans argument nécessaire pour le système de packets
    }

    public GiveItemPacket(UUID playerUUID, String itemRegistryName, int count, int meta) {
        this.playerUUID = playerUUID;
        this.itemRegistryName = itemRegistryName;
        this.count = count;
        this.meta = meta;
    }

    @Override
    public void fromBytes(ByteBuf buf) {
        this.playerUUID = new UUID(buf.readLong(), buf.readLong());
        this.itemRegistryName = ByteBufUtils.readUTF8String(buf);
        this.count = buf.readInt();
        this.meta = buf.readInt();
    }

    @Override
    public void toBytes(ByteBuf buf) {
        buf.writeLong(playerUUID.getMostSignificantBits());
        buf.writeLong(playerUUID.getLeastSignificantBits());
        ByteBufUtils.writeUTF8String(buf, itemRegistryName);
        buf.writeInt(count);
        buf.writeInt(meta);
    }

    @Override
    public IMessage onMessage(GiveItemPacket message, MessageContext ctx) {
        IThreadListener mainThread = (WorldServer) ctx.getServerHandler().player.world;
        mainThread.addScheduledTask(() -> {
            EntityPlayerMP player = (EntityPlayerMP) ctx.getServerHandler().player.getServerWorld()
                    .getPlayerEntityByUUID(message.playerUUID);

            if (player != null) {
                Item item = ForgeRegistries.ITEMS.getValue(new net.minecraft.util.ResourceLocation(message.itemRegistryName));
                if (item != null) {
                    ItemStack stack = new ItemStack(item, message.count, message.meta);
                    boolean success = player.inventory.addItemStackToInventory(stack);

                    if (!success) {
                        // Si l'inventaire est plein, on fait spawn l'item au sol
                        player.dropItem(stack, false);
                    }

                    // Synchroniser l'inventaire
                    player.inventoryContainer.detectAndSendChanges();
                }
            }
        });

        return null;
    }
}