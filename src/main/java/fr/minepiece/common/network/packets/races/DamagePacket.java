package fr.minepiece.common.network.packets.races;

import io.netty.buffer.ByteBuf;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.potion.Potion;
import net.minecraft.potion.PotionEffect;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.DamageSource;
import net.minecraftforge.fml.common.network.ByteBufUtils;
import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;

/**
 * C2S packet that is used to deal damage to a player client-side
 * */
public class DamagePacket implements IMessage, IMessageHandler <DamagePacket, IMessage > {

    private double damage;
    private int entityIdToDamage;
    private String usernameDamageDealer;
    private Types damageType;

    public DamagePacket() {}

    public DamagePacket(int entityIdToDamage, double damage, String usernameDamageDealer) {
        this.entityIdToDamage = entityIdToDamage;
        this.damage = damage;
        this.usernameDamageDealer = usernameDamageDealer;
    }

    public DamagePacket(int entityIdToDamage, Types damageType, String usernameDamageDealer) {
        this.entityIdToDamage = entityIdToDamage;
        this.usernameDamageDealer = usernameDamageDealer;
        this.damageType = damageType;
    }

    @Override
    public void fromBytes(ByteBuf buf) {
        entityIdToDamage = buf.readInt();
        damage = buf.readDouble();

        boolean hasDamageDealer = buf.readBoolean();
        if (hasDamageDealer) {
            usernameDamageDealer = ByteBufUtils.readUTF8String(buf);
        }

        boolean hasDamageType = buf.readBoolean();
        if (hasDamageType) {
            damageType = Types.values()[buf.readInt()];
        }
    }

    @Override
    public void toBytes(ByteBuf buf) {
        buf.writeInt(entityIdToDamage);
        buf.writeDouble(damage);

        if (usernameDamageDealer != null) {
            buf.writeBoolean(true);
            ByteBufUtils.writeUTF8String(buf, usernameDamageDealer);
        } else {
            buf.writeBoolean(false);
        }

        if (damageType != null) {
            buf.writeBoolean(true);
            buf.writeInt(damageType.ordinal());
        } else {
            buf.writeBoolean(false);
        }
    }

    @Override
    public IMessage onMessage(DamagePacket message, MessageContext ctx) {
        if (ctx.side.isServer()) {
            int entityId = message.getEntityIdToDamage();
            boolean isDamageType = message.getDamageType() != null;
            System.out.println("received " + isDamageType + " " + message.getDamageType() + ", player name: "+message.getUsernameDamageDealer());
            double damage =  isDamageType ? message.getDamageType().getDamage() : message.getDamage();
            String damageDealerUsername = message.getUsernameDamageDealer();

            Entity targetEntity = ctx.getServerHandler().player.world.getEntityByID(entityId);

            if (targetEntity instanceof EntityLivingBase) {
                EntityLivingBase targetEntityLiving = (EntityLivingBase) targetEntity;

                EntityPlayer dealingEntity = null;
                DamageSource damageSource;

                if (damageDealerUsername != null) {
                    // Get the EntityLivingBase instance for the damage dealer
                    dealingEntity = ctx.getServerHandler().player.world.getPlayerEntityByName(damageDealerUsername);
                }

                if (dealingEntity != null) {
                    // Create a custom damage source based on the damage dealer
                    damageSource = DamageSource.causePlayerDamage(dealingEntity);
                } else {
                    // Fallback to generic damage source
                    damageSource = DamageSource.GENERIC;
                }


                // Apply damage to the target entity using the specified damage source
                MinecraftServer server = ctx.getServerHandler().player.getServer();
                if (server != null) server.addScheduledTask(() -> targetEntityLiving.attackEntityFrom(damageSource, (float) damage));

                if (isDamageType && server != null) {
                    if (message.getDamageType() == Types.MINKS_SHOCKWAVE)
                        server.addScheduledTask(() -> targetEntityLiving.addPotionEffect(new PotionEffect(Potion.getPotionFromResourceLocation("slowness"), 80, 6)));
                }

            }
        }
        return null; // No response packet
    }

    public enum Types {
        CYBORG_LASER_BEAM(8),
        MINKS_SHOCKWAVE(6);

        final int damage;

        Types(int damage) {
            this.damage = damage;
        }

        public int getDamage() {
            return damage;
        }
    }

    public double getDamage() {
        return damage;
    }

    public int getEntityIdToDamage() {
        return entityIdToDamage;
    }

    public String getUsernameDamageDealer() {
        return usernameDamageDealer;
    }

    public Types getDamageType() {
        return damageType;
    }
}
