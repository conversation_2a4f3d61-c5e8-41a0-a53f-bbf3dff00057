/*
 * Copyright (c) 2021.  Zom'
 * https://twitter.com/ZOm__YT
 */

package fr.minepiece.common.network.packets;

import fr.minepiece.common.capability.mpdata.MinePieceDataStorage;
import io.netty.buffer.ByteBuf;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraftforge.fml.common.network.ByteBufUtils;
import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;
import net.minecraftforge.fml.relauncher.Side;

public class NamePacket implements IMessage, IMessageHandler< NamePacket, IMessage > {

	public String name, lastName;

	public NamePacket() {
	}

	public NamePacket(String name) {
		this.name = name;
	}

	@Override
	public void fromBytes(ByteBuf buf) {
		name = ByteBufUtils.readUTF8String(buf);
	}

	@Override
	public void toBytes(ByteBuf buf) {
		ByteBufUtils.writeUTF8String(buf, name);
	}

	@Override
	public IMessage onMessage(NamePacket message, MessageContext ctx) {
		if (ctx.side.isServer()) {
			EntityPlayer player = ctx.getServerHandler().player;
			player.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null).setRPName(message.name);
			player.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY,null).setRPLastName(message.lastName);
			player.refreshDisplayName();
		}
		return null;
	}

}
