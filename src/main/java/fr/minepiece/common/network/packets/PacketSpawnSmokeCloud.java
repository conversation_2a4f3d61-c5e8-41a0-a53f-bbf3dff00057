package fr.minepiece.common.network.packets;

import fr.minepiece.client.render.ShaderHelper;
import fr.minepiece.client.render.shaders.SmokeCloudClientData;
import fr.minepiece.client.render.sphere.SphereCallback;
import fr.minepiece.client.render.sphere.SphereManager;
import fr.minepiece.client.render.sphere.SphereRenderObject;
import io.netty.buffer.ByteBuf;
import net.minecraft.client.Minecraft;
import net.minecraft.util.math.Vec3d;
import net.minecraftforge.fml.common.network.ByteBufUtils;
import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;
import net.minecraftforge.fml.relauncher.Side;

import java.util.UUID;

public class PacketSpawnSmokeCloud implements IMessage, IMessageHandler<PacketSpawnSmokeCloud, IMessage> {
    UUID id; double x, y, z; float radius; int color; float density; int duration;

    public PacketSpawnSmokeCloud() {
    }

    public PacketSpawnSmokeCloud(UUID id, double x, double y, double z, float radius, int color, float density, int duration) {
        this.id = id;
        this.x = x;
        this.y = y;
        this.z = z;
        this.radius = radius;
        this.color = color;
        this.density = density;
        this.duration = duration;
    }

    @Override
    public void fromBytes(ByteBuf buf) {
        id = UUID.fromString(ByteBufUtils.readUTF8String(buf));
        x = buf.readDouble();
        y = buf.readDouble();
        z = buf.readDouble();
        radius = buf.readFloat();
        color = buf.readInt();
        density = buf.readFloat();
        duration = buf.readInt();
    }

    @Override
    public void toBytes(ByteBuf buf) {
        ByteBufUtils.writeUTF8String(buf, id.toString());
        buf.writeDouble(x);
        buf.writeDouble(y);
        buf.writeDouble(z);
        buf.writeFloat(radius);
        buf.writeInt(color);
        buf.writeFloat(density);
        buf.writeInt(duration);
    }

    @Override
    public IMessage onMessage(PacketSpawnSmokeCloud message, MessageContext ctx) {
        // Ensure execution on the main client thread
        Minecraft.getMinecraft().addScheduledTask(() -> {
            long expiry = Minecraft.getMinecraft().world.getTotalWorldTime() + message.duration;
            float r = ((message.color >> 16) & 0xFF) / 255.0F;
            float g = ((message.color >> 8) & 0xFF) / 255.0F;
            float b = (message.color & 0xFF) / 255.0F;

            SphereRenderObject object = new SphereRenderObject(5, new Vec3d(message.x, message.y, message.z), new float[]{r, g, b, 0.75f}, new SphereCallback() {
                @Override
                public void callback(SphereRenderObject object, Side side) {

                }
            }, message.duration, ShaderHelper.smokeFogShader);
            SphereManager.addFullSphere(object);

            SmokeCloudClientData.activeClouds.put(
                    String.valueOf(message.id), new SmokeCloudClientData(
                    new Vec3d(message.x, message.y, message.z),
                    message.radius,
                    r, g, b, message.density,
                    expiry)
            );

            // *** Optional: Spawn your PARTICLES here on the client ***
            // You can still use your old particle spawning code here
            // triggered by the packet arrival, just don't call it from
            // handleStatusUpdate anymore.
            // spawnDenseCoreSmokeLayer(...) etc.

        });
        return null; // No reply needed
    }
}
