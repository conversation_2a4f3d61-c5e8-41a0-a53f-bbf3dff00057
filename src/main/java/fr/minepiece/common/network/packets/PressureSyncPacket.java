package fr.minepiece.common.network.packets;

import fr.minepiece.common.items.BarometerItem;
import io.netty.buffer.ByteBuf;
import net.minecraft.client.Minecraft;
import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

public class PressureSyncPacket implements IMessage, IMessageHandler<PressureSyncPacket, IMessage> {
    private float pressure;
    private long displayEndTime;

    public PressureSyncPacket() {}

    public PressureSyncPacket(float pressure) {
        this.pressure = pressure;
        this.displayEndTime = System.currentTimeMillis() + 5000;
    }

    @Override
    public void fromBytes(ByteBuf buf) {
        pressure = buf.readFloat();
        displayEndTime = buf.readLong();
    }

    @Override
    public void toBytes(ByteBuf buf) {
        buf.writeFloat(pressure);
        buf.writeLong(displayEndTime);
    }

    @Override
    @SideOnly(Side.CLIENT)
    public IMessage onMessage(PressureSyncPacket message, MessageContext ctx) {
        Minecraft.getMinecraft().addScheduledTask(() -> {
            BarometerItem.displayPressure = message.pressure;
            BarometerItem.displayEndTime = message.displayEndTime;
        });
        return null;
    }
}