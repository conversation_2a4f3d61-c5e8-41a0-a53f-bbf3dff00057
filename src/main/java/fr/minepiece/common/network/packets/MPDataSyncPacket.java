package fr.minepiece.common.network.packets;

import fr.minepiece.common.capability.mpdata.IMinePieceData;
import fr.minepiece.common.capability.mpdata.MinePieceDataStorage;
import io.netty.buffer.ByteBuf;
import net.minecraftforge.fml.common.network.ByteBufUtils;
import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;

import javax.annotation.Nullable;

/**
 * C2S Packet to sync the player's data with the server
 * */
public class MPDataSyncPacket implements IMessage, IMessageHandler<MPDataSyncPacket, IMessage>{

    protected String name, lastname;
    protected int age, raceID, jobID;

    public MPDataSyncPacket() {}

    public MPDataSyncPacket(String name, String lastname, int age, int raceID, int jobID) {
        this.name = name;
        this.lastname = lastname;
        this.age = age;
        this.raceID = raceID;
        this.jobID = jobID;
    }

    @Override
    public void fromBytes(ByteBuf buf) {
        name = ByteBufUtils.readUTF8String(buf);
        lastname = ByteBufUtils.readUTF8String(buf);
        age = buf.readInt();
        raceID = buf.readInt();
        jobID = buf.readInt();
    }

    @Override
    public void toBytes(ByteBuf buf) {
        ByteBufUtils.writeUTF8String(buf, name);
        ByteBufUtils.writeUTF8String(buf, lastname);
        buf.writeInt(age);
        buf.writeInt(raceID);
        buf.writeInt(jobID);
    }

    @Override
    public IMessage onMessage(MPDataSyncPacket message, MessageContext ctx) {
        if (ctx.side.isServer()) {
            IMinePieceData data = ctx.getServerHandler().player.getCapability(MinePieceDataStorage.MP_DATA_CAPABILITY, null);
            assert data != null;
            data.setRPName(message.name);
            data.setRPLastName(message.lastname);
            data.setRPAge(message.age);
            data.setJob(message.jobID);
            data.setRace(message.raceID);
            data.clientSync(ctx.getServerHandler().player);
            ctx.getServerHandler().player.refreshDisplayName();
        }
        return null;
    }
}
