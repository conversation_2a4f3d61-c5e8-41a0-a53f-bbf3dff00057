package ca.xef5000.mmostats;

import ca.xef5000.mmostats.commands.StatsItemsCommand;
import ca.xef5000.mmostats.listeners.PlayerConnectionListener;
import ca.xef5000.mmostats.manager.ItemStatManager;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.java.JavaPlugin;

public final class StatsItems extends JavaPlugin {

    private StatsAPI statsAPI;
    private ItemStatManager itemStatManager;

    @Override
    public void onEnable() {
        // Plugin startup logic
        statsAPI = StatsCore.getStatsAPI();
        if (this.statsAPI == null) {
            getLogger().severe("StatAPI not found!");
            return;
        }

        if (getServer().getPluginManager().getPlugin("ItemsAdder") == null) {
            getLogger().warning("ItemsAdder not found. ItemsAdder item stats will not work.");
        }
        this.itemStatManager = new ItemStatManager(this, statsAPI);
        itemStatManager.startUpdater();

        getServer().getPluginManager().registerEvents(new PlayerConnectionListener(this, itemStatManager), this);

        getCommand("statsitems").setExecutor(new StatsItemsCommand(this));
        getCommand("statsitems").setTabCompleter(new StatsItemsCommand(this));
        getServer().getServicesManager().register(StatsItemsAPI.class, this.itemStatManager, this, org.bukkit.plugin.ServicePriority.Normal);

        getLogger().info("StatsItems enabled!");
    }

    @Override
    public void onDisable() {
        // Plugin shutdown logic
        if (itemStatManager != null) {
            itemStatManager.stopUpdater();
        }
        getLogger().info("StatsItems disabled!");
    }

    public ItemStatManager getItemStatManager() {
        return itemStatManager;
    }
}
