package ca.xef5000.mmostats.manager;

import ca.xef5000.mmostats.StatsAPI;
import ca.xef5000.mmostats.StatsEquipmentAPI;
import ca.xef5000.mmostats.StatsItemsAPI;
import ca.xef5000.mmostats.stats.StatType;
import dev.lone.itemsadder.api.CustomStack;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.PlayerInventory;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitTask;

import java.util.*;

public class ItemStatManager implements StatsItemsAPI {
    private final JavaPlugin plugin;
    private final StatsAPI statAPI; // Your existing stat API instance
    private final Map<String, ItemStatDefinition> itemDefinitions = new HashMap<>();
    private final Map<UUID, Map<StatType, Double>> playerAppliedItemStats = new HashMap<>(); // To track what stats items gave
    private BukkitTask updateTask;


    public enum ApplyCondition {
        EQUIPPED_ARMOR, HELD_MAIN_HAND, HELD_OFF_HAND, HELD_ANY_HAND, WORN_OR_HELD,
        EQUIPMENT // StatsEquipment Extension
    }

    private static class ItemStatDefinition {
        String key; // e.g., "swift_boots"
        String type; // "VANILLA" or "ITEMSADDER"
        Material material; // For VANILLA
        Integer customModelData; // For VANILLA
        String itemsAdderId; // For ITEMSADDER
        String nameContains; // Optional
        List<String> loreContains; // Optional
        ApplyCondition applyWhen;
        Map<StatType, Double> stats = new HashMap<>();

        boolean matches(ItemStack item, Player player, PlayerInventory inventory, ItemStack[] armorContents, ItemStack mainHand, ItemStack offHand) {
            if (item == null || item.getType() == Material.AIR) {
                return false;
            }

            // Check ItemsAdder first
            if ("ITEMSADDER".equalsIgnoreCase(type)) {
                CustomStack customStack = CustomStack.byItemStack(item);
                if (customStack == null || !customStack.getNamespacedID().equals(itemsAdderId)) {
                    return false;
                }
            } else if ("VANILLA".equalsIgnoreCase(type)) {
                if (item.getType() != material) {
                    return false;
                }
                if (customModelData != null) {
                    if (!item.hasItemMeta() || !item.getItemMeta().hasCustomModelData() || item.getItemMeta().getCustomModelData() != customModelData) {
                        return false;
                    }
                }
                if (nameContains != null) {
                    if (!item.hasItemMeta() || !item.getItemMeta().hasDisplayName() || !item.getItemMeta().getDisplayName().contains(nameContains)) {
                        return false;
                    }
                }
                if (loreContains != null && !loreContains.isEmpty()) {
                    if (!item.hasItemMeta() || !item.getItemMeta().hasLore()) {
                        return false;
                    }
                    List<String> itemLore = item.getItemMeta().getLore();
                    boolean allLoreMatch = true;
                    for (String requiredLore : loreContains) {
                        if (itemLore.stream().noneMatch(line -> line.contains(requiredLore))) {
                            allLoreMatch = false;
                            break;
                        }
                    }
                    if (!allLoreMatch) return false;
                }
            } else {
                return false; // Unknown type
            }

            // Check apply condition
            switch (applyWhen) {
                case EQUIPPED_ARMOR:
                    for (ItemStack armor : armorContents) {
                        if (item.equals(armor)) return true;
                    }
                    return false;
                case HELD_MAIN_HAND:
                    return item.equals(mainHand);
                case HELD_OFF_HAND:
                    return item.equals(offHand);
                case HELD_ANY_HAND:
                    return item.equals(mainHand) || item.equals(offHand);
                case WORN_OR_HELD:
                    if (item.equals(mainHand) || item.equals(offHand)) return true;
                    for (ItemStack armor : armorContents) {
                        if (item.equals(armor)) return true;
                    }
                    return false;
                case EQUIPMENT:
                    if (Bukkit.getPluginManager().getPlugin("StatsEquipment") != null &&
                            Bukkit.getPluginManager().getPlugin("StatsEquipment").isEnabled()) {
                        StatsEquipmentAPI statsEquipmentPlugin = getStatsEquipmentAPI();
                        return statsEquipmentPlugin.isItemInCustomEquipmentSlot(player, item);
                    }
                    return false;
                default:
                    return false;
            }
        }
    }


    public ItemStatManager(JavaPlugin plugin, StatsAPI statAPI) {
        this.plugin = plugin;
        this.statAPI = statAPI;
        loadItemStatsConfig();
    }

    public void loadItemStatsConfig() {
        itemDefinitions.clear();
        plugin.saveDefaultConfig(); // Ensure config.yml exists
        plugin.reloadConfig();    // Reload it

        ConfigurationSection itemStatsSection = plugin.getConfig().getConfigurationSection("item_stats");
        if (itemStatsSection == null) {
            plugin.getLogger().warning("No 'item_stats' section found in config.yml!");
            return;
        }

        for (String key : itemStatsSection.getKeys(false)) {
            ConfigurationSection itemConfig = itemStatsSection.getConfigurationSection(key);
            if (itemConfig == null) continue;

            ItemStatDefinition def = new ItemStatDefinition();
            def.key = key;
            def.type = itemConfig.getString("type", "VANILLA").toUpperCase();
            def.applyWhen = ApplyCondition.valueOf(itemConfig.getString("apply_when", "WORN_OR_HELD").toUpperCase());

            if ("VANILLA".equals(def.type)) {
                try {
                    def.material = Material.valueOf(itemConfig.getString("material", "").toUpperCase());
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("Invalid material for " + key + ": " + itemConfig.getString("material"));
                    continue;
                }
                if (itemConfig.contains("custom_model_data")) {
                    def.customModelData = itemConfig.getInt("custom_model_data");
                }
                if (itemConfig.contains("name_contains")) {
                    def.nameContains = itemConfig.getString("name_contains");
                }
                if (itemConfig.contains("lore_contains")) {
                    def.loreContains = itemConfig.getStringList("lore_contains");
                }

            } else if ("ITEMSADDER".equals(def.type)) {
                def.itemsAdderId = itemConfig.getString("id");
                if (def.itemsAdderId == null) {
                    plugin.getLogger().warning("ItemsAdder item " + key + " is missing an 'id'!");
                    continue;
                }
            } else {
                plugin.getLogger().warning("Unknown item type for " + key + ": " + def.type);
                continue;
            }

            ConfigurationSection statsSection = itemConfig.getConfigurationSection("stats");
            if (statsSection != null) {
                for (String statKey : statsSection.getKeys(false)) {
                    try {
                        StatType statType = StatType.valueOf(statKey.toUpperCase());
                        double value = statsSection.getDouble(statKey);
                        def.stats.put(statType, value);
                    } catch (IllegalArgumentException e) {
                        plugin.getLogger().warning("Invalid stat type for " + key + ": " + statKey);
                    }
                }
            }
            itemDefinitions.put(key, def);
            plugin.getLogger().info("Loaded item stat definition: " + key);
        }
    }

    public void startUpdater() {
        if (updateTask != null) {
            updateTask.cancel();
        }
        // Run every 10 ticks (0.5 seconds). Adjust as needed.
        // More frequent means more responsive but more server load.
        updateTask = Bukkit.getScheduler().runTaskTimer(plugin, this::updateAllPlayersStats, 20L, 10L);

        // Initial update for players already online
        Bukkit.getOnlinePlayers().forEach(this::updatePlayerStats);
    }

    public void stopUpdater() {
        if (updateTask != null) {
            updateTask.cancel();
            updateTask = null;
        }
        // Clean up stats for all online players
        Bukkit.getOnlinePlayers().forEach(this::removeAllItemStats);
        playerAppliedItemStats.clear();
    }

    private void updateAllPlayersStats() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            updatePlayerStats(player);
        }
    }

    public void updatePlayerStats(Player player) {
        UUID playerUUID = player.getUniqueId();
        Map<StatType, Double> newTotalStatsFromItems = new HashMap<>();
        PlayerInventory inventory = player.getInventory();

        Set<ItemStack> itemsToCheck = new HashSet<>();
        ItemStack[] armorContents = inventory.getArmorContents();
        ItemStack mainHand = inventory.getItemInMainHand();
        ItemStack offHand = inventory.getItemInOffHand();

        for(ItemStack armor : armorContents) {
            if (armor != null && armor.getType() != Material.AIR) itemsToCheck.add(armor);
        }
        if (mainHand != null && mainHand.getType() != Material.AIR) itemsToCheck.add(mainHand);
        if (offHand != null && offHand.getType() != Material.AIR) itemsToCheck.add(offHand);

        if (Bukkit.getPluginManager().getPlugin("StatsEquipment") != null &&
                Bukkit.getPluginManager().getPlugin("StatsEquipment").isEnabled()) {
            StatsEquipmentAPI statsEquipmentPlugin = getStatsEquipmentAPI();
            Collection<ItemStack> customEquipped = statsEquipmentPlugin.getCustomEquippedItems(player);
            customEquipped.forEach(eqItem -> {
                if (eqItem != null && eqItem.getType() != Material.AIR) itemsToCheck.add(eqItem);
            });
        }

        for (ItemStatDefinition def : itemDefinitions.values()) {
            for (ItemStack item : itemsToCheck) { // Iterate over unique items player has
                if (def.matches(item, player, inventory, armorContents, mainHand, offHand)) {
                    def.stats.forEach((statType, value) ->
                            newTotalStatsFromItems.merge(statType, value, Double::sum)
                    );
                    // Important: If an item can match multiple definitions, this sums them.
                    // If an item should only match ONE definition, you might need to break here
                    // or ensure your definitions are mutually exclusive.
                }
            }
        }

        Map<StatType, Double> previouslyAppliedStats = playerAppliedItemStats.getOrDefault(playerUUID, new HashMap<>());
        Map<StatType, Double> statsToUpdateInAPI = new HashMap<>();

        // Calculate differences: stats to add or remove
        // For stats in newTotal that were not in previous or have different values
        for (Map.Entry<StatType, Double> entry : newTotalStatsFromItems.entrySet()) {
            StatType stat = entry.getKey();
            double newValue = entry.getValue();
            double oldValue = previouslyAppliedStats.getOrDefault(stat, 0.0);
            if (newValue != oldValue) {
                statsToUpdateInAPI.put(stat, newValue - oldValue); // Store the delta
            }
        }

        // For stats in previous that are no longer in newTotal (i.e., item unequipped)
        for (Map.Entry<StatType, Double> entry : previouslyAppliedStats.entrySet()) {
            StatType stat = entry.getKey();
            if (!newTotalStatsFromItems.containsKey(stat)) {
                statsToUpdateInAPI.put(stat, -entry.getValue()); // Store negative of old value to remove it
            }
        }

        // Apply changes through your Stat API
        // This assumes your API's "giveStat" can handle negative values to remove stats.
        // If not, you'll need a "removeStat" or "setStat" method.
        for (Map.Entry<StatType, Double> entry : statsToUpdateInAPI.entrySet()) {
            StatType stat = entry.getKey();
            double delta = entry.getValue();

            if (delta != 0) {
                // Example: statAPI.giveStat(player, stat.name(), delta);
                // Or, if your API uses enums: statAPI.giveStat(player, stat, delta);
                // Or, if it's attribute based:
                // statAPI.modifyPlayerAttribute(player, getAttributeForStat(stat), "item_bonus_" + stat.name(), delta, getOperationForStat(stat));

                //plugin.getLogger().info("Player " + player.getName() + ": " + (delta > 0 ? "Gained" : "Lost") + " " + Math.abs(delta) + " " + stat.name() + " from items.");
                statAPI.addStat(player, stat, delta);
                // **INTEGRATION POINT:** Call your existing stat API here.
                // For example:
                // yourStatAPI.giveStat(player, stat.name(), delta);
                // If your stat API has specific methods for different stats:
                // switch (stat) {
                //    case SPEED: yourStatAPI.addSpeed(player, delta); break;
                //    case MAX_HEALTH: yourStatAPI.addMaxHealth(player, delta); break;
                //    ...
                // }
            }
        }

        // Update the record of what stats items are currently providing
        if (newTotalStatsFromItems.isEmpty()) {
            playerAppliedItemStats.remove(playerUUID);
        } else {
            playerAppliedItemStats.put(playerUUID, newTotalStatsFromItems);
        }
    }

    // Call this on player quit or plugin disable for a player
    public void removeAllItemStats(Player player) {
        UUID playerUUID = player.getUniqueId();
        Map<StatType, Double> previouslyAppliedStats = playerAppliedItemStats.remove(playerUUID);

        if (previouslyAppliedStats != null) {
            for (Map.Entry<StatType, Double> entry : previouslyAppliedStats.entrySet()) {
                StatType stat = entry.getKey();
                double valueToRemove = entry.getValue();
                if (valueToRemove != 0) { // Only act if there was a value
                    // **INTEGRATION POINT:** Call your existing stat API to remove the stat
                    // Example: yourStatAPI.giveStat(player, stat.name(), -valueToRemove);
                    statAPI.addStat(player, stat, -valueToRemove);
                    plugin.getLogger().info("Player " + player.getName() + ": Removed " + valueToRemove + " " + stat.name() + " (cleanup).");
                }
            }
        }
    }

    /**
     * Gets the configuration key of a StatsItems definition that matches the given ItemStack.
     * @param item The ItemStack to check.
     * @return The String key of the item definition (e.g., "my_custom_sword"), or null if no match.
     */
    public String getItemDefinitionKey(ItemStack item) {
        if (item == null || item.getType().isAir()) {
            return null;
        }
        for (Map.Entry<String, ItemStatDefinition> entry : itemDefinitions.entrySet()) {
            ItemStatDefinition def = entry.getValue();
            // We need a way to check if 'item' *is* this 'def' without considering 'applyWhen'
            // Let's make a helper for this in ItemStatDefinition or reuse parts of matches()

            // Simplified check (adapt from your full def.matches logic, but ignore applyWhen part)
            boolean isTheDefinedItem = false;
            if ("ITEMSADDER".equalsIgnoreCase(def.type)) {
                if (Bukkit.getPluginManager().getPlugin("ItemsAdder") != null && Bukkit.getPluginManager().getPlugin("ItemsAdder").isEnabled()) {
                    CustomStack customStack = CustomStack.byItemStack(item);
                    if (customStack != null && customStack.getNamespacedID().equals(def.itemsAdderId)) {
                        isTheDefinedItem = true;
                    }
                }
            } else if ("VANILLA".equalsIgnoreCase(def.type)) {
                if (item.getType() == def.material) {
                    boolean cmdMatch = (def.customModelData == null) || (item.hasItemMeta() && item.getItemMeta().hasCustomModelData() && item.getItemMeta().getCustomModelData() == def.customModelData);
                    boolean nameMatch = (def.nameContains == null) || (item.hasItemMeta() && item.getItemMeta().hasDisplayName() && item.getItemMeta().getDisplayName().contains(def.nameContains));
                    // Add lore check if you use it for definition matching
                    if (cmdMatch && nameMatch /* && loreMatch */) {
                        isTheDefinedItem = true;
                    }
                }
            }

            if (isTheDefinedItem) {
                return def.key; // Return the configuration key (e.g., "swift_boots")
            }
        }
        return null; // No definition matched this item
    }

    private static StatsEquipmentAPI getStatsEquipmentAPI() {
        org.bukkit.plugin.RegisteredServiceProvider<StatsEquipmentAPI> provider = Bukkit.getServicesManager().getRegistration(StatsEquipmentAPI.class);
        if (provider != null) {
            return provider.getProvider();
        }
        return null; // Or a dummy implementation that does nothing / returns false
    }

    // Helper method if your stat API works with Vanilla Attributes
    /*
    private org.bukkit.attribute.Attribute getAttributeForStat(StatType stat) {
        switch (stat) {
            case SPEED: return org.bukkit.attribute.Attribute.GENERIC_MOVEMENT_SPEED;
            case MAX_HEALTH: return org.bukkit.attribute.Attribute.GENERIC_MAX_HEALTH;
            case STRENGTH: // Assuming STRENGTH maps to ATTACK_DAMAGE for vanilla
                return org.bukkit.attribute.Attribute.GENERIC_ATTACK_DAMAGE;
            case GENERIC_ARMOR: return org.bukkit.attribute.Attribute.GENERIC_ARMOR;
            // ... map others
            default: return null;
        }
    }

    private org.bukkit.attribute.AttributeModifier.Operation getOperationForStat(StatType stat) {
        // Most stats are additive. Speed is often multiplicative or additive percentage.
        // Adjust this based on how your stats work.
        switch (stat) {
            case SPEED: // Vanilla speed is base + (base * sum_of_MULTIPLY_SCALAR_1) + sum_of_ADD_NUMBER
                        // If your '0.10' means +10% of base, use MULTIPLY_SCALAR_1
                return org.bukkit.attribute.AttributeModifier.Operation.ADD_SCALAR; // Or ADD_NUMBER if it's a flat value
            default:
                return org.bukkit.attribute.AttributeModifier.Operation.ADD_NUMBER;
        }
    }
    */
}
